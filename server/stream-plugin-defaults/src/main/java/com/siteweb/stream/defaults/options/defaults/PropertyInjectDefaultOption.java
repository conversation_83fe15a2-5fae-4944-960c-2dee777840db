package com.siteweb.stream.defaults.options.defaults;

import com.siteweb.tcs.common.expression.enums.MemberAccessScope;
import com.siteweb.tcs.common.expression.enums.ValueScope;
import com.siteweb.tcs.common.expression.AssignExpression;
import com.siteweb.tcs.common.expression.MemberAccessExpression;
import com.siteweb.tcs.common.expression.ValueExpression;
import com.siteweb.stream.common.stream.AbstractShapeDefaultOption;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.defaults.options.PropertyInjectOption;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2025-02-28)
 **/
public class PropertyInjectDefaultOption implements AbstractShapeDefaultOption {
    @Override
    public StreamShapeOption option() {
        var option = new PropertyInjectOption();
        List<AssignExpression> expressionList = new ArrayList<>();
        expressionList.add(new AssignExpression(new MemberAccessExpression(MemberAccessScope.MESSAGE, "payload"), new ValueExpression(ValueScope.OBJECT, "")));
        expressionList.add(new AssignExpression(new MemberAccessExpression(MemberAccessScope.MESSAGE, "payload.flow"), new ValueExpression(ValueScope.FLOAT, "3.14159")));
        expressionList.add(new AssignExpression(new MemberAccessExpression(MemberAccessScope.MESSAGE, "payload.json"), new ValueExpression(ValueScope.JSON, "{ \"a\": 123, \"b\": true}")));
        option.setAssignExpressions(expressionList);
        return option;
    }
}
