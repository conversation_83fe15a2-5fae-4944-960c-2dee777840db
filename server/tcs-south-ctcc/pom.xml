<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.siteweb</groupId>
        <artifactId>thing-connect-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>tcs-south-ctcc</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
        <plugin.id>south-ctcc-plugin</plugin.id>
        <plugin.name>中国电信南向接入插件</plugin.name>
        <plugin.class>com.siteweb.tcs.south.ctcc.SouthCtccPlugin</plugin.class>
        <plugin.version>1.0.0</plugin.version>
        <plugin.provider>Siteweb</plugin.provider>
        <plugin.applicationName>中国电信南向接入插件</plugin.applicationName>
        <plugin.dependencies/>
        <webroot.resource.path>META-INF/com-siteweb-webroot/plugins/${plugin.id}</webroot.resource.path>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>stream-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-hub</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-middleware-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-plugin-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson-dataformat-xml.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 写入插件信息 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Plugin-Id>${plugin.id}</Plugin-Id>
                            <Plugin-Name>${plugin.name}</Plugin-Name>
                            <Plugin-Version>${plugin.version}</Plugin-Version>
                            <Plugin-Provider>${plugin.provider}</Plugin-Provider>
                            <Plugin-Class>${plugin.class}</Plugin-Class>
                            <Plugin-Dependencies>${plugin.dependencies}</Plugin-Dependencies>
                            <Plugin-BuildTime>${maven.build.timestamp}</Plugin-BuildTime>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>1.15.0</version>
                <configuration>
                    <!-- 指定构建目录 -->
                    <workingDirectory>src/main/web</workingDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>install-node-and-npm</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>${node.version}</nodeVersion>
                            <npmVersion>${npm.version}</npmVersion>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm-install-pnpm</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <arguments>install -g pnpm --registry=https://registry.npmmirror.com</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>pnpm-install</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <arguments>exec pnpm install --registry=https://registry.npmmirror.com</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>pnpm-build</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>exec pnpm run build</arguments>
                            <environmentVariables>
                                <PROJECT_VERSION>${project.version}</PROJECT_VERSION>
                            </environmentVariables>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <!-- 插件打包 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <descriptors>
                        <descriptor>../plugin.release.xml</descriptor>
                    </descriptors>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>target/dist</directory>
                <targetPath>${webroot.resource.path}</targetPath>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

</project>


