package com.siteweb.tcs.south.ctcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.tcs.south.ctcc.protocol.PK_TypeName;
import lombok.extern.slf4j.Slf4j;

/**
 * CTCC响应消息基类的具体实现
 * 提供XML序列化功能
 * 
 * <AUTHOR> (2025-05-09)
 */
@Slf4j
public abstract class CtccBaseResponseMessage extends CtccResponseMessage {
    
    private static final XmlMapper xmlMapper = new XmlMapper();
    
    @JsonProperty("PK_Type")
    @JacksonXmlProperty(localName = "PK_Type")
    private final PkType pkType;
    
    protected CtccBaseResponseMessage(PK_TypeName typeName) {
        this.pkType = new PkType(typeName);
    }
    
    public PkType getPkType() {
        return pkType;
    }
    
    @Override
    protected String toXmlPayload() {
        try {
            return xmlMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize CTCC response message to XML", e);
            return "";
        }
    }
    
    /**
     * 报文类型信息
     */
    public static class PkType {
        @JsonProperty("Name")
        @JacksonXmlProperty(localName = "Name")
        private String name;
        
        @JsonProperty("Code")
        @JacksonXmlProperty(localName = "Code")
        private int code;
        
        public PkType() {}
        
        public PkType(PK_TypeName typeName) {
            this.name = typeName.name();
            this.code = typeName.getCode();
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public int getCode() {
            return code;
        }
        
        public void setCode(int code) {
            this.code = code;
        }
    }
}