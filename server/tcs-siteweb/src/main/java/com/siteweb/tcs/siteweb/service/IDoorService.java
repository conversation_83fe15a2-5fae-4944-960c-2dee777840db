package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.Door;

import java.util.List;

/**
 * Door Service Interface
 */
public interface IDoorService extends IService<Door> {
    /**
     * 根据设备id创建门
     *
     * @param equipmentId 设备主键id
     */
    void createDoor(Integer equipmentId);

    /**
     * 根据门信息创建门
     *
     * @param door 门信息
     */
    void createDoor(Door door);

    /**
     * 通过设备主键id删除门
     *
     * @param equipmentId   设备主键id
     * @param equipmentName 设备名称
     */
    void deleteByEquipmentId(Integer equipmentId, String equipmentName);

    /**
     * 删除门禁设备
     * 
     * @param equipmentId 设备ID
     * @param doorNo 门编号
     * @return 是否删除成功
     */
    boolean deleteDoor(Integer equipmentId, Integer doorNo);

    /**
     * 更新门禁设备
     * 
     * @param door 门信息
     * @return 更新后的门信息
     */
    Door updateDoor(Door door);

    /**
     * 根据设备id查询门设备
     * 
     * @param equipmentId 设备ID
     * @return 门设备列表
     */
    List<Door> getDoorByEquipmentId(Integer equipmentId);
}
