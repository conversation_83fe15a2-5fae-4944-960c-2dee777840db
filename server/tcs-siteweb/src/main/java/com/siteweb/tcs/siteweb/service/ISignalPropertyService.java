package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.SignalProperty;

import java.util.List;

/**
 * Signal Property Service Interface
 */
public interface ISignalPropertyService extends IService<SignalProperty> {

    /**
     * 批量创建信号属性
     * @param signalPropertyList 信号属性列表
     */
    void batchCreateSignalProperty(List<SignalProperty> signalPropertyList);

    /**
     * 创建信号属性
     * @param signalProperty 信号属性
     */
    void createSignalProperty(SignalProperty signalProperty);

    /**
     * 根据设备模板ID和信号ID查找信号属性
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     * @return 信号属性列表
     */
    List<SignalProperty> findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId);

    /**
     * 根据设备模板ID删除信号属性
     * @param equipmentTemplateId 设备模板ID
     */
    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 复制信号属性
     * @param originEquipmentTemplateId 源设备模板ID
     * @param destEquipmentTemplateId 目标设备模板ID
     */
    void copySignalProperty(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    /**
     * 更新信号属性
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     * @param signalPropertyList 信号属性列表
     */
    void updateSignalProperty(Integer equipmentTemplateId, Integer signalId, List<SignalProperty> signalPropertyList);

    /**
     * 批量保存联通信号属性
     *
     * @return 是否保存成功
     */
    boolean batchsaveLianTongSignalPropertys();

    /**
     * 根据设备模板ID和信号ID删除信号属性
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     */
    void deleteByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId);
}
