package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 设备基础类型实体类
 */
@Data
@TableName("tbl_equipmentbasetype")
public class EquipmentBaseType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基础设备ID
     */
    @TableId(value = "BaseEquipmentId", type = IdType.INPUT)
    private Integer baseEquipmentId;

    /**
     * 基础设备名称
     */
    @TableField("BaseEquipmentName")
    private String baseEquipmentName;

    /**
     * 设备类型ID
     */
    @TableField("EquipmentTypeId")
    private Integer equipmentTypeId;

    /**
     * 设备子类型ID
     */
    @TableField("EquipmentSubTypeId")
    private Integer equipmentSubTypeId;

    /**
     * 描述
     */
    @TableField("Description")
    private String description;

    /**
     * 扩展字段
     */
    @TableField("ExtField")
    private String extField;
}