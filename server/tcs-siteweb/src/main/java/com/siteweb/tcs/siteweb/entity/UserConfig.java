package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 用户配置实体类
 * User configuration table
 */
@Data
@TableName("userconfig")
public class UserConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户配置ID，主键，自增
     */
    @TableId(value = "userconfigid", type = IdType.AUTO)
    private Integer userConfigId;

    /**
     * 用户ID
     */
    @TableField("userid")
    private Integer userId;

    /**
     * 配置类型 1TTS
     */
    @TableField("configtype")
    private Integer configType;

    /**
     * 用户信息配置键
     */
    @TableField("configkey")
    private String configKey;

    /**
     * 用户信息配置值
     */
    @TableField("configvalue")
    private String configValue;
}