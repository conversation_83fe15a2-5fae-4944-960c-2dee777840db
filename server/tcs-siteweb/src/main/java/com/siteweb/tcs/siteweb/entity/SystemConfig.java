package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 系统配置实体类
 * System configuration table
 */
@Data
@TableName("systemconfig")
public class SystemConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID，主键，自增
     */
    @TableId(value = "systemconfigid", type = IdType.AUTO)
    private Integer systemConfigId;

    /**
     * 键名
     */
    @TableField("systemconfigkey")
    private String systemConfigKey;

    /**
     * 对应键值
     */
    @TableField("systemconfigvalue")
    private String systemConfigValue;

    /**
     * 键值对业务类型
     */
    @TableField("systemconfigtype")
    private Integer systemConfigType;

    /**
     * 描述信息
     */
    @TableField("description")
    private String description;
}