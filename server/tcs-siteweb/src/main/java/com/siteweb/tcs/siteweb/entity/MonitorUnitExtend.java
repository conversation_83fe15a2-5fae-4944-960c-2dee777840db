package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 监控单元扩展信息实体
 * Description: 监控单元扩展信息，用于存储账号密码等扩展字段
 * Author: <EMAIL>
 * Creation Date: 2024/6/5
 */
@TableName("tsl_monitorunitextend")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MonitorUnitExtend {

    @TableId(value = "MonitorUnitId", type = IdType.INPUT)
    private Integer monitorUnitId;

    private String description;

    /**
     * 用来存储监控单元的账号密码端口协议,密码使用aes对称加密
     * 格式: ssh:root:123456:22
     */
    private String extendFiled1;

    private String extendFiled2;

    private String extendFiled3;

    private String extendFiled4;

    private String extendFiled5;
}
