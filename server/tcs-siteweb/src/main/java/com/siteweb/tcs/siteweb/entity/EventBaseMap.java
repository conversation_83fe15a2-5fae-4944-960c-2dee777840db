package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Event base mapping entity
 */
@Data
@TableName("tbl_eventbasemap")
public class EventBaseMap implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StandardDicId")
    private Integer standardDicId;

    @TableField("StandardType")
    private Integer standardType;

    @TableField("StationBaseType")
    private Integer stationBaseType;

    @TableId(value = "BaseTypeId")
    private BigDecimal baseTypeId;
}
