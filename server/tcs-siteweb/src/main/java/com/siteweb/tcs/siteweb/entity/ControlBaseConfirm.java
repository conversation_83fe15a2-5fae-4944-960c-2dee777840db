package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Control base confirmation entity
 */
@Data
@TableName("tbl_controlbaseconfirm")
public class ControlBaseConfirm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("ControlId")
    private Integer controlId;

    @TableField("ParameterValue")
    private Integer parameterValue;

    @TableField("SubState")
    private String subState;
}
