package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.dto.EventConfigItem;
import com.siteweb.tcs.siteweb.dto.EventExpressionDTO;
import com.siteweb.tcs.siteweb.dto.excel.EventExcel;
import com.siteweb.tcs.siteweb.entity.Event;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Event Mapper
 */
@Mapper
public interface EventMapper extends BaseMapper<Event> {

    /**
     * 根据设备模板ID查询事件配置项
     * @param equipmentTemplateId 设备模板ID
     * @return 事件配置项列表
     */
    List<EventConfigItem> findEventItemByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 根据设备模板ID和事件ID查询事件配置项
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 事件配置项
     */
    EventConfigItem findByEquipmentTemplateIdAndEventId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("eventId") Integer eventId);

    /**
     * 根据设备模板ID查询最大事件ID
     * @param equipmentTemplateId 设备模板ID
     * @return 最大事件ID
     */
    Integer findMaxEventIdByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 根据设备模板ID查询事件配置项
     * @param equipmentTemplateId 设备模板ID
     * @return 事件配置项列表
     */
    List<EventConfigItem> findEventItemByEquipmentTemplateIdOMC(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 根据设备模板ID和事件ID查询事件配置项
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 事件配置项
     */
    EventConfigItem findByEquipmentTemplateIdAndEventIdOMC(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("eventId") Integer eventId);


    /**
     * 删除事件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 删除结果
     */
    int deleteEvent(@Param("equipmentTemplateId") int equipmentTemplateId, @Param("eventId") int eventId);

    /**
     * 批量更新事件
     * @param eventList 事件列表
     */
    void batchUpdate(@Param("eventList") List<Event> eventList);
    
    /**
     * 根据设备模板ID查询最大显示索引
     * @param equipmentTemplateId 设备模板ID
     * @return 最大显示索引
     */
    Integer findMaxDisplayIndexByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 更新工作站事件名称
     * @param prefix 前缀
     * @param equipmentTemplateId 设备模板ID
     */
    void updateWorkStationEventName(@Param("prefix") String prefix, @Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 更新自诊断事件ID、启动表达式和信号ID
     * @param centerId 中心ID
     * @param equipmentTemplateId 设备模板ID
     */
    void updateEventIdAndStartExpressionAndSignalId(@Param("centerId") Integer centerId, @Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 查找设备模板下最大的事件配置项
     * @param equipmentTemplateId 设备模板ID
     * @return 事件配置项
     */
    EventConfigItem findMaxEventByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    EventConfigItem findMaxEventByEquipmentTemplateIdOMC(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * Find cmcc type IDs not in event cmcc dictionary for equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of cmcc type IDs
     */
    List<Long> findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<EventExcel> findExcelDtoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 根据设备模板ID查找事件表达式
     * @param equipmentTemplateId 设备模板ID
     * @return 事件表达式列表
     */
    List<EventExpressionDTO> findEventExpression(@Param("equipmentTemplateId") Integer equipmentTemplateId);

}
