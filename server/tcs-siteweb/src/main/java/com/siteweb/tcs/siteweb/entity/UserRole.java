package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * User role entity
 */
@Data
@TableName("tbl_userrole")
public class UserRole implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "RoleId")
    private Integer roleId;

    @TableField("RoleName")
    private String roleName;

    @TableField("Description")
    private String description;
}
