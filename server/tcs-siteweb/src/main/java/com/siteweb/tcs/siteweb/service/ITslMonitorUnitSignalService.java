package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.TslMonitorUnitSignal;

import java.util.List;
import java.util.Map;

/**
 * 监控单元信号服务接口
 */
public interface ITslMonitorUnitSignalService extends IService<TslMonitorUnitSignal> {

    /**
     * 根据站点ID删除监控单元信号
     *
     * @param stationId 站点ID
     * @return 是否删除成功
     */
    boolean deleteByStationId(Integer stationId);

    // ==================== 设备管理相关方法 ====================

    /**
     * 根据设备ID和信号ID查询监控单元信号
     *
     * @param equipmentId 设备ID
     * @param signalId 信号ID
     * @return 监控单元信号列表
     */
    List<TslMonitorUnitSignal> findByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId);

    /**
     * 根据条件查询监控单元信号
     *
     * @param condition 查询条件
     * @return 监控单元信号列表
     */
    List<TslMonitorUnitSignal> findByCondition(Map<String, Object> condition);

    /**
     * 创建或更新监控单元信号
     *
     * @param signal 监控单元信号
     * @return 是否成功
     */
    boolean createOrUpdate(TslMonitorUnitSignal signal);

    /**
     * 删除监控单元信号
     *
     * @param equipmentId 设备ID
     * @param signalId 信号ID
     * @return 是否成功
     */
    boolean deleteByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId);

    /**
     * 获取监控单元信号列表
     *
     * @return 监控单元信号列表
     */
    List<TslMonitorUnitSignal> getMonitorUnitSignals();
}