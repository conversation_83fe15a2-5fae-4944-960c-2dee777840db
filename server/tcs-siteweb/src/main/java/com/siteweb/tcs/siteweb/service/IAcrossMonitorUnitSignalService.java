package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.AcrossMonitorUnitSignal;

import java.util.List;
import java.util.Map;

/**
 * 跨站监控单元信号 Service Interface
 */
public interface IAcrossMonitorUnitSignalService extends IService<AcrossMonitorUnitSignal> {

    /**
     * 根据条件查询跨站信号
     *
     * @param condition 查询条件
     * @return 跨站信号列表
     */
    List<AcrossMonitorUnitSignal> findByCondition(Map<String, Object> condition);

    /**
     * 根据设备ID和信号ID查询跨站信号
     *
     * @param equipmentId 设备ID
     * @param signalId 信号ID
     * @return 跨站信号列表
     */
    List<AcrossMonitorUnitSignal> findByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId);

    /**
     * 创建跨站信号
     *
     * @param signal 跨站信号
     * @return 是否成功
     */
    boolean createAcrossMonitorUnitSignal(AcrossMonitorUnitSignal signal);
}
