package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Door card entity
 */
@Data
@TableName("tbl_doorcard")
@ChangeSource(channel = "tcs", product = "siteweb", source = "doorcard")
public class DoorCard implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "CardId")
    private Integer cardId;

    @TableField("TimeGroupId")
    private Integer timeGroupId;

    @TableField("DoorId")
    private Integer doorId;

    @TableField("StartTime")
    private LocalDateTime startTime;

    @TableField("EndTime")
    private LocalDateTime endTime;

    @TableField("Password")
    private String password;

    @TableField("timegrouptype")
    private Integer timeGroupType;
}
