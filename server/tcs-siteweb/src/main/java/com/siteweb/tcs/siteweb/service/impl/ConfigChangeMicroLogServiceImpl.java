package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.ConfigChangeMicroLog;
import com.siteweb.tcs.siteweb.mapper.ConfigChangeMicroLogMapper;
import com.siteweb.tcs.siteweb.service.IConfigChangeMicroLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 配置变更微日志服务实现类
 */
@Slf4j
@Service
public class ConfigChangeMicroLogServiceImpl extends ServiceImpl<ConfigChangeMicroLogMapper, ConfigChangeMicroLog> implements IConfigChangeMicroLogService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean configChangeLog(String objectId, Integer configId, Integer changeOperator) {
        if (objectId == null || configId == null || changeOperator == null) {
            log.warn("Cannot record config change log: parameters are null");
            return false;
        }

        try {
            ConfigChangeMicroLog log = new ConfigChangeMicroLog();
            log.setObjectId(objectId);
            log.setConfigId(configId);
            log.setEditType(Integer.valueOf(changeOperator));
            log.setUpdateTime(LocalDateTime.now());

            return save(log);
        } catch (Exception e) {
            log.error("Error recording config change log: {}", e.getMessage(), e);
            return false;
        }
    }
}
