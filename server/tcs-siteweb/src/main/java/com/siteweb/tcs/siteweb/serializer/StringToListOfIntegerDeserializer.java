package com.siteweb.tcs.siteweb.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.siteweb.tcs.siteweb.util.StrSplitUtil;

import java.io.IOException;
import java.util.List;

/**
 * 字符串转整数列表反序列化器
 */
public class StringToListOfIntegerDeserializer extends JsonDeserializer<List<Integer>> {

    @Override
    public List<Integer> deserialize(JsonParser p, DeserializationContext deserializationContext) throws IOException {
        String value = p.getText();
        return StrSplitUtil.splitToIntList(value);
    }
}
