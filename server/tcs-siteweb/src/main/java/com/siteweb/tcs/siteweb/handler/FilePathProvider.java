package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.common.util.PathUtil;
import com.siteweb.tcs.siteweb.dto.DistributeFilePath;

import java.util.List;

/**
 * 文件路径提供者接口
 * 用于提供文件系统的根路径配置
 *
 * 这个接口允许不同的实现提供不同的根路径策略：
 * - 默认实现：使用FileServerProperty的配置
 * - 插件实现：使用插件特定的路径配置
 */
public interface FilePathProvider {

    /**
     * 获取文件系统的根路径
     *
     * @return 根路径字符串
     */
    String getRootPath();

    /**
     * 获取当前插件的工作区域路径
     *
     * @return 工作区域路径，格式：plugins/{pluginId}/workspace
     */
    String getWorkspacePath();

    /**
     * 获取协议文件存储路径
     *
     * @return 协议文件路径，格式：plugins/{pluginId}/workspace/protocol
     */
    String getProtocolPath();

    /**
     * 获取指定插件的路径
     * 用于跨插件访问，如访问CMB插件的字典文件
     *
     * @param pluginId 插件ID
     * @return 指定插件的根路径
     */
    String getPluginPath(String pluginId);

    /**
     * 获取指定插件的工作区域路径
     *
     * @param pluginId 插件ID
     * @return 指定插件的工作区域路径
     */
    String getPluginWorkspacePath(String pluginId);

    /**
     * 获取完整的文件路径
     * 
     * @param relativePath 相对路径
     * @return 完整路径
     */
    default String getFullPath(String relativePath) {
        if (relativePath == null || relativePath.isEmpty()) {
            return getRootPath();
        }
        
        // 确保路径分隔符正确
        String cleanRelativePath = relativePath.replace("\\", "/");
        if (cleanRelativePath.startsWith("/")) {
            cleanRelativePath = cleanRelativePath.substring(1);
        }
        
        return getRootPath() + "/" + cleanRelativePath;
    }
    
    /**
     * 获取完整的文件路径（包含文件名）
     * 
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 完整路径
     */
    default String getFullPath(String filePath, String fileName) {
        return PathUtil.pathJoin(filePath,fileName);
    }

    List<DistributeFilePath> getDistributeFilePaths();

    void addDistributeFilePath(DistributeFilePath distributeFilePath);

    void clearDistributeFilePath();

    void setDistributeFilePaths(List<DistributeFilePath> distributeFilePaths);

}
