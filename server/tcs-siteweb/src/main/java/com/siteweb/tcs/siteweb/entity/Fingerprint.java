package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Fingerprint entity
 */
@Data
@TableName("tbl_fingerprint")
public class Fingerprint implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "FingerPrintId")
    private Integer fingerPrintId;

    @TableField("FingerPrintNO")
    private Integer fingerPrintNo;

    @TableField("FingerPrintData")
    private byte[] fingerPrintData;
}
