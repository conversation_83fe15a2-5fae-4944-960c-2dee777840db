package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Standard dictionary event entity
 */
@Data
@TableName("tbl_standarddicevent")
public class StandardDicEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "StandardDicId")
    private Integer standardDicId;

    @TableField("StandardType")
    private Integer standardType;

    @TableField("EquipmentLogicClassId")
    private Integer equipmentLogicClassId;

    @TableField("EquipmentLogicClass")
    private String equipmentLogicClass;

    @TableField("EventLogicClassId")
    private Integer eventLogicClassId;

    @TableField("EventLogicClass")
    private String eventLogicClass;

    @TableField("EventClass")
    private String eventClass;

    @TableField("EventStandardName")
    private String eventStandardName;

    @TableField("NetManageId")
    private String netManageId;

    @TableField("EventSeverity")
    private Integer eventSeverity;

    @TableField("CompareValue")
    private String compareValue;

    @TableField("StartDelay")
    private String startDelay;

    @TableField("Meanings")
    private String meanings;

    @TableField("EquipmentAffect")
    private String equipmentAffect;

    @TableField("BusinessAffect")
    private String businessAffect;

    @TableField("StationCategory")
    private Integer stationCategory;

    @TableField("ModifyType")
    private Integer modifyType;

    @TableField("Description")
    private String description;

    @TableField("ExtendFiled1")
    private String extendFiled1;

    @TableField("ExtendFiled2")
    private String extendFiled2;

    @TableField("ExtendFiled3")
    private String extendFiled3;
}
