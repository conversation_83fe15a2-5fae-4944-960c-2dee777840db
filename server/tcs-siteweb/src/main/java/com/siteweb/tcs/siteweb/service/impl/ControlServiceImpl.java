package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.dto.ControlConfigItem;
import com.siteweb.tcs.siteweb.dto.ControlConfigPointDTO;
import com.siteweb.tcs.siteweb.dto.CommandFieldCopyDTO;
import com.siteweb.tcs.siteweb.dto.SimilarDataDTO;
import com.siteweb.tcs.siteweb.dto.excel.ControlExcel;
import com.siteweb.tcs.siteweb.entity.Control;
import com.siteweb.tcs.siteweb.entity.ControlMeanings;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.exception.BusinessException;
import com.siteweb.tcs.siteweb.mapper.ControlMapper;
import com.siteweb.tcs.siteweb.mapper.EquipmentTemplateMapper;
import com.siteweb.tcs.siteweb.service.IControlMeaningsService;
import com.siteweb.tcs.siteweb.service.IControlService;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.vo.ControlVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import cn.hutool.core.collection.CollUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Control Service Implementation
 */
@Slf4j
@Service
public class ControlServiceImpl extends IsolatedServiceImpl<ControlMapper, Control> implements IControlService {

    private static final int GENERATE_CONTROL_ID_FLAG = -1;

    @Autowired
    private ControlMapper controlMapper;

    @Autowired
    private EquipmentTemplateMapper equipmentTemplateMapper;

    @Autowired
    private IControlMeaningsService controlMeaningsService;

    @Autowired
    private I18n i18n;

    @Autowired
    private IOperationDetailService operationDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createControl(ControlConfigItem controlConfigItem) {
        Control control = new Control();
        BeanUtils.copyProperties(controlConfigItem, control, "id", "controlId");

        if (Objects.isNull(control.getControlId()) || control.getControlId() == GENERATE_CONTROL_ID_FLAG) {
            Integer controlId = findMaxControlIdByEquipmentTemplateId(control.getEquipmentTemplateId());
            control.setControlId(controlId);
        }
        if (Objects.isNull(control.getDisplayIndex())) {
            control.setDisplayIndex(findMaxDisplayIndexByEquipmentTemplateId(control.getEquipmentTemplateId()) + 1);
        }

        controlMapper.insertControl(control); // Use custom insert due to potential keyword issues

        if (!CollectionUtils.isEmpty(controlConfigItem.getControlMeaningsList())) {
            controlConfigItem.getControlMeaningsList().forEach(e -> {
                e.setEquipmentTemplateId(control.getEquipmentTemplateId());
                e.setControlId(control.getControlId());
            });
            controlMeaningsService.saveBatch(controlConfigItem.getControlMeaningsList());
        }
        controlConfigItem.setId(control.getId());
        controlConfigItem.setControlId(control.getControlId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateControlByControl(ControlConfigItem controlConfigItem) {
        Control control = new Control();
        BeanUtils.copyProperties(controlConfigItem, control);
        control.setId(null); // Ensure ID is not used for update by equipmentTemplateId and controlId

        controlMapper.updateControl(control); // Use custom update due to potential keyword issues

        // 记录操作日志
        if (operationDetailService != null) {
            try {
                operationDetailService.recordOperationLog(
                        String.valueOf(controlConfigItem.getEquipmentTemplateId()),
                        OperationObjectTypeEnum.CONTROL,
                        String.format("control.%d", controlConfigItem.getControlId()),
                        i18n.T("update"),
                        "",
                        controlConfigItem.getDescription() != null ? controlConfigItem.getDescription() : ""
                );
            } catch (Exception e) {
                log.warn("Failed to record operation log for control update", e);
            }
        }

        if (controlConfigItem.getControlMeaningsList() != null) {
            controlMeaningsService.batchUpdateControlMeanings(controlConfigItem.getControlMeaningsList());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteControl(int equipmentTemplateId, int controlId) {
        controlMapper.deleteControl(equipmentTemplateId, controlId);
        controlMeaningsService.deleteByEquipmentTemplateIdAndControlId(equipmentTemplateId, controlId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteControl(int equipmentTemplateId, List<Integer> controlIds) {
        if (CollectionUtils.isEmpty(controlIds)) {
            return;
        }
        for (Integer controlId : controlIds) {
            controlMapper.deleteControl(equipmentTemplateId, controlId);
        }
    }

    @Override
    public List<ControlConfigItem> findItemByEquipmentTemplateId(Integer equipmentTemplateId) {
        return Optional.ofNullable(controlMapper.findControlItemByEquipmentTemplateId(equipmentTemplateId)).orElseGet(List::of)
                .stream()
                .sorted(Comparator.comparingInt(ControlConfigItem::getDisplayIndex))
                .toList();
    }

    @Override
    public ControlConfigItem getControlInfo(Integer equipmentTemplateId, Integer controlId) {
        return controlMapper.findByEquipmentTemplateIdAndControlId(equipmentTemplateId, controlId);
    }

    @Override
    public Integer findMaxControlIdByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxId = controlMapper.findMaxControlIdByEquipmentTemplateId(equipmentTemplateId);
        return maxId == null ? 1 : maxId + 1;
    }

    @Override
    public int findMaxDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxIndex = controlMapper.findMaxDisplayIndexByEquipmentTemplateId(equipmentTemplateId);
        return maxIndex == null ? 0 : maxIndex; // Default to 0 if no records exist, so next is 1
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveLianTongControls() {
        try {
            List<Control> controls = new ArrayList<>();

            // 创建联通控制
            // 这里需要实现批量保存联通控制的逻辑
            // 由于没有具体的数据，这里只提供一个空实现
            log.warn("batchsaveLianTongControls method is not fully implemented");
            return true;
        } catch (Exception e) {
            log.error("Failed to batch save LianTong controls", e);
            return false;
        }
    }

    @Override
    public List<Long> findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(Integer equipmentTemplateId) {
        return controlMapper.findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(equipmentTemplateId);
    }

    @Override
    public int deleteControl(Integer equipmentTemplateId, Integer controlId) {
        return controlMapper.deleteControl(equipmentTemplateId, controlId);
    }

    @Override
    public Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds) {
        // TODO: 实现控制标准化应用逻辑
        // 这里应该根据标准ID和设备模板ID列表来应用控制标准化
        // 暂时返回0，实际实现需要根据业务逻辑来完成
        return 0L;
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    public List<ControlConfigPointDTO> findControlPoints(Integer equipmentTemplateId) {
        try {
            EquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
            if (Objects.isNull(equipmentTemplate)) {
                throw new BusinessException("设备模板不存在");
            }
            List<ControlConfigItem> controlConfigItemList = controlMapper.findControlItemByEquipmentTemplateId(equipmentTemplateId);
            if (CollUtil.isEmpty(controlConfigItemList)) {
                return List.of();
            }

            // 简化实现，暂时不依赖StandardPointService
            return controlConfigItemList.stream().map(s -> {
                ControlConfigPointDTO controlConfigPointDTO = BeanUtil.copyProperties(s, ControlConfigPointDTO.class);
                // 暂时不设置mappingPointName，避免依赖StandardPointService
                return controlConfigPointDTO;
            }).sorted(Comparator.comparing(ControlConfigPointDTO::getDisplayIndex, Comparator.nullsLast(Comparator.naturalOrder()))).toList();
        } catch (Exception e) {
            log.error("Failed to find control points for equipment template: {}", equipmentTemplateId, e);
            return List.of();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean fieldCopy(List<CommandFieldCopyDTO> commandFieldCopyList) {
        try {
            if (commandFieldCopyList == null || commandFieldCopyList.isEmpty()) {
                return true;
            }
            List<Control> controlList = new ArrayList<>(commandFieldCopyList.size());
            for (CommandFieldCopyDTO dto : commandFieldCopyList) {
                Integer equipmentTemplateId = dto.getEquipmentTemplateId();
                Integer controlId = dto.getControlId();
                String fieldName = dto.getFieldName();
                String fieldValue = dto.getFieldValue();
                // 处理特殊字段 controlMeaningsList
                if ("controlMeaningsList".equals(fieldName)) {
                    handleControlMeanings(equipmentTemplateId, controlId, fieldValue);
                    continue;
                }
                // 处理普通字段：通过反射构建 TblControl 实例并设置字段值
                Control control = ReflectUtil.newInstance(Control.class);
                control.setEquipmentTemplateId(equipmentTemplateId);
                control.setControlId(controlId);
                ReflectUtil.setFieldValue(control, fieldName, fieldValue);
                controlList.add(control);
            }
            // 批量更新主表
            if (!controlList.isEmpty()) {
                controlMapper.batchUpdateField(controlList);
            }

            log.info("Successfully copied {} control fields", commandFieldCopyList.size());
            return true;
        } catch (Exception e) {
            log.error("Failed to copy control fields", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disposeSimilarControl(SimilarDataDTO similarDataDTO) {
        try {
            // 参数验证
            Integer startNumber = similarDataDTO.getStartNumber();
            Integer abortNumber = similarDataDTO.getAbortNumber();
            if (Objects.isNull(startNumber) || startNumber < 0 || startNumber > abortNumber) {
                throw new IllegalArgumentException("startNumber Exception");
            }
            if (abortNumber > 999) {
                throw new IllegalArgumentException(i18n.T("abortNumber.format"));
            }
            String wildcard = similarDataDTO.getWildcard();
            if (CharSequenceUtil.isBlank(wildcard) || !wildcard.contains("{0}")) {
                throw new IllegalArgumentException(i18n.T("wildcard.format"));
            }

            startNumber = Math.max(startNumber, 1);
            abortNumber = Math.max(abortNumber, 1);

            // 简化实现，暂时只做基本的相似控制处理
            // 完整实现需要依赖更多的Service和复杂的业务逻辑
            log.info("Processing similar control for equipment template: {}, wildcard: {}, range: {}-{}",
                    similarDataDTO.getEquipmentTemplateId(), wildcard, startNumber, abortNumber);

            // TODO: 实现完整的相似控制处理逻辑
            // 这里需要根据tcs-config的完整实现来补充

        } catch (Exception e) {
            log.error("Failed to dispose similar control", e);
            throw e;
        }
    }

    @Override
    public void updateControlDescriptions(Integer childTemplateId, List<Control> parentControls) {
        if (parentControls.isEmpty()) {
            return;
        }

        // 转换为Map便于查找
        Map<Integer, String> controlDescMap = parentControls.stream()
                .collect(Collectors.toMap(Control::getControlId, Control::getDescription));

        // 批量更新
        for (Map.Entry<Integer, String> entry : controlDescMap.entrySet()) {
            Integer controlId = entry.getKey();
            String description = entry.getValue();

            int updated = controlMapper.update(null,
                    new UpdateWrapper<Control>()
                            .eq("EquipmentTemplateId", childTemplateId)
                            .eq("ControlId", controlId)
                            .set("Description", description)
            );

            if (updated > 0) {
                log.debug("子模版 {} 的控制 {} 描述已更新", childTemplateId, controlId);

                // 记录操作日志
                if (operationDetailService != null) {
                    try {
                        operationDetailService.recordOperationLog(
                                String.valueOf(childTemplateId),
                                OperationObjectTypeEnum.CONTROL,
                                String.format("control.%d.description", controlId),
                                i18n.T("update"),
                                "",
                                description
                        );
                    } catch (Exception e) {
                        log.warn("Failed to record operation log for control description update", e);
                    }
                }
            }
        }
    }

    /**
     * 复制 controlMeaningsList 字段内容到目标设备模板和控制ID下
     */
    private void handleControlMeanings(Integer srcTemplateId, Integer srcControlId, String fieldValue) {
        String[] dest = fieldValue.split("\\.");
        Integer destTemplateId = Integer.valueOf(dest[0]);
        Integer destControlId = Integer.valueOf(dest[1]);
        List<ControlMeanings> meaningsList = controlMeaningsService.findByEquipmentTemplateIdAndControlId(srcTemplateId, srcControlId);
        List<ControlMeanings> copiedList = meaningsList.stream()
                .map(meaning -> {
                    meaning.setId(null);
                    meaning.setEquipmentTemplateId(destTemplateId);
                    meaning.setControlId(destControlId);
                    return meaning;
                })
                .toList();
        controlMeaningsService.batchUpdateControlMeanings(copiedList);
    }
    @Override
    public List<ControlExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId) {
        return controlMapper.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
    }
    @Override
    public List<Control> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return controlMapper.selectList(new LambdaQueryWrapper<Control>()
                .eq(Control::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public void copyControl(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<Control> controlList = findByEquipmentTemplateId(originEquipmentTemplateId);
        if (controlList.isEmpty()) {
            return;
        }
        controlList.forEach(control -> control.setEquipmentTemplateId(destEquipmentTemplateId));
        controlMapper.batchInsertControl(controlList);
        controlMeaningsService.copyControlMeanings(originEquipmentTemplateId, destEquipmentTemplateId);
    }

    @Override
    public List<ControlVO> findVoByEquipmentTemplateId(Integer equipmentTemplateId) {
        return controlMapper.findVoByEquipmentTemplateId(equipmentTemplateId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        controlMapper.delete(Wrappers.lambdaQuery(Control.class)
                .eq(Control::getEquipmentTemplateId, equipmentTemplateId));
        controlMeaningsService.deleteByEquipmentTemplateId(equipmentTemplateId);
    }
}
