package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.EventBaseDic;
import com.siteweb.tcs.siteweb.mapper.EventBaseDicMapper;
import com.siteweb.tcs.siteweb.service.IEventBaseDicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.IntFunction;

/**
 * Event Base Dictionary Service Implementation
 */
@Slf4j
@Service
public class EventBaseDicServiceImpl extends IsolatedServiceImpl<EventBaseDicMapper, EventBaseDic> implements IEventBaseDicService {

    @Override
    public EventBaseDic findByBaseTypeId(Long baseTypeId) {
        return getOne(Wrappers.lambdaQuery(EventBaseDic.class)
                .eq(EventBaseDic::getBaseTypeId, BigDecimal.valueOf(baseTypeId)));
    }

    @Override
    public void updateBaseClassStandardDictionary(Integer equipmentTemplateId) {
        // 由BaseDicServiceImpl统一处理
    }

    @Override
    public boolean existsByBaseTypeId(Long baseTypeId) {
        return count(Wrappers.lambdaQuery(EventBaseDic.class)
                .eq(EventBaseDic::getBaseTypeId, BigDecimal.valueOf(baseTypeId))) > 0;
    }

    @Override
    public void generateBaseDic(Long baseTypeId, Long sourceId) {
        baseMapper.generateEventBaseDic(baseTypeId, sourceId);
        
        // 更新baseTypeName
        EventBaseDic eventBaseDic = findByBaseTypeId(baseTypeId);
        if (eventBaseDic != null && eventBaseDic.getBaseNameExt() != null && !eventBaseDic.getBaseNameExt().isEmpty()) {
            Long moduleNo = baseTypeId % 1000;
            String baseTypeName = eventBaseDic.getBaseNameExt().replace("{0}", moduleNo.toString());
            eventBaseDic.setBaseTypeName(baseTypeName);
            updateById(eventBaseDic);
        }
    }

    @Override
    public void processBaseTypeIdList(IntFunction<List<Long>> findBaseTypeIdsFunction, com.siteweb.tcs.siteweb.service.IBaseDicService baseDicService, Integer equipmentTemplateId) {
        // 由BaseDicServiceImpl统一处理
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLianTongByBaseTypeId(Integer baseTypeId) {
        try {
            // 删除指定基础类型ID的联通事件基础字典
            remove(new LambdaQueryWrapper<EventBaseDic>()
                    .eq(EventBaseDic::getBaseTypeId, BigDecimal.valueOf(baseTypeId)));
            return true;
        } catch (Exception e) {
            log.error("Failed to delete LianTong event cmcc dictionary by baseTypeId: " + baseTypeId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createLianTongEventBaseDic() {
        try {
            List<EventBaseDic> eventBaseDics = new ArrayList<>();

            // 创建联通事件基础字典
            eventBaseDics.add(createEventBaseDic(7, 1, "10KV配电设备事件", "10KV Distribution Equipment Event"));
            eventBaseDics.add(createEventBaseDic(7, 2, "低压配电柜事件", "Low Voltage Distribution Event"));
            eventBaseDics.add(createEventBaseDic(7, 3, "柴油发电机组事件", "Diesel Generator Event"));
            eventBaseDics.add(createEventBaseDic(7, 4, "240V高压直流交流屏事件", "240V High Voltage DC-AC Board Event"));
            eventBaseDics.add(createEventBaseDic(7, 5, "240V高压直流整流屏事件", "240V High Voltage DC-Rectifier Board Event"));
            eventBaseDics.add(createEventBaseDic(7, 6, "240V高压直流直流屏事件", "240V High Voltage DC-DC Board Event"));
            eventBaseDics.add(createEventBaseDic(7, 7, "直流操作电源柜事件", "DC Operational Power Supply System Event"));
            eventBaseDics.add(createEventBaseDic(7, 8, "10KV/400V变压器事件", "10KV/400V Transformer Event"));
            eventBaseDics.add(createEventBaseDic(7, 9, "48V开关电源整流屏事件", "48V Power Supply Rectifier Board Event"));
            eventBaseDics.add(createEventBaseDic(7, 10, "48V开关电源交流屏事件", "48V Power Supply AC Board Event"));
            eventBaseDics.add(createEventBaseDic(7, 11, "48V组合开关电源事件", "48V Power Supply Event"));
            eventBaseDics.add(createEventBaseDic(7, 12, "48V开关电源直流屏事件", "48V Power Supply DC Board Event"));
            eventBaseDics.add(createEventBaseDic(7, 13, "48V铅酸阀控胆电池组事件", "48V Battery Event"));
            eventBaseDics.add(createEventBaseDic(7, 14, "240V直流系统事件", "240V DC System Event"));
            eventBaseDics.add(createEventBaseDic(7, 15, "240V铅酸阀控胆电池组事件", "240V Battery Event"));
            eventBaseDics.add(createEventBaseDic(7, 16, "电容补偿柜事件", "Capacitance Compensation Cabinet Event"));
            eventBaseDics.add(createEventBaseDic(7, 17, "调波抑制柜事件", "Hamonic Filter Event"));
            eventBaseDics.add(createEventBaseDic(7, 18, "低压联络柜事件", "Low Voltage Contact Cabinet Event"));
            eventBaseDics.add(createEventBaseDic(7, 19, "备用电源投入装置事件", "Backup Power Input Equipment Event"));
            eventBaseDics.add(createEventBaseDic(7, 20, "UPS事件", "UPS Event"));
            eventBaseDics.add(createEventBaseDic(7, 21, "直流远供局站电源事件", "DC Remote Power Supply Infrastructure Event"));
            eventBaseDics.add(createEventBaseDic(7, 22, "太阳能供电事件", "Solar Power Equipment Event"));
            eventBaseDics.add(createEventBaseDic(7, 23, "直流直流变换器事件", "DC-DC Converter Event"));
            eventBaseDics.add(createEventBaseDic(7, 24, "模块化UPS事件", "Module UPS Event"));
            eventBaseDics.add(createEventBaseDic(7, 25, "UPS铅酸阀控胆电池组事件", "UPS Battery Event"));
            eventBaseDics.add(createEventBaseDic(7, 26, "交流配电屏事件", "AC Distribution Board Event"));
            eventBaseDics.add(createEventBaseDic(7, 27, "交流配电箱事件", "AC Distribution Box Event"));
            eventBaseDics.add(createEventBaseDic(7, 28, "48V配电屏事件", "48V Distribution Board Event"));
            eventBaseDics.add(createEventBaseDic(7, 29, "48V配电箱事件", "48V Distribution Box Event"));
            eventBaseDics.add(createEventBaseDic(7, 30, "逆变器事件", "Inverter Event"));

            // 批量保存
            return saveBatch(eventBaseDics);
        } catch (Exception e) {
            log.error("Failed to create LianTong event cmcc dictionary", e);
            return false;
        }
    }

    /**
     * 创建事件基础字典对象
     *
     * @param baseTypeId 基础类型ID
     * @param eventTypeId 事件类型ID
     * @param eventName 事件名称
     * @param eventNameEn 事件英文名称
     * @return 事件基础字典对象
     */
    private EventBaseDic createEventBaseDic(Integer baseTypeId, Integer eventTypeId, String eventName, String eventNameEn) {
        EventBaseDic eventBaseDic = new EventBaseDic();
        eventBaseDic.setBaseTypeId(BigDecimal.valueOf(baseTypeId));
        eventBaseDic.setBaseTypeName(eventName);
        eventBaseDic.setEnglishName(eventNameEn);
        eventBaseDic.setIsSystem(false);
        return eventBaseDic;
    }
} 