package com.siteweb.tcs.siteweb.util;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 注解工具类
 * 
 * <AUTHOR> (2024-04-09)
 **/
public class AnnotationUtils {

    // 获取类及其父类上的注解
    public static <T extends Annotation> T getAnnotationInHierarchy(Class<?> clazz, Class<T> annotationType) {
        T annotation = clazz.getAnnotation(annotationType);
        if (annotation != null) {
            return annotation;
        }

        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null) {
            return getAnnotationInHierarchy(superClass, annotationType);
        }

        return null;
    }

    // 获取字段及其父类中定义的字段上的注解
    public static <T extends Annotation> T getFieldAnnotationInHierarchy(Class<?> clazz, String fieldName, Class<T> annotationType) throws NoSuchFieldException {
        Field field = getFieldInHierarchy(clazz, fieldName);
        return (field != null) ? field.getAnnotation(annotationType) : null;
    }

    // 获取方法及其父类中定义的方法上的注解
    public static <T extends Annotation> T getMethodAnnotationInHierarchy(Class<?> clazz, String methodName, Class<T> annotationType, Class<?>... parameterTypes) throws NoSuchMethodException {
        Method method = getMethodInHierarchy(clazz, methodName, parameterTypes);
        return (method != null) ? method.getAnnotation(annotationType) : null;
    }

    // 获取类及其父类中定义的字段
    public static Field getFieldInHierarchy(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                return getFieldInHierarchy(superClass, fieldName);
            }
            throw e;
        }
    }

    // 获取类及其父类中定义的方法
    public static Method getMethodInHierarchy(Class<?> clazz, String methodName, Class<?>... parameterTypes) throws NoSuchMethodException {
        try {
            return clazz.getDeclaredMethod(methodName, parameterTypes);
        } catch (NoSuchMethodException e) {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                return getMethodInHierarchy(superClass, methodName, parameterTypes);
            }
            throw e;
        }
    }
} 