package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import org.dom4j.Element;

import java.util.List;

/**
 * Equipment Template XML Service Interface
 */
public interface IEquipmentTemplateXmlService {
    
    /**
     * Export equipment template to XML string
     *
     * @param equipmentTemplateId Equipment template ID
     * @return XML string
     */
    String exportEquipmentTemplate(Integer equipmentTemplateId);
    
    /**
     * Export equipment template list to XML Element
     *
     * @param equipmentTemplateIds List of equipment template IDs
     * @return XML Element
     */
    Element exportEquipmentTemplateElement(List<Integer> equipmentTemplateIds);
    
    /**
     * Import equipment template from XML
     *
     * @param equipmentTemplatesElement XML root element containing equipment templates
     * @return Imported equipment template
     */
    EquipmentTemplate importTemplate(Element equipmentTemplatesElement);
}
