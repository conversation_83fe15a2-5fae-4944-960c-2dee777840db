package com.siteweb.tcs.siteweb.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.StationStructure;

/**
 * Station Structure Service Interface
 */
public interface IStationStructureService extends IService<StationStructure> {

    /**
     * 根据结构ID查询站点结构
     *
     * @param structureId 结构ID
     * @return 站点结构
     */
    StationStructure findStructureById(Integer structureId);

    /**
     * 创建站点结构
     *
     * @param stationStructure 站点结构
     * @return 创建结果
     */
    boolean create(StationStructure stationStructure);

    /**
     * 根据结构组ID查询站点结构列表
     *
     * @param structureGroupId 结构组ID
     * @return 站点结构列表
     */
    List<StationStructure> getStructureByStructureGroupId(Integer structureGroupId);

    StationStructure getRootStationStructure();

    StationStructure getTCSStationStructure();

}
