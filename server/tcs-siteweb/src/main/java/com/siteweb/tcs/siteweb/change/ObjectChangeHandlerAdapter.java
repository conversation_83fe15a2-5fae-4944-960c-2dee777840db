package com.siteweb.tcs.siteweb.change;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public abstract class ObjectChangeHandlerAdapter implements ObjectChangeHandler {

    @Autowired
    private ChangeMessageScheduler changeMessageScheduler;

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        log.info("监听到Create消息{}", changeRecord);
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        log.info("监听到Delete消息{}", changeRecord);
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        log.info("监听到Update消息{}", changeRecord);
    }

    @PostConstruct
    public final void registerHandler() {
        List<Class<?>> entityClassList = doRegisterHandler();
        for (Class<?> entityClass : entityClassList) {
            System.out.println("监听"+entityClass);
            ChangeEventListener eventListener = changeMessageScheduler.getEventListener(entityClass);
            eventListener.listen(this);
        }
    }

    protected abstract List<Class<?>> doRegisterHandler();
}
