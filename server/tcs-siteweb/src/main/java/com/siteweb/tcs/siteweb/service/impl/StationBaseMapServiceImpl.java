package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.StationBaseMap;
import com.siteweb.tcs.siteweb.mapper.StationBaseMapMapper;
import com.siteweb.tcs.siteweb.service.IStationBaseMapService;
import org.springframework.stereotype.Service;

/**
 * Station Base Map Service Implementation
 */
@Service
public class StationBaseMapServiceImpl extends IsolatedServiceImpl<StationBaseMapMapper, StationBaseMap> implements IStationBaseMapService {

    @Override
    public int deleteByStandardType(Integer standardType) {
        return baseMapper.delete(new QueryWrapper<StationBaseMap>().eq("StandardType", standardType));
    }
}
