package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Command base dictionary entity
 */
@Data
@TableName("tbl_commandbasedic")
public class CommandBaseDic implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "BaseTypeId")
    private BigDecimal baseTypeId;

    @TableField("BaseTypeName")
    private String baseTypeName;

    @TableField("BaseEquipmentId")
    private Integer baseEquipmentId;

    @TableField("EnglishName")
    private String englishName;

    @TableField("BaseLogicCategoryId")
    private Integer baseLogicCategoryId;

    @TableField("CommandType")
    private Integer commandType;

    @TableField("BaseStatusId")
    private Integer baseStatusId;

    @TableField("ExtendField1")
    private String extendField1;

    @TableField("ExtendField2")
    private String extendField2;

    @TableField("ExtendField3")
    private String extendField3;

    @TableField("Description")
    private String description;

    @TableField("BaseNameExt")
    private String baseNameExt;

    @TableField("IsSystem")
    private Boolean isSystem;
}
