package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import com.siteweb.tcs.siteweb.annotation.ConfigId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Work station entity
 */
@Data
@TableName("tbl_workstation")
@ChangeSource(channel = "tcs", product = "siteweb", source = "workstation")
public class WorkStation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "WorkStationId")
    private Integer workStationId;

    @TableField("WorkStationName")
    private String workStationName;

    @TableField("WorkStationType")
    private Integer workStationType;

    @TableField("IPAddress")
    private String ipAddress;

    @TableField("ParentId")
    private Integer parentId;

    @TableField("ConnectState")
    private Integer connectState;

    @TableField("UpdateTime")
    private LocalDateTime updateTime;

    @TableField("IsUsed")
    private Boolean isUsed;

    @TableField("CPU")
    private Double cpu;

    @TableField("Memory")
    private Double memory;

    @TableField("ThreadCount")
    private Integer threadCount;

    @TableField("DiskFreeSpace")
    private Double diskFreeSpace;

    @TableField("DBFreeSpace")
    private Double dbFreeSpace;

    @TableField("LastCommTime")
    private LocalDateTime lastCommTime;
}
