<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.SignalBaseDicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.SignalBaseDic">
        <id column="BaseTypeId" property="baseTypeId" />
        <result column="BaseTypeName" property="baseTypeName" />
        <result column="BaseEquipmentId" property="baseEquipmentId" />
        <result column="EnglishName" property="englishName" />
        <result column="BaseLogicCategoryId" property="baseLogicCategoryId" />
        <result column="StoreInterval" property="storeInterval" />
        <result column="AbsValueThreshold" property="absValueThreshold" />
        <result column="PercentThreshold" property="percentThreshold" />
        <result column="StoreInterval2" property="storeInterval2" />
        <result column="AbsValueThreshold2" property="absValueThreshold2" />
        <result column="PercentThreshold2" property="percentThreshold2" />
        <result column="ExtendField1" property="extendField1" />
        <result column="ExtendField2" property="extendField2" />
        <result column="ExtendField3" property="extendField3" />
        <result column="UnitId" property="unitId" />
        <result column="BaseStatusId" property="baseStatusId" />
        <result column="BaseHysteresis" property="baseHysteresis" />
        <result column="BaseFreqPeriod" property="baseFreqPeriod" />
        <result column="BaseFreqCount" property="baseFreqCount" />
        <result column="BaseShowPrecision" property="baseShowPrecision" />
        <result column="BaseStatPeriod" property="baseStatPeriod" />
        <result column="CGElement" property="cgElement" />
        <result column="Description" property="description" />
        <result column="BaseNameExt" property="baseNameExt" />
        <result column="IsSystem" property="isSystem" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, BaseLogicCategoryId, StoreInterval, AbsValueThreshold, PercentThreshold, StoreInterval2, AbsValueThreshold2, PercentThreshold2, ExtendField1, ExtendField2, ExtendField3, UnitId, BaseStatusId, BaseHysteresis, BaseFreqPeriod, BaseFreqCount, BaseShowPrecision, BaseStatPeriod, CGElement, Description, BaseNameExt, IsSystem
    </sql>

    <!-- 生成信号基类字典 -->
    <insert id="generateSignalBaseDic">
        INSERT INTO TBL_SignalBaseDic
        (BaseTypeId,
         BaseTypeName,
         BaseEquipmentId,
         EnglishName,
         BaseLogicCategoryId,
         StoreInterval,
         AbsValueThreshold,
         PercentThreshold,
         StoreInterval2,
         AbsValueThreshold2,
         PercentThreshold2,
         ExtendField1,
         ExtendField2,
         ExtendField3,
         UnitId,
         BaseStatusId,
         BaseHysteresis,
         BaseFreqPeriod,
         BaseFreqCount,
         BaseShowPrecision,
         BaseStatPeriod,
         CGElement,
         Description,
         BaseNameExt,
         IsSystem)
        SELECT #{baseTypeId},
               signalBaseDic.BaseTypeName,
               signalBaseDic.BaseEquipmentId,
               signalBaseDic.EnglishName,
               signalBaseDic.BaseLogicCategoryId,
               signalBaseDic.StoreInterval,
               signalBaseDic.AbsValueThreshold,
               signalBaseDic.PercentThreshold,
               signalBaseDic.StoreInterval2,
               signalBaseDic.AbsValueThreshold2,
               signalBaseDic.PercentThreshold2,
               signalBaseDic.ExtendField1,
               signalBaseDic.ExtendField2,
               signalBaseDic.ExtendField3,
               signalBaseDic.UnitId,
               signalBaseDic.BaseStatusId,
               signalBaseDic.BaseHysteresis,
               signalBaseDic.BaseFreqPeriod,
               signalBaseDic.BaseFreqCount,
               signalBaseDic.BaseShowPrecision,
               signalBaseDic.BaseStatPeriod,
               signalBaseDic.CGElement,
               signalBaseDic.Description,
               signalBaseDic.BaseNameExt,
                <choose>
                    <when test="_databaseId == 'postgres'">
                        false
                    </when>
                    <otherwise>
                        0
                    </otherwise>
                </choose>
        FROM TBL_SignalBaseDic signalBaseDic
        WHERE signalBaseDic.BaseTypeId = #{sourceId}
    </insert>

</mapper>