package com.siteweb.tcs.middleware.common.service;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.FileInfo;
import com.siteweb.tcs.middleware.common.model.FileUploadResult;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.model.config.FileSystemServiceConfig;
import com.siteweb.tcs.middleware.common.resource.FileSystemResource;
import com.siteweb.tcs.middleware.common.resource.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 文件系统服务实现类
 */
public class FileSystemService extends BaseService {
    
    private static final Logger logger = LoggerFactory.getLogger(FileSystemService.class);

    private final FileSystemResource fileSystemResource;
    private final FileSystemServiceConfig config;
    private final ExecutorService executorService;

    public FileSystemService(String id, String name, String description, Resource resource, FileSystemServiceConfig config) {
        super(id, ServiceType.FILESYSTEM.getCode(), name, description, resource);
        this.fileSystemResource = (FileSystemResource) resource;
        this.config = config;
        // 只有在启用异步模式时才创建线程池
        // 同步模式下不创建线程池，异步方法会降级为同步执行
        this.executorService = config.isAsyncMode() ?
            Executors.newFixedThreadPool(config.getThreadPoolSize()) : null;
    }
    
    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化文件系统服务: {}", getId());
        // 验证资源类型
        if (!(resource instanceof FileSystemResource)) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "不支持的资源类型，需要FileSystemResource"
            );
        }
    }
    
    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动文件系统服务: {}", getId());
        try {
            // 验证资源状态
            if (!resource.isHealthy()) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_START_FAILED,
                    "文件系统资源未就绪"
                );
            }
            logger.info("文件系统服务启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动文件系统服务失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_START_FAILED,
                "启动文件系统服务失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止文件系统服务: {}", getId());
        try {
            // 关闭线程池（如果存在）
            if (executorService != null) {
                executorService.shutdown();
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            }
            logger.info("文件系统服务停止成功: {}", getId());
        } catch (Exception e) {
            logger.error("停止文件系统服务失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_STOP_FAILED,
                "停止文件系统服务失败: " + e.getMessage(),
                e
            );
        }
    }
    
    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁文件系统服务: {}", getId());
        logger.info("文件系统服务销毁成功: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            if (resource != null && resource.isHealthy()) {
                return HealthStatus.up("文件系统服务运行正常");
            } else {
                return HealthStatus.down("文件系统资源不健康");
            }
        } catch (Exception e) {
            return HealthStatus.down("检查健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件（同步版本）
     * 直接执行文件上传操作，不使用线程池
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param content 文件内容
     * @return 上传结果
     */
    public FileUploadResult writeFile(String filePath, String fileName, byte[] content) {
        try {
            logger.debug("上传文件: {}/{}", filePath, fileName);

            // 计算MD5
            String md5Hash = calculateMD5(content);

            // 调用底层资源上传
            boolean success = fileSystemResource.writeFile(filePath, fileName, content);

            if (success) {
                logger.info("文件上传成功: {}/{}, size: {}", filePath, fileName, content.length);
                return FileUploadResult.builder()
                    .success(true)
                    .filePath(filePath)
                    .fileName(fileName)
                    .fileSize(content.length)
                    .md5Hash(md5Hash)
                    .uploadTime(LocalDateTime.now())
                    .build();
            } else {
                logger.error("文件上传失败: {}/{}", filePath, fileName);
                return FileUploadResult.builder()
                    .success(false)
                    .filePath(filePath)
                    .fileName(fileName)
                    .errorMessage("文件上传失败")
                    .build();
            }
        } catch (Exception e) {
            logger.error("上传文件异常: {}/{}", filePath, fileName, e);
            return FileUploadResult.builder()
                .success(false)
                .filePath(filePath)
                .fileName(fileName)
                .errorMessage("上传文件异常: " + e.getMessage())
                .build();
        }
    }

    /**
     * 上传文件（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param content 文件内容
     * @return 上传结果的CompletableFuture
     */
    public CompletableFuture<FileUploadResult> writeFileAsync(String filePath, String fileName, byte[] content) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: uploadFileAsync -> uploadFile");
            return CompletableFuture.completedFuture(writeFile(filePath, fileName, content));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> writeFile(filePath, fileName, content), executorService);
    }
    

    /**
     * 下载文件（同步版本）
     * 直接执行文件下载操作，不使用线程池
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 文件内容，如果文件不存在返回null
     */
    public byte[] readFile(String filePath, String fileName) {
        try {
            logger.debug("下载文件: {}/{}", filePath, fileName);

            byte[] content = fileSystemResource.readFile(filePath, fileName);

            if (content == null) {
                logger.warn("文件不存在: {}/{}", filePath, fileName);
            }

            return content;
        } catch (Exception e) {
            logger.error("下载文件异常: {}/{}", filePath, fileName, e);
            throw new RuntimeException("下载文件异常: " + e.getMessage(), e);
        }
    }


    /**
     * 下载文件（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 文件内容的CompletableFuture
     */
    public CompletableFuture<byte[]> readFileAsync(String filePath, String fileName) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: downloadFileAsync -> downloadFile");
            return CompletableFuture.completedFuture(readFile(filePath, fileName));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> readFile(filePath, fileName), executorService);
    }
    

    /**
     * 删除文件（同步版本）
     * 直接执行文件删除操作，不使用线程池
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 是否删除成功
     */
    public Boolean deleteFile(String filePath, String fileName) {
        try {
            logger.debug("删除文件: {}/{}", filePath, fileName);

            boolean success = fileSystemResource.deleteFile(filePath, fileName);

            if (success) {
                logger.info("文件删除成功: {}/{}", filePath, fileName);
            } else {
                logger.warn("文件删除失败: {}/{}", filePath, fileName);
            }

            return success;
        } catch (Exception e) {
            logger.error("删除文件异常: {}/{}", filePath, fileName, e);
            throw new RuntimeException("删除文件异常: " + e.getMessage(), e);
        }
    }

    /**
     * 删除文件（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 删除结果的CompletableFuture
     */
    public CompletableFuture<Boolean> deleteFileAsync(String filePath, String fileName) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: deleteFileAsync -> deleteFile");
            return CompletableFuture.completedFuture(deleteFile(filePath, fileName));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> deleteFile(filePath, fileName), executorService);
    }
    

    /**
     * 列出目录文件（同步版本）
     * 直接执行目录文件列表操作，不使用线程池
     *
     * @param directoryPath 目录路径
     * @return 文件信息列表
     */
    public List<FileInfo> listFiles(String directoryPath) {
        try {
            logger.debug("列出目录文件: {}", directoryPath);

            List<FileInfo> files = fileSystemResource.listFiles(directoryPath);

            logger.info("列出目录文件成功: {}, 文件数: {}", directoryPath, files.size());
            return files;
        } catch (Exception e) {
            logger.error("列出目录文件异常: {}", directoryPath, e);
            throw new RuntimeException("列出目录文件异常: " + e.getMessage(), e);
        }
    }

    /**
     * 列出目录文件（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param directoryPath 目录路径
     * @return 文件信息列表的CompletableFuture
     */
    public CompletableFuture<List<FileInfo>> listFilesAsync(String directoryPath) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: listFilesAsync -> listFiles");
            return CompletableFuture.completedFuture(listFiles(directoryPath));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> listFiles(directoryPath), executorService);
    }
    

    /**
     * 检查文件是否存在（同步版本）
     * 直接执行文件存在检查操作，不使用线程池
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 文件是否存在
     */
    public Boolean fileExists(String filePath, String fileName) {
        try {
            return fileSystemResource.fileExists(filePath, fileName);
        } catch (Exception e) {
            logger.error("检查文件存在异常: {}/{}", filePath, fileName, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 文件存在结果的CompletableFuture
     */
    public CompletableFuture<Boolean> fileExistsAsync(String filePath, String fileName) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: fileExistsAsync -> fileExists");
            return CompletableFuture.completedFuture(fileExists(filePath, fileName));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> fileExists(filePath, fileName), executorService);
    }
    

    /**
     * 创建目录（同步版本）
     * 直接执行目录创建操作，不使用线程池
     *
     * @param directoryPath 目录路径
     * @return 是否创建成功
     */
    public Boolean createDirectory(String directoryPath) {
        try {
            logger.debug("创建目录: {}", directoryPath);

            boolean success = fileSystemResource.createDirectory(directoryPath);

            if (success) {
                logger.info("目录创建成功: {}", directoryPath);
            } else {
                logger.warn("目录创建失败: {}", directoryPath);
            }

            return success;
        } catch (Exception e) {
            logger.error("创建目录异常: {}", directoryPath, e);
            throw new RuntimeException("创建目录异常: " + e.getMessage(), e);
        }
    }

    /**
     * 创建目录（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param directoryPath 目录路径
     * @return 创建结果的CompletableFuture
     */
    public CompletableFuture<Boolean> createDirectoryAsync(String directoryPath) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: createDirectoryAsync -> createDirectory");
            return CompletableFuture.completedFuture(createDirectory(directoryPath));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> createDirectory(directoryPath), executorService);
    }


    /**
     * 删除目录（同步版本）
     * 直接执行目录删除操作，不使用线程池
     *
     * @param directoryPath 目录路径
     * @param recursive 是否递归删除
     * @return 是否删除成功
     */
    public Boolean deleteDirectory(String directoryPath, boolean recursive) {
        try {
            logger.debug("删除目录: {}, recursive: {}", directoryPath, recursive);

            boolean success = fileSystemResource.deleteDirectory(directoryPath, recursive);

            if (success) {
                logger.info("目录删除成功: {}", directoryPath);
            } else {
                logger.warn("目录删除失败: {}", directoryPath);
            }

            return success;
        } catch (Exception e) {
            logger.error("删除目录异常: {}", directoryPath, e);
            throw new RuntimeException("删除目录异常: " + e.getMessage(), e);
        }
    }

    /**
     * 删除目录（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param directoryPath 目录路径
     * @param recursive 是否递归删除
     * @return 删除结果的CompletableFuture
     */
    public CompletableFuture<Boolean> deleteDirectoryAsync(String directoryPath, boolean recursive) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: deleteDirectoryAsync -> deleteDirectory");
            return CompletableFuture.completedFuture(deleteDirectory(directoryPath, recursive));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> deleteDirectory(directoryPath, recursive), executorService);
    }


    /**
     * 获取文件信息（同步版本）
     * 直接执行文件信息获取操作，不使用线程池
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 文件信息，如果文件不存在返回null
     */
    public FileInfo getFileInfo(String filePath, String fileName) {
        try {
            logger.debug("获取文件信息: {}/{}", filePath, fileName);

            FileInfo fileInfo = fileSystemResource.getFileInfo(filePath, fileName);

            if (fileInfo != null) {
                logger.info("获取文件信息成功: {}/{}", filePath, fileName);
            } else {
                logger.warn("文件不存在: {}/{}", filePath, fileName);
            }

            return fileInfo;
        } catch (Exception e) {
            logger.error("获取文件信息异常: {}/{}", filePath, fileName, e);
            throw new RuntimeException("获取文件信息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件信息（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 文件信息的CompletableFuture
     */
    public CompletableFuture<FileInfo> getFileInfoAsync(String filePath, String fileName) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: getFileInfoAsync -> getFileInfo");
            return CompletableFuture.completedFuture(getFileInfo(filePath, fileName));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> getFileInfo(filePath, fileName), executorService);
    }


    /**
     * 复制文件（同步版本）
     * 直接执行文件复制操作，不使用线程池
     *
     * @param sourcePath 源文件路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件路径
     * @param targetFileName 目标文件名
     * @return 是否复制成功
     */
    public Boolean copyFile(String sourcePath, String sourceFileName,
                           String targetPath, String targetFileName) {
        try {
            logger.debug("复制文件: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);

            boolean success = fileSystemResource.copyFile(sourcePath, sourceFileName, targetPath, targetFileName);

            if (success) {
                logger.info("文件复制成功: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);
            } else {
                logger.warn("文件复制失败: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);
            }

            return success;
        } catch (Exception e) {
            logger.error("复制文件异常: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            throw new RuntimeException("复制文件异常: " + e.getMessage(), e);
        }
    }

    /**
     * 复制文件（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param sourcePath 源文件路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件路径
     * @param targetFileName 目标文件名
     * @return 复制结果的CompletableFuture
     */
    public CompletableFuture<Boolean> copyFileAsync(String sourcePath, String sourceFileName,
                                                   String targetPath, String targetFileName) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: copyFileAsync -> copyFile");
            return CompletableFuture.completedFuture(copyFile(sourcePath, sourceFileName, targetPath, targetFileName));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> copyFile(sourcePath, sourceFileName, targetPath, targetFileName), executorService);
    }


    /**
     * 移动文件（同步版本）
     * 直接执行文件移动操作，不使用线程池
     *
     * @param sourcePath 源文件路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件路径
     * @param targetFileName 目标文件名
     * @return 是否移动成功
     */
    public Boolean moveFile(String sourcePath, String sourceFileName,
                           String targetPath, String targetFileName) {
        try {
            logger.debug("移动文件: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);

            boolean success = fileSystemResource.moveFile(sourcePath, sourceFileName, targetPath, targetFileName);

            if (success) {
                logger.info("文件移动成功: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);
            } else {
                logger.warn("文件移动失败: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);
            }

            return success;
        } catch (Exception e) {
            logger.error("移动文件异常: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            throw new RuntimeException("移动文件异常: " + e.getMessage(), e);
        }
    }

    /**
     * 移动文件（异步版本）
     * 如果配置为异步模式，使用线程池异步执行
     * 如果配置为同步模式，会降级为同步执行并返回已完成的CompletableFuture
     *
     * @param sourcePath 源文件路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件路径
     * @param targetFileName 目标文件名
     * @return 移动结果的CompletableFuture
     */
    public CompletableFuture<Boolean> moveFileAsync(String sourcePath, String sourceFileName,
                                                   String targetPath, String targetFileName) {
        if (executorService == null) {
            // 同步模式：直接返回已完成的Future
            logger.debug("同步模式下调用异步接口，降级为同步执行: moveFileAsync -> moveFile");
            return CompletableFuture.completedFuture(moveFile(sourcePath, sourceFileName, targetPath, targetFileName));
        }
        // 异步模式：使用线程池异步执行
        return CompletableFuture.supplyAsync(() -> moveFile(sourcePath, sourceFileName, targetPath, targetFileName), executorService);
    }

    private String calculateMD5(byte[] content) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(content);
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            logger.warn("计算MD5失败", e);
            return null;
        }
    }
}
