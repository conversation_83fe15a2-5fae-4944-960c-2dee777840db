<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.s6.access.dal.mapper.ControlMapMapper">

    <resultMap id="BaseResultMap" type="com.siteweb.tcs.s6.access.dal.entity.ControlMap">
        <result column="DeviceId" property="deviceId" />
        <result column="NorthEquipmentId" property="northEquipmentId" />
        <result column="ControlId" property="controlId" />
        <result column="NorthControlId" property="northControlId" />
        <result column="Deleted" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        DeviceId, NorthEquipmentId, ControlId, NorthControlId, Deleted
    </sql>

    <select id="selectByDeviceId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_control_map
        WHERE DeviceId = #{deviceId} AND Deleted = FALSE
    </select>
    
    <select id="selectByNorthEquipmentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_control_map
        WHERE NorthEquipmentId = #{northEquipmentId} AND Deleted = FALSE
    </select>

    <select id="selectByControlId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_control_map
        WHERE ControlId = #{controlId} AND Deleted = FALSE
    </select>

    <select id="selectByNorthControlId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_control_map
        WHERE NorthControlId = #{northControlId} AND Deleted = FALSE
    </select>
    
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO tcs_control_map (DeviceId, NorthEquipmentId, ControlId, NorthControlId, Deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.deviceId}, #{item.northEquipmentId}, #{item.controlId}, #{item.northControlId}, #{item.deleted})
        </foreach>
    </insert>

    <update id="deleteByCompositeKey">
        UPDATE tcs_control_map
        SET Deleted = TRUE
        WHERE DeviceId = #{deviceId}
        AND ControlId = #{controlId}
        AND NorthControlId = #{northControlId}
    </update>

</mapper> 