<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.s6.access.dal.mapper.DeviceMapMapper">

    <resultMap id="BaseResultMap" type="com.siteweb.tcs.s6.access.dal.entity.DeviceMap">
        <result column="GatewayId" property="gatewayId" />
        <result column="NorthMonitorUnitId" property="northMonitorUnitId" />
        <result column="DeviceId" property="deviceId" />
        <result column="NorthEquipmentId" property="northEquipmentId" />
        <result column="Deleted" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        GatewayId, NorthMonitorUnitId, DeviceId, NorthEquipmentId, Deleted
    </sql>

    <select id="selectByGatewayId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_device_map
        WHERE GatewayId = #{gatewayId} AND Deleted = FALSE
    </select>
    
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO tcs_device_map (GatewayId, NorthMonitorUnitId, DeviceId, NorthEquipmentId, Deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.gatewayId}, #{item.northMonitorUnitId}, #{item.deviceId}, #{item.northEquipmentId}, #{item.deleted})
        </foreach>
    </insert>

    <update id="deleteByCompositeKey">
        UPDATE tcs_device_map
        SET Deleted = TRUE
        WHERE GatewayId = #{gatewayId}
        AND DeviceId = #{deviceId}
        AND NorthEquipmentId = #{northEquipmentId}
    </update>

</mapper> 