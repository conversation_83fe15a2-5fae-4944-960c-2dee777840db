<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.s6.access.dal.mapper.GatewayMapMapper">
    
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.s6.access.dal.entity.GatewayMap">
        <result column="GatewayId" property="gatewayId" />
        <result column="NorthMonitorUnitId" property="northMonitorUnitId" />
        <result column="NorthStationId" property="northStationId" />
        <result column="Deleted" property="deleted" />
    </resultMap>

    <!-- 设备映射结果映射 -->
    <resultMap id="DeviceMapResultMap" type="com.siteweb.tcs.s6.access.dal.entity.DeviceMap">
        <result column="DeviceId" property="deviceId" />
        <result column="NorthEquipmentId" property="northEquipmentId" />
        <result column="NorthEquipmentTemplateId" property="northEquipmentTemplateId" />
        <result column="DeviceDeleted" property="deleted" />
    </resultMap>

    <!-- 信号映射结果映射 -->
    <resultMap id="SignalMapResultMap" type="com.siteweb.tcs.s6.access.dal.entity.SignalMap">
        <result column="SignalId" property="signalId" />
        <result column="NorthSignalId" property="northSignalId" />
        <result column="SignalDeleted" property="deleted" />
    </resultMap>

    <!-- 告警映射结果映射 -->
    <resultMap id="AlarmMapResultMap" type="com.siteweb.tcs.s6.access.dal.entity.AlarmMap">
        <result column="AlarmId" property="alarmId" />
        <result column="NorthEventId" property="northEventId" />
        <result column="NorthEventConditionId" property="northEventConditionId" />
        <result column="AlarmDeleted" property="deleted" />
    </resultMap>

    <!-- 控制映射结果映射 -->
    <resultMap id="ControlMapResultMap" type="com.siteweb.tcs.s6.access.dal.entity.ControlMap">
        <result column="ControlId" property="controlId" />
        <result column="NorthControlId" property="northControlId" />
        <result column="ControlDeleted" property="deleted" />
    </resultMap>

    <!-- 设备映射包含信号、告警、控制的完整结果映射 -->
    <resultMap id="DeviceMapWithRelationsResultMap" type="com.siteweb.tcs.s6.access.dal.entity.DeviceMap" extends="DeviceMapResultMap">
        <collection property="signalMapList" ofType="com.siteweb.tcs.s6.access.dal.entity.SignalMap" resultMap="SignalMapResultMap" />
        <collection property="alarmMapList" ofType="com.siteweb.tcs.s6.access.dal.entity.AlarmMap" resultMap="AlarmMapResultMap" />
        <collection property="controlMapList" ofType="com.siteweb.tcs.s6.access.dal.entity.ControlMap" resultMap="ControlMapResultMap" />
    </resultMap>

    <!-- 网关映射包含完整关联数据的结果映射 -->
    <resultMap id="GatewayMapWithFullRelationsResultMap" type="com.siteweb.tcs.s6.access.dal.entity.GatewayMap" extends="BaseResultMap">
        <collection property="deviceMapList" ofType="com.siteweb.tcs.s6.access.dal.entity.DeviceMap" resultMap="DeviceMapWithRelationsResultMap" />
    </resultMap>

    <sql id="Base_Column_List">
        GatewayId, NorthMonitorUnitId, Deleted
    </sql>

    <!-- 根据网关ID查询网关映射 -->
    <select id="selectByGatewayId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_gateway_map
        WHERE GatewayId = #{gatewayId}
        AND Deleted = FALSE
    </select>

    <!-- 根据北向监控单元ID查询网关映射 -->
    <select id="selectByNorthMonitorUnitId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_gateway_map
        WHERE NorthMonitorUnitId = #{northMonitorUnitId}
        AND Deleted = FALSE
    </select>

    <!-- 查询所有活跃的网关映射 -->
    <select id="selectAllActive" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_gateway_map
        WHERE Deleted = FALSE
        ORDER BY GatewayId, NorthMonitorUnitId
    </select>

    <!-- 根据网关ID查询包含完整关联数据的网关映射 -->
    <select id="selectGatewayMapWithFullRelationsByGatewayId" resultMap="GatewayMapWithFullRelationsResultMap">
        SELECT 
            gm.GatewayId,
            gm.NorthStationId,
            gm.NorthMonitorUnitId,
            gm.Deleted,
            dm.DeviceId,
            dm.NorthEquipmentId,
            dm.NorthEquipmentTemplateId,
            dm.Deleted as DeviceDeleted,
            sm.SignalId,
            sm.NorthSignalId,
            sm.Deleted as SignalDeleted,
            am.AlarmId,
            am.NorthEventId,
            am.NorthEventConditionId,
            am.Deleted as AlarmDeleted,
            cm.ControlId,
            cm.NorthControlId,
            cm.Deleted as ControlDeleted
        FROM tcs_gateway_map gm
        LEFT JOIN tcs_device_map dm ON gm.GatewayId = dm.GatewayId AND dm.Deleted = FALSE
        LEFT JOIN tcs_signal_map sm ON dm.DeviceId = sm.DeviceId AND dm.NorthEquipmentId = sm.NorthEquipmentId AND sm.Deleted = FALSE
        LEFT JOIN tcs_alarm_map am ON dm.DeviceId = am.DeviceId AND dm.NorthEquipmentId = am.NorthEquipmentId AND am.Deleted = FALSE
        LEFT JOIN tcs_control_map cm ON dm.DeviceId = cm.DeviceId AND dm.NorthEquipmentId = cm.NorthEquipmentId AND cm.Deleted = FALSE
        WHERE gm.GatewayId = #{gatewayId}
        AND gm.Deleted = FALSE
        ORDER BY gm.GatewayId, gm.NorthMonitorUnitId, dm.DeviceId, dm.NorthEquipmentId
    </select>

    <!-- 根据复合主键删除网关映射 -->
    <update id="deleteByCompositeKey">
        UPDATE tcs_gateway_map 
        SET Deleted = TRUE
        WHERE GatewayId = #{gatewayId}
        AND NorthMonitorUnitId = #{northMonitorUnitId}
    </update>


    <!-- 更新网关映射 -->
    <update id="updateById" parameterType="com.siteweb.tcs.s6.access.dal.entity.GatewayMap">
        UPDATE tcs_gateway_map
        <set>
            <if test="deleted != null">Deleted = #{deleted}</if>
        </set>
        WHERE GatewayId = #{gatewayId}
        AND NorthMonitorUnitId = #{northMonitorUnitId}
    </update>

    <!-- 查询所有网关映射（包括已删除的） -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_gateway_map
        ORDER BY GatewayId, NorthMonitorUnitId
    </select>

</mapper> 