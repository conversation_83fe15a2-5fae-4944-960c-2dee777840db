package com.siteweb.tcs.s6.access.connector.process;

import cn.hutool.json.JSONUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.LifeCycleEvent;
import com.siteweb.tcs.hub.util.TCSTopicUtil;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;
import com.siteweb.tcs.s6.access.web.lcm.LCMHandlerObserver;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-09-01 18:29
 **/

public class NorthS6GatewayConfigChangeProcessor  extends ProbeActor {

    private final ActorRef mediator;

    private LCMHandlerObserver lcmHandlerObserver;

    private NorthS6GatewayConfigChangeProcessor(GatewayMap gatewayMap){
        this.mediator = DistributedPubSub.get(getContext().system()).mediator();
        this.lcmHandlerObserver = PluginScope.getBean(LCMHandlerObserver.class);
        handlerSubscribe(gatewayMap.getGatewayId());
    }

    private void handlerSubscribe(Long gatewayId) {
        String topicName = TCSTopicUtil.getTopicName(SubscriptionTypeEnum.LIFECYCLE_EVENT, gatewayId);
        mediator.tell(new DistributedPubSubMediator.Subscribe(topicName, getSelf()), getSelf());
    }

    public static Props props(GatewayMap gatewayMap) {
        return Props.create(NorthS6GatewayConfigChangeProcessor.class,gatewayMap);
    }

    @Override
    public Receive createReceive() {
        Receive receive = ReceiveBuilder.create().match(LifeCycleEvent.class, this::onLifeCycleEvent).build();
        return super.createReceive().orElse(receive);
    }

    private void onLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        GatewayConfigChangeDto configChangeDto = (GatewayConfigChangeDto) lifeCycleEvent.getChangeObject();
        try {
            probe.info("收到生命周期事件，内容为"+ JSONUtil.toJsonStr(lifeCycleEvent));
            lcmHandlerObserver.handleEvent(configChangeDto.getPluginId(),configChangeDto);
        }catch (Exception ex){
            probe.error("收到生命周期事件，但是处理异常，错误为"+ex.getMessage());
            ex.printStackTrace();
        }
    }
}
