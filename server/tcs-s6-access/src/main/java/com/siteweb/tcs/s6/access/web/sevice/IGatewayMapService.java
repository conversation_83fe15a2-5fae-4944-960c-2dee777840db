package com.siteweb.tcs.s6.access.web.sevice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;

import java.util.List;

/**
 * 网关映射服务接口
 */
public interface IGatewayMapService extends IService<GatewayMap> {

    /**
     * 根据网关ID查询网关映射
     * @param gatewayId 网关ID
     * @return 网关映射列表
     */
    GatewayMap getByGatewayId(Long gatewayId);

    /**
     * 根据北向监控单元ID查询网关映射
     * @param northMonitorUnitId 北向监控单元ID
     * @return 网关映射列表
     */
    List<GatewayMap> getByNorthMonitorUnitId(Integer northMonitorUnitId);

    /**
     * 批量保存网关映射
     * @param gatewayMaps 网关映射列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<GatewayMap> gatewayMaps);

    /**
     * 根据复合主键删除网关映射
     * @param gatewayId 网关ID
     * @param northMonitorUnitId 北向监控单元ID
     * @return 是否删除成功
     */
    boolean deleteByCompositeKey(Long gatewayId, Integer northMonitorUnitId);

    /**
     * 查询所有活跃的网关映射
     * @return 网关映射列表
     */
    List<GatewayMap> getAllActive();

    boolean deleteByGatewayId(Long gatewayId);

    /**
     * 根据网关ID查询包含完整关联数据的网关映射
     * 包含 deviceMapList 以及 deviceMap 下面的 signalMapList、alarmMapList、controlMapList
     * @param gatewayId 网关ID
     * @return 包含完整关联数据的网关映射列表
     */
    List<GatewayMap> getGatewayMapWithFullRelationsByGatewayId(Long gatewayId);
}