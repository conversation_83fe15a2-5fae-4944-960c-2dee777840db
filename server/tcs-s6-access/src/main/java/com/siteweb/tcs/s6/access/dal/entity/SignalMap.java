package com.siteweb.tcs.s6.access.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 信号映射表实体类
 */
@Data
@TableName("tcs_signal_map")
public class SignalMap implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableField("DeviceId")
    private Long deviceId;

    /**
     * 北向设备ID
     */
    @TableField("NorthEquipmentId")
    private Integer northEquipmentId;

    /**
     * 信号ID
     */
    @TableField("SignalId")
    private Long signalId;

    /**
     * 北向信号ID
     */
    @TableField("NorthSignalId")
    private Integer northSignalId;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "false", delval = "true")
    @TableField("Deleted")
    private Boolean deleted;
} 