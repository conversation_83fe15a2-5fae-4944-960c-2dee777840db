package com.siteweb.tcs.s6.access.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.s6.access.dal.entity.ApiLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * API日志Mapper接口
 */
@Mapper
@Repository
public interface ApiLogMapper extends BaseMapper<ApiLog> {

    /**
     * 根据API路径查询日志
     * @param apiPath API路径
     * @return 日志列表
     */
    List<ApiLog> selectByApiPath(@Param("apiPath") String apiPath);

    /**
     * 根据请求方法查询日志
     * @param requestMethod 请求方法
     * @return 日志列表
     */
    List<ApiLog> selectByRequestMethod(@Param("requestMethod") String requestMethod);

    /**
     * 查询指定时间范围内的日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志列表
     */
    List<ApiLog> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 统计成功请求数量
     * @return 成功请求数量
     */
    Long countSuccessRequests();

    /**
     * 统计失败请求数量
     * @return 失败请求数量
     */
    Long countErrorRequests();

    /**
     * 计算平均响应时间
     * @return 平均响应时间(毫秒)
     */
    Double calculateAvgResponseTime();
} 