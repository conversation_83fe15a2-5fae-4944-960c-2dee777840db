package com.siteweb.tcs.s6.access.connector.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceChange;
import com.siteweb.tcs.hub.util.TCSTopicUtil;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description: 北向S6设备状态变化处理器
 * @author: xsx
 * @create: 2025-08-28 10:15
 **/

public class NorthS6DeviceChangeProcessor extends ProbeActor {

    private final ActorRef mediator;

    private ActorRef sitewebRedisSink;

    public static Props props(GatewayMap gatewayMap, ActorRef sitewebRedisSink){
        return Props.create(NorthS6GatewayChangeProcessor.class,gatewayMap,sitewebRedisSink);
    }


    private NorthS6DeviceChangeProcessor(GatewayMap gatewayMap,ActorRef sitewebRedisSink){
        this.mediator = DistributedPubSub.get(getContext().system()).mediator();
        this.sitewebRedisSink = sitewebRedisSink;
        handlerSubscribe(gatewayMap.getGatewayId());
        handleInit(gatewayMap);
    }

    private void handlerSubscribe(Long gatewayGuid) {
        String topicName = TCSTopicUtil.getTopicName(SubscriptionTypeEnum.DEVICE_STATE_CHANGE, gatewayGuid);
        mediator.tell(new DistributedPubSubMediator.Subscribe(topicName, getSelf()), getSelf());
    }

    private void handleInit(GatewayMap gatewayMap) {
        //初始化数据
    }

    @Override
    public Receive createReceive() {
        Receive receive = ReceiveBuilder.create().match(DeviceChange.class, this::onDeviceChange).build();
        return super.createReceive().orElse(receive);
    }

    private void onDeviceChange(DeviceChange deviceChange) {

    }
}
