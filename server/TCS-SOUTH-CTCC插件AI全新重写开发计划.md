# TCS-SOUTH-CTCC插件AI全新重写开发计划

## 1. 项目概述

### 1.1 开发策略
**完全重新开发**，而非基于CMCC插件改造。通过精准参考CMCC插件的架构模式和CTCC技术规范，从零开始构建全新的CTCC插件。

### 1.2 核心目标
- 严格按照《中国电信动环监控系统B接口技术规范20230515》实现
- 参考CMCC插件的成熟架构模式，但不直接修改其代码
- 确保代码的独立性和规范性，避免改造带来的潜在问题

---

## 2. 核心参考资源分析

### 2.1 CMCC插件架构可借鉴组件

#### 2.1.1 插件基础架构 (完全借鉴)
**参考文件**: `SouthCmccPlugin.java`
```java
// 核心架构模式
public class SouthCmccPlugin extends SouthPlugin {
    @Autowired private ConnectorDataHolder dataHolder;
    @Autowired private MessageSource messageSource;
    @Autowired private ServiceRegistry serviceRegistry;
    @Autowired private CMCCWebService webService;
    @Autowired private FsuLauncher fsuLaunchService;
}
```
**借鉴要点**:
- 继承SouthPlugin基类的模式
- Spring依赖注入的组织方式
- WebService和启动器的架构设计
- 数据持有器模式

#### 2.1.2 消息基础架构 (架构借鉴，内容重写)
**参考文件**: `MobileBMessage.java`, `MobileBRequestMessage.java`, `MobileBResponseMessage.java`
```java
// 消息架构模式
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class MobileBMessage extends StreamMessage {
    @JsonProperty("PK_Type")
    @JacksonXmlProperty(localName = "PK_Type")
    private PK_Type pkType = new PK_Type();
    
    public String toSCResponseXml() { /* SOAP封装 */ }
    public String toSURequestXml() { /* SOAP封装 */ }
}
```
**借鉴要点**:
- Jackson XML序列化架构
- SOAP消息封装机制
- 请求/响应消息分离设计
- 消息类型管理模式

#### 2.1.3 消息处理器架构 (架构借鉴，逻辑重写)
**参考文件**: `CmccFsuMessageProcessor.java`
```java
// 处理器架构模式
@Component
public class CmccFsuMessageProcessor {
    // 1. ACK响应处理方法组
    public void handleTimeCheckAck(...) { }
    public void handleGetFsuInfoAck(...) { }
    
    // 2. 用户命令处理方法组
    public void processUserCommand(...) { }
    
    // 3. FTP命令处理方法组
    public void handleFTPCommand(...) { }
    
    // 4. HTTP请求处理方法组
    private CompletableFuture<String> sendHttpRequest(...) { }
}
```
**借鉴要点**:
- 消息处理器的模块化分组
- 异步处理机制 (CompletableFuture)
- 统一的错误处理模式
- FTP和HTTP的集成处理

### 2.2 CTCC规范核心要求

#### 2.2.1 消息类型定义 (27种消息类型)
**规范来源**: 技术规范第6节
```
网络联接参数设置: LOGIN(101) ↔ LOGIN_ACK(102), SUREADY(103) ↔ SUREADY_ACK(104), ...
标准化配置文件: ASK_SCHEMECONFIG(201) ↔ ASK_SCHEMECONFIG_ACK(202), ...  
厂家配置文件: ASK_FACTORYCONFIG(301) ↔ ASK_FACTORYCONFIG_ACK(302), ...
监控点配置: GET_SPCONFIGOPTION(401) ↔ GET_SPCONFIGOPTION_ACK(402), ...
实时数据: GET_DATA(501) ↔ GET_DATA_ACK(502), ASK_TODAYHISDATA(503) ↔ ASK_TODAYHISDATA_ACK(504)
告警信息: SEND_ALARM(601) ↔ SEND_ALARM_ACK(602), GET_ACTIVEALARM(603) ↔ GET_ACTIVEALARM_ACK(604)
控制命令: SET_RMCTRLCMD(701) ↔ SET_RMCTRLCMD_ACK(702)
系统辅助: SET_TIME(901) ↔ SET_TIME_ACK(902), GET_SUINFO(1001) ↔ GET_SUINFO_ACK(1002), SET_SUREBOOT(1101) ↔ SET_SUREBOOT_ACK(1102)
智能门禁: GET_SmartDOOR(1201) ↔ GET_SmartDOOR_ACK(1202), SET_SmartDOOR(1203) ↔ SET_SmartDOOR_ACK(1204), SEND_SmartDOOR(1205) ↔ SEND_SmartDOOR_ACK(1206), SEND_DOOREvent(1207) ↔ SEND_DOOREvent_ACK(1208)
```

#### 2.2.2 关键数据结构
**规范来源**: 技术规范第5.5.4节
```
TTime: Year, Month, Day, Hour, Minute, Second
TAlarm: SerialNo, SUID, DeviceID, SPID, StartTime, EndTime, TriggerVal, AlarmLevel, AlarmFlag, AlarmDesc, AlarmFriDesc
TSemaphore: Type, SPID, MeasuredVal, Meanings, ReportTime, Status
SOResource: SOName, SO_Manufac, SO_Model, SO_OutputCapacity, SO_OutputUnit, SO_StartTime
SmartDoorValue: DeviceID, DoorType, CardType, SmartDoorDes
SDoorAuthData: CardNumber, CardCode, StartDate, EndDate, OtherData
SDoorEvent: SerialNo, DeviceID, EventType, EventTime, CardNumber, CardCode, EventDes
TSUStatus: CPUUsage, MEMUsage, SUDateTime
```

#### 2.2.3 关键枚举定义
**规范来源**: 技术规范第5.5.3节
```
EnumType: TI=1, DI=2, AI=3, DO=4, AO=5
EnumFailureCode: USERNAME_ERROR=1, PASSWORD_ERROR=2, SUID_ERROR=3, ..., OTHER_ERROR=9999 (24种)
EnumAlarmLevel: NOALARM=0, CRITICAL=1, MAJOR=2, MINOR=3, HINT=4
EnumState: NOALARM=0, CRITICAL=1, MAJOR=2, MINOR=3, HINT=4, OPEVENT=5, INVALID=6
```

---

## 3. 详细重写开发计划

### 3.1 第一阶段：项目基础搭建 (第1周 - 40小时)

#### 任务3.1.1: 创建全新项目结构
**工作量**: 16小时
**参考**: CMCC插件的Maven项目结构和Spring配置模式

**AI提示词模板**:
```
# 创建全新的TCS-SOUTH-CTCC插件项目结构

## 任务目标
从零开始创建一个完整的TCS-SOUTH-CTCC插件项目，不复制任何现有代码。

## 参考CMCC插件的项目结构模式
请参考以下CMCC插件的项目组织方式，但创建全新的CTCC版本：

### Maven项目结构
```
server/tcs-south-ctcc/
├── pom.xml
├── src/
│   ├── main/
│   │   ├── java/com/siteweb/tcs/south/ctcc/
│   │   │   ├── SouthCtccPlugin.java (主插件类)
│   │   │   ├── CtccWebService.java (WebService实现)
│   │   │   ├── SuLauncher.java (SU启动器)
│   │   │   ├── config/ (配置类)
│   │   │   ├── connector/ (连接器)
│   │   │   │   ├── protocol/ (协议定义)
│   │   │   │   ├── letter/ (消息类)
│   │   │   │   ├── process/ (消息处理)
│   │   │   │   ├── commands/ (用户命令)
│   │   │   │   └── ftp/ (FTP处理)
│   │   │   ├── dal/ (数据访问层)
│   │   │   └── util/ (工具类)
│   │   └── resources/
│   │       ├── application.yml
│   │       ├── db/south-ctcc-plugin/migration/
│   │       ├── i18n/
│   │       └── META-INF/
│   └── test/ (测试代码)
```

### 核心配置要求
1. **Maven配置**: artifactId为tcs-south-ctcc，groupId保持一致
2. **Spring配置**: 包扫描路径为com.siteweb.tcs.south.ctcc
3. **插件配置**: 插件ID为south-ctcc-plugin
4. **数据库配置**: 表前缀为ctcc_

## 实现要求
1. 创建完整的目录结构，但暂时不实现具体功能
2. 配置正确的Maven依赖（参考CMCC但不复制）
3. 设置Spring Boot基础配置
4. 确保项目能够编译通过

## 输出要求
1. 完整的pom.xml文件
2. 基础的application.yml配置
3. 空的主类文件（仅包含类定义和注解）
4. Maven编译验证成功的确认
```

#### 任务3.1.2: 实现插件基础框架
**工作量**: 16小时
**参考**: `SouthCmccPlugin.java`的架构模式

**AI提示词模板**:
```
# 实现CTCC插件基础框架

## 参考架构模式
参考CMCC插件的基础架构，但针对CTCC规范全新实现：

### CMCC插件基础架构模式 (仅参考架构，不复制代码)
```java
@Slf4j
public class SouthCmccPlugin extends SouthPlugin {
    @Autowired private ConnectorDataHolder dataHolder;
    @Autowired private MessageSource messageSource; 
    @Autowired private ServiceRegistry serviceRegistry;
    @Autowired private CMCCWebService webService;
    @Autowired private FsuLauncher fsuLaunchService;
}
```

## CTCC插件实现要求
### 1. 主插件类 (SouthCtccPlugin)
- 继承SouthPlugin基类
- 实现CTCC特定的初始化逻辑
- 配置Spring依赖注入
- 实现插件生命周期管理

### 2. WebService实现 (CtccWebService)
- 基于CTCC B接口规范实现WebService
- 支持27种消息类型处理
- 实现SOAP消息解析和响应

### 3. SU启动器 (SuLauncher) 
- 管理SU设备的连接和状态
- 实现心跳机制
- 处理SU注册和认证

### 4. 数据持有器 (ConnectorDataHolder)
- 管理CTCC协议的配置数据
- 缓存SU设备信息
- 提供数据访问接口

## 技术要求
1. 使用Spring Boot框架
2. 支持PF4J插件机制
3. 集成Pekko Actor系统
4. 支持多数据库访问

## 输出要求
1. SouthCtccPlugin主类实现
2. CtccWebService基础框架
3. SuLauncher连接管理器
4. ConnectorDataHolder数据管理器
5. 相关的Spring配置类
```

#### 任务3.1.3: 设计数据库结构
**工作量**: 8小时
**参考**: CMCC插件的数据库设计思路

**AI提示词模板**:
```
# 设计CTCC插件数据库结构

## 参考CMCC数据库设计思路
CMCC插件使用以下表结构模式（仅参考设计思路）：
- 设备管理表：存储FSU基础信息
- 信号点表：存储监控点配置
- 告警表：存储告警历史记录
- 控制表：存储控制命令记录

## CTCC数据库设计要求
根据CTCC B接口技术规范，设计以下表结构：

### 1. ctcc_sus (SU基础信息表)
字段基于LOGIN消息结构：
- suid varchar(17) - SU编码
- su_ip varchar(15) - SU IP地址  
- su_port int - SU WebService端口
- su_vendor varchar(50) - SU制造商
- su_model varchar(50) - SU型号
- su_hard_ver varchar(20) - 硬件版本
- su_soft_ver varchar(20) - 软件版本
- created_time timestamp
- updated_time timestamp

### 2. ctcc_devices (设备信息表)
字段基于DeviceID规范：
- device_id varchar(7) - 设备编码
- suid varchar(17) - 所属SU
- so_name varchar(30) - 设备名称
- so_manufac varchar(30) - 制造商
- so_model varchar(30) - 设备型号
- so_output_capacity float - 设备容量
- so_output_unit varchar(10) - 容量单位
- so_start_time date - 投产日期

### 3. ctcc_signals (监控点信息表)
字段基于TSemaphore结构：
- spid varchar(12) - 监控点ID
- device_id varchar(7) - 所属设备
- signal_type int - 信号类型(TI=1,DI=2,AI=3,DO=4,AO=5)
- measured_val float - 当前值
- meanings int - 信号意义
- report_time timestamp - 上报时间
- status int - 状态

### 4. ctcc_alarms (告警信息表)  
字段基于TAlarm结构：
- serial_no varchar(10) - 告警序号
- suid varchar(17) - SU编码
- device_id varchar(7) - 设备编码
- spid varchar(12) - 监控点ID
- start_time timestamp - 开始时间
- end_time timestamp - 结束时间
- trigger_val float - 触发值
- alarm_level int - 告警级别
- alarm_flag int - 告警标志
- alarm_desc varchar(200) - 告警描述
- alarm_fri_desc varchar(200) - 详细描述

### 5. ctcc_smart_doors (智能门禁表) - CTCC特有
- device_id varchar(7) - 门禁设备编码
- door_type varchar(60) - 门禁类型
- card_type smallint - 读卡字节数
- smart_door_des varchar(200) - 设备描述

### 6. ctcc_door_auth (门禁授权表) - CTCC特有
- card_number bigint - 卡序号
- device_id varchar(7) - 门禁设备
- card_code varchar(16) - 卡号
- start_date timestamp - 授权开始时间
- end_date timestamp - 授权结束时间
- other_data varchar(200) - 其他识别信息

### 7. ctcc_door_events (门禁事件表) - CTCC特有
- serial_no varchar(10) - 事件序号
- device_id varchar(7) - 门禁设备
- event_type smallint - 事件类型
- event_time timestamp - 事件时间
- card_number bigint - 卡序号
- card_code varchar(16) - 卡号
- event_des varchar(200) - 事件描述

## 输出要求
1. 完整的MySQL DDL脚本
2. PostgreSQL兼容版本
3. H2数据库版本（测试用）
4. Flyway迁移脚本
5. 索引和约束定义
```

### 3.2 第二阶段：协议定义层全新实现 (第2-3周 - 80小时)

#### 任务3.2.1: 实现CTCC枚举定义
**工作量**: 20小时
**参考**: CMCC枚举类的实现模式，但枚举值完全按CTCC规范

**AI提示词模板**:
```
# 实现CTCC协议枚举定义

## 参考CMCC枚举实现模式
CMCC使用以下枚举实现模式（仅参考模式，不复制值）：
```java
@Getter
public enum EnumResult {
    FAILURE(0, "失败"), SUCCESS(1, "成功");
    
    @JsonValue @EnumValue private final int code;
    private final String description;
    
    @JsonCreator
    public static EnumResult getByCode(int code) {
        // 查找逻辑
    }
}
```

## CTCC枚举实现要求
严格按照CTCC B接口技术规范实现以下枚举：

### 1. EnumResult (报文返回结果)
```
FAILURE = 0 (失败)
SUCCESS = 1 (成功)
```

### 2. EnumFailureCode (失败原因 - CTCC特有的24种)
```
USERNAME_ERROR = 1 (用户名错)
PASSWORD_ERROR = 2 (密码错)  
SUID_ERROR = 3 (错误的SUID)
DEVICEID_ERROR = 4 (错误的广义设备ID)
SPID_ERROR = 5 (错误的SPID)
IP_ERROR = 6 (IP错误)
NOFILE_ERROR = 7 (没有文件)
CONFIG_CHECK_ERROR = 8 (配置验证失败)
CONFIG_ERROR = 9 (配置方案选项值超出范围)
DATA_FORMAT_ERROR = 10 (数据格式错)
CTRL_TIMEOUT = 11 (控制超时)
CTRL_PARA_ERROR = 12 (控制参数错)
IP_OUTOFACL_ERROR = 13 (IP不在ACL范围)
NOFILEDIR_ERROR = 14 (文件目录不存在)
AlarmTime_NULL = 15 (告警开始时间为空或年份错误)
AlarmTrigger_NULL = 16 (告警触发值为空)
AlarmLevel_NULL = 17 (告警级别为空或不规范)
AlarmFlag_NULL = 18 (告警标识为空或不规范)
AlarmNum_ERROR = 19 (告警信息数量错误或为空)
AlarmSN_ERROR = 20 (告警序号错误)
Alarm_ERROR = 21 (告警标识为END但结束时间为空)
Alarm_Sending_Failed = 22 (FSU向SC发送告警失败)
Alarm_Failed = 23 (SU存在发送失败的告警)
OTHER_ERROR = 9999 (其他错误)
```

### 3. EnumType (监控点种类 - 与CMCC不同)
```
TI = 1 (阈值生成信号)
DI = 2 (遥信信号)
AI = 3 (遥测信号)  
DO = 4 (遥控信号)
AO = 5 (遥调信号)
```

### 4. EnumAlarmLevel (告警事件等级)
```
NOALARM = 0 (无告警)
CRITICAL = 1 (一级告警)
MAJOR = 2 (二级告警)
MINOR = 3 (三级告警)
HINT = 4 (四级告警)
```

### 5. EnumState (监控点数据状态)
```
NOALARM = 0 (正常数据)
CRITICAL = 1 (一级告警)
MAJOR = 2 (二级告警)
MINOR = 3 (三级告警)
HINT = 4 (四级告警)
OPEVENT = 5 (操作事件)
INVALID = 6 (无效数据)
```

### 6. EnumFlag (告警事件起始/结束标志)
```
BEGIN = 0 (开始)
END = 1 (结束)
```

### 7. EnumConfigOption (监控点配置模板选项)
```
ONE = 1 (配置方案1)
TWO = 2 (配置方案2)
THREE = 3 (配置方案3)
FOUR = 4 (配置方案4)
```

### 8. EnumDeviceMeanings (信号意义定义)
```
Meanings = 0 (遥信量"正常")
Meanings = 1 (遥信量"告警")
Meanings = 2 (遥控量"通")
Meanings = 3 (遥控量"断")
Meanings = 4 (遥控量"运行")
Meanings = 5 (遥控量"停止")
Meanings = 6 (遥调量"赋值")
Meanings = 7 (遥调量"增加")
Meanings = 8 (遥调量"减少")
```

## 实现要求
1. 每个枚举类都要包含中文描述
2. 支持Jackson JSON和XML序列化
3. 支持MyBatis Plus数据库映射
4. 提供getByCode静态查找方法
5. 包含完整的Javadoc注释

## 输出要求
为每个枚举提供完整的Java类实现
```

#### 任务3.2.2: 实现CTCC数据结构定义
**工作量**: 24小时
**参考**: CMCC数据结构的Jackson XML注解模式

**AI提示词模板**:
```
# 实现CTCC数据结构定义

## 参考CMCC数据结构实现模式
参考CMCC的Jackson XML注解使用方式（仅参考注解模式）：
```java
@Data
public class TAlarm {
    @JsonProperty("SerialNo")
    @JacksonXmlProperty(localName = "SerialNo", isAttribute = true)
    private String serialNo;
    
    @JsonProperty("AlarmLevel")
    @JacksonXmlProperty(localName = "AlarmLevel", isAttribute = true) 
    private EnumState alarmLevel;
}
```

## CTCC数据结构实现要求
严格按照CTCC B接口技术规范第5.5.4节实现以下数据结构：

### 1. TTime (时间结构)
```java
// 字段：Year, Month, Day, Hour, Minute, Second
// 类型：全部为short
// 用途：标准时间表示结构
```

### 2. TAlarm (当前告警数据结构 - 11个字段)
```java
// SerialNo: char[SERIALNO_LEN] - SU告警序号
// SUID: char[SUID_LEN] - SU编码  
// DeviceID: char[DEVICEID_LEN] - 设备编码
// SPID: char[SPID_LENGTH] - 监控点ID
// StartTime: char[TIME_LEN] - 告警开始时间，YYYY-MM-DD hh:mm:ss
// EndTime: char[TIME_LEN] - 告警结束时间，YYYY-MM-DD hh:mm:ss
// TriggerVal: Float - 告警触发值
// AlarmLevel: EnumAlarmLevel - 告警级别
// AlarmFlag: EnumFlag - 告警标志
// AlarmDesc: char[DES_LENGTH] - 告警事件标准文本
// AlarmFriDesc: char[DES_LENGTH] - 告警详细描述
```

### 3. TSemaphore (实时数据/控制命令值结构 - 6个字段)
```java
// Type: EnumType - 数据类型
// SPID: char[SPID_LENGTH] - 监控点ID
// MeasuredVal: float - 实测值/控制值
// Meanings: EnumDeviceMeanings - 信号意义
// ReportTime: char[TIME_LEN] - 实时数据时间，YYYY-MM-DD hh:mm:ss
// Status: EnumState - 状态
```

### 4. SOResource (监控对象资源信息 - CTCC特有，6个字段)
```java
// SOName: char[30] - 设备名称
// SO_Manufac: char[30] - 设备制造商名称
// SO_Model: char[30] - 设备型号
// SO_OutputCapacity: float - 设备容量
// SO_OutputUnit: char[10] - 设备容量单位
// SO_StartTime: char[10] - 设备投产日期，YYYY-MM-DD
```

### 5. SmartDoorValue (智能门禁配置信息 - CTCC特有，4个字段)
```java
// DeviceID: char[DEVICEID_LEN] - 智能门禁设备编码
// DoorType: char[60] - 智能门禁类型
// CardType: short - 智能门禁卡读卡字节数
// SmartDoorDes: char[DES_LENGTH] - 智能门禁设备详细描述
```

### 6. SDoorAuthData (智能门禁授权信息 - CTCC特有，5个字段)
```java
// CardNumber: long - 卡序号，在门禁设备内唯一
// CardCode: char[16] - 智能卡卡号，在门禁设备内唯一
// StartDate: char[TIME_LEN] - 授权开始时间，YYYY-MM-DD hh:mm:ss
// EndDate: char[TIME_LEN] - 授权结束时间，YYYY-MM-DD hh:mm:ss
// OtherData: char[DES_LENGTH] - 指纹、身份证等其他识别信息
```

### 7. SDoorEvent (智能门禁事件信息 - CTCC特有，7个字段)
```java
// SerialNo: char[SERIALNO_LEN] - 智能门禁事件序号
// DeviceID: char[DEVICEID_LEN] - 智能门禁设备编码
// EventType: short - 智能门禁事件类型
// EventTime: char[TIME_LEN] - 事件发生时间，YYYY-MM-DD hh:mm:ss
// CardNumber: long - 卡序号，在门禁设备内唯一
// CardCode: char[16] - 智能卡卡号，在门禁设备内唯一  
// EventDes: char[DES_LENGTH] - 智能门禁事件详细描述
```

### 8. TSUStatus (SU状态参数 - CTCC特有，3个字段)
```java
// CPUUsage: float - CPU使用率
// MEMUsage: float - 内存使用率
// SUDateTime: char[TIME_LEN] - SU本地时间，YYYY-MM-DD hh:mm:ss
```

## 常量定义 (技术规范第5.5.2节)
```java
// NAME_LENGTH = 40字节 - 名字命名长度
// USER_LENGTH = 20字节 - 用户名长度
// PASSWORD_LEN = 20字节 - 口令长度
// DES_LENGTH = 200字节 - 告警描述信息长度
// SUID_LEN = 17字节 - SU编码长度
// DEVICEID_LEN = 7字节 - 设备编码长度
// SPID_LENGTH = 12字节 - 监控点ID长度
// IP_LENGTH = 15字节 - IP串长度
// SERIALNO_LEN = 10字节 - 告警序号长度
// TIME_LEN = 19字节 - 时间串长度
// FILENAME_LEN = 80字节 - 文件名长度
// FAILURE_CAUSE_LEN = 40字节 - 失败原因描述长度
```

## 实现要求
1. 使用Jackson XML注解支持XML序列化
2. 使用Lombok简化getter/setter
3. 添加字段验证注解 (@Size, @NotNull等)
4. 包含完整的Javadoc文档
5. 字段长度严格按照规范限制

## 输出要求
为每个数据结构提供完整的Java类实现，包括所有字段和注解
```

#### 任务3.2.3: 实现CTCC消息类型枚举
**工作量**: 16小时
**参考**: CMCC的`PK_TypeName.java`模式

**AI提示词模板**:
```
# 实现CTCC消息类型枚举

## 参考CMCC消息类型实现模式
CMCC使用以下模式（仅参考架构模式）：
```java
public enum PK_TypeName {
    LOGIN_ACK(), LOGIN(LOGIN_ACK),
    SEND_ALARM_ACK, SEND_ALARM(SEND_ALARM_ACK);
    
    @Getter private final PK_TypeName ack;
    public Boolean isAck() { return ack == null; }
}
```

## CTCC消息类型实现要求
严格按照CTCC B接口技术规范第6节定义，实现27种消息类型：

### 网络联接参数设置 (101-110)
```
LOGIN(101) ↔ LOGIN_ACK(102)
SUREADY(103) ↔ SUREADY_ACK(104)  
SET_SCIP(105) ↔ SET_SCIP_ACK(106)
GET_SUFTP(107) ↔ GET_SUFTP_ACK(108)
SET_SUFTP(109) ↔ SET_SUFTP_ACK(110)
```

### 标准化配置文件 (201-206)
```
ASK_SCHEMECONFIG(201) ↔ ASK_SCHEMECONFIG_ACK(202)
GET_SCHEMECONFIG(203) ↔ GET_SCHEMECONFIG_ACK(204)
SET_SCHEMECONFIG(205) ↔ SET_SCHEMECONFIG_ACK(206)
```

### 厂家配置文件 (301-308)  
```
ASK_FACTORYCONFIG(301) ↔ ASK_FACTORYCONFIG_ACK(302)
SEND_FACTORYCONFIG(303) ↔ SEND_FACTORYCONFIG_ACK(304)
GET_FACTORYCONFIG(305) ↔ GET_FACTORYCONFIG_ACK(306)
SET_FACTORYCONFIG(307) ↔ SET_FACTORYCONFIG_ACK(308)
```

### FSU监控点标准化配置模板选型 (401-404)
```
GET_SPCONFIGOPTION(401) ↔ GET_SPCONFIGOPTION_ACK(402)
SET_SPCONFIGOPTION(403) ↔ SET_SPCONFIGOPTION_ACK(404)
```

### 实时监测数据与当天历史监测数据 (501-504)
```
GET_DATA(501) ↔ GET_DATA_ACK(502)
ASK_TODAYHISDATA(503) ↔ ASK_TODAYHISDATA_ACK(504)
```

### 告警信息 (601-604)
```
SEND_ALARM(601) ↔ SEND_ALARM_ACK(602)
GET_ACTIVEALARM(603) ↔ GET_ACTIVEALARM_ACK(604)
```

### 控制（遥调遥控）命令 (701-702)
```
SET_RMCTRLCMD(701) ↔ SET_RMCTRLCMD_ACK(702)
```

### 系统或辅助命令 (901-1102)
```
SET_TIME(901) ↔ SET_TIME_ACK(902)
GET_SUINFO(1001) ↔ GET_SUINFO_ACK(1002)
SET_SUREBOOT(1101) ↔ SET_SUREBOOT_ACK(1102)
```

### 智能门禁 (1201-1208) - CTCC特有
```
GET_SmartDOOR(1201) ↔ GET_SmartDOOR_ACK(1202)
SET_SmartDOOR(1203) ↔ SET_SmartDOOR_ACK(1204)
SEND_SmartDOOR(1205) ↔ SEND_SmartDOOR_ACK(1206)
SEND_DOOREvent(1207) ↔ SEND_DOOREvent_ACK(1208)
```

## 实现要求
1. 每个消息类型包含数字代码(Code)
2. 支持请求-响应消息配对
3. 提供isAck()方法判断消息类型
4. 支持按名称和代码查找消息类型
5. 兼容Jackson序列化

## 额外功能要求
```java
public enum CtccMessageType {
    LOGIN(101, LOGIN_ACK),
    LOGIN_ACK(102, null);
    
    private final int code;
    private final CtccMessageType ackType;
    
    // 按代码查找
    public static CtccMessageType getByCode(int code);
    
    // 按名称查找  
    public static CtccMessageType getByName(String name);
    
    // 获取对应的ACK消息类型
    public CtccMessageType getAckType();
}
```

## 输出要求
完整的消息类型枚举定义，包含所有27种CTCC消息类型
```

#### 任务3.2.4: 实现CTCC消息类定义
**工作量**: 20小时
**参考**: CMCC的消息基础架构模式

**AI提示词模板**:
```
# 实现CTCC消息类定义

## 参考CMCC消息基础架构
参考CMCC消息的基础架构模式（仅参考架构，不复制内容）：
```java
// 基础消息类
@JsonIgnoreProperties(ignoreUnknown = true)  
public abstract class CtccMessage extends StreamMessage {
    @JsonProperty("PK_Type")
    @JacksonXmlProperty(localName = "PK_Type")
    private PK_Type pkType = new PK_Type();
    
    public String toSCResponseXml() { /* SOAP封装 */ }
    public String toSURequestXml() { /* SOAP封装 */ }
}

// 请求消息基类
@JacksonXmlRootElement(localName = "Request")
public abstract class CtccRequestMessage extends CtccMessage { }

// 响应消息基类  
@JacksonXmlRootElement(localName = "Response")
public abstract class CtccResponseMessage extends CtccMessage { }
```

## CTCC消息实现要求
基于CTCC B接口技术规范，实现关键消息类定义：

### 1. LOGIN消息 (代码101)
**规范来源**: 技术规范6.1节
```xml
<Request>
    <PK_Type>
        <Name>LOGIN</Name>
        <Code>101</Code>
    </PK_Type>
    <Info>
        <UserName/> <!-- USER_LENGTH(20字节) -->
        <Password/> <!-- PASSWORD_LEN(20字节) -->
        <SUID/>     <!-- char[SUID_LEN](17字节) -->
        <SUIP/>     <!-- Char[IP_LENGTH](15字节) -->
        <SUPort/>   <!-- Sizeof(long) -->
        <SUVendor/> <!-- char[VENDOR_LENGTH] -->
        <SUModel/>  <!-- char[MODEL_LENGTH] -->
        <SUHardVer/> <!-- char[VER_LEN] -->
        <SUSoftVer/> <!-- char[VER_LEN] -->
    </Info>
</Request>
```

### 2. LOGIN_ACK消息 (代码102)
```xml
<Response>
    <PK_Type>
        <Name>LOGIN_ACK</Name>
        <Code>102</Code>
    </PK_Type>
    <Info>
        <SUID/>
        <SCIP/>   <!-- SC采集机IP地址 -->
        <SCPort/> <!-- SC采集机端口 -->
        <Result/> <!-- EnumResult -->
        <FailureCode/> <!-- EnumFailureCode，Result为FAILURE时 -->
        <FailureCause/> <!-- 失败原因描述 -->
    </Info>
</Response>
```

### 3. SEND_ALARM消息 (代码601)
**规范来源**: 技术规范6.17节
```xml
<Request>
    <PK_Type>
        <Name>SEND_ALARM</Name>
        <Code>601</Code>
    </PK_Type>
    <Info>
        <SUID/>
        <n*TAlarm>
            <!-- 多个TAlarm结构 -->
        </n*TAlarm>
    </Info>
</Request>
```

### 4. GET_DATA消息 (代码501)
```xml
<Request>
    <PK_Type>
        <Name>GET_DATA</Name>
        <Code>501</Code>
    </PK_Type>
    <Info>
        <SUID/>
        <n*Device DeviceID="xxx">
            <m*SPID>xxx</m*SPID>
        </n*Device>
    </Info>
</Request>
```

### 5. GET_SmartDOOR消息 (代码1201) - CTCC特有
```xml
<Request>
    <PK_Type>
        <Name>GET_SmartDOOR</Name>
        <Code>1201</Code>
    </PK_Type>
    <Info>
        <SUID/>
        <n*DeviceID>xxx</n*DeviceID>
    </Info>
</Request>
```

## 分批实现建议
### 第一批：基础连接消息 (LOGIN、SUREADY、SET_SCIP)
### 第二批：数据获取消息 (GET_DATA、ASK_TODAYHISDATA)
### 第三批：告警消息 (SEND_ALARM、GET_ACTIVEALARM)  
### 第四批：配置消息 (ASK_SCHEMECONFIG、GET_SPCONFIGOPTION等)
### 第五批：智能门禁消息 (GET_SmartDOOR、SET_SmartDOOR等)

## 实现要求
1. 严格按照CTCC规范的XML结构
2. 使用Jackson XML注解
3. 包含Code字段（与CMCC的主要区别）
4. 支持SOAP消息封装
5. 字段验证和长度限制

## 输出要求  
每批提供完整的消息类Java实现，包括Request和Response消息
```

### 3.3 第三阶段：业务逻辑层全新实现 (第4-5周 - 80小时)

#### 任务3.3.1: 实现CTCC消息处理器
**工作量**: 40小时
**参考**: `CmccFsuMessageProcessor.java`的架构模式

**AI提示词模板**:
```
# 实现CTCC消息处理器

## 参考CMCC消息处理器架构模式
参考CMCC处理器的架构组织方式（仅参考架构，不复制逻辑）：
```java
@Component
public class CmccFsuMessageProcessor {
    // 分组处理模式：
    // 1. ACK响应处理方法组
    // 2. 用户命令处理方法组  
    // 3. FTP命令处理方法组
    // 4. HTTP请求处理方法组
    // 5. 异步处理机制 (CompletableFuture)
}
```

## CTCC消息处理器实现要求
创建全新的CtccSuMessageProcessor，实现27种消息类型的处理：

### 1. 网络连接管理组
```java
// 处理LOGIN消息 (101)
public LoginAckMessage handleLogin(LoginMessage message) {
    // 1. 验证用户名密码
    // 2. 验证SUID格式和权限
    // 3. 记录SU注册信息  
    // 4. 返回SC采集机信息
    // 5. 支持EnumFailureCode的24种错误返回
}

// 处理SUREADY消息 (103) 
public SureadyAckMessage handleSuready(SureadyMessage message) {
    // 1. 验证SU注册状态
    // 2. 确认SU准备就绪
    // 3. 返回验证结果
}

// 处理SET_SCIP消息 (105)
public SetScipAckMessage handleSetScip(SetScipMessage message) {
    // 1. 设置SC网络参数
    // 2. 更新连接配置
    // 3. 返回设置结果
}

// FTP参数管理 (107-110)
public GetSuftpAckMessage handleGetSuftp(GetSuftpMessage message);
public SetSuftpAckMessage handleSetSuftp(SetSuftpMessage message);
```

### 2. 配置管理组
```java  
// 标准化配置文件处理 (201-206)
public AskSchemeConfigAckMessage handleAskSchemeConfig(AskSchemeConfigMessage message) {
    // 1. 处理FSU请求标准化配置
    // 2. 通过FTP传输配置文件
    // 3. 返回传输结果
}

public GetSchemeConfigAckMessage handleGetSchemeConfig(GetSchemeConfigMessage message);
public SetSchemeConfigAckMessage handleSetSchemeConfig(SetSchemeConfigMessage message);

// 厂家配置文件处理 (301-308)  
public AskFactoryConfigAckMessage handleAskFactoryConfig(AskFactoryConfigMessage message);
public SendFactoryConfigAckMessage handleSendFactoryConfig(SendFactoryConfigMessage message);
public GetFactoryConfigAckMessage handleGetFactoryConfig(GetFactoryConfigMessage message);
public SetFactoryConfigAckMessage handleSetFactoryConfig(SetFactoryConfigMessage message);

// 监控点配置模板选项 (401-404)
public GetSpConfigOptionAckMessage handleGetSpConfigOption(GetSpConfigOptionMessage message);
public SetSpConfigOptionAckMessage handleSetSpConfigOption(SetSpConfigOptionMessage message);
```

### 3. 数据处理组
```java
// 实时数据获取 (501)
public GetDataAckMessage handleGetData(GetDataMessage message) {
    // 1. 解析设备ID和SPID列表
    // 2. 获取对应监控点的实时数据
    // 3. 构建TSemaphore数据结构
    // 4. 返回数据查询结果
}

// 历史数据获取 (503)
public AskTodayHisDataAckMessage handleAskTodayHisData(AskTodayHisDataMessage message) {
    // 1. 查询当天历史监测数据
    // 2. 通过FTP传输数据文件
    // 3. 返回文件传输状态
}
```

### 4. 告警处理组
```java
// 告警信息上报 (601)
public SendAlarmAckMessage handleSendAlarm(SendAlarmMessage message) {
    // 1. 解析TAlarm告警信息列表
    // 2. 验证告警数据完整性
    // 3. 存储告警记录
    // 4. 触发告警处理流程
    // 5. 支持EnumFailureCode错误处理
}

// 获取活动告警 (603)  
public GetActiveAlarmAckMessage handleGetActiveAlarm(GetActiveAlarmMessage message) {
    // 1. 查询当前所有活动告警
    // 2. 构建TAlarm结构列表
    // 3. 返回告警查询结果
}
```

### 5. 控制命令组
```java
// 遥调遥控命令 (701)
public SetRmCtrlCmdAckMessage handleSetRmCtrlCmd(SetRmCtrlCmdMessage message) {
    // 1. 解析控制命令参数
    // 2. 执行DO/AO控制操作
    // 3. 监控执行结果
    // 4. 返回控制执行状态
}
```

### 6. 系统辅助组
```java
// 时间同步 (901)
public SetTimeAckMessage handleSetTime(SetTimeMessage message) {
    // 1. 接收SC时间参数
    // 2. 同步SU本地时间
    // 3. 返回同步结果
}

// SU信息查询 (1001)
public GetSuInfoAckMessage handleGetSuInfo(GetSuInfoMessage message) {
    // 1. 获取TSUStatus状态信息
    // 2. 包含CPU、内存使用率
    // 3. 返回SU运行状态
}

// SU重启 (1101)
public SetSuRebootAckMessage handleSetSuReboot(SetSuRebootMessage message);
```

### 7. 智能门禁组 (CTCC特有)
```java
// 获取门禁配置 (1201)
public GetSmartDoorAckMessage handleGetSmartDoor(GetSmartDoorMessage message) {
    // 1. 查询SmartDoorValue配置信息
    // 2. 返回门禁设备配置
}

// 设置门禁配置 (1203)
public SetSmartDoorAckMessage handleSetSmartDoor(SetSmartDoorMessage message) {
    // 1. 设置SDoorAuthData授权信息
    // 2. 更新门禁配置
    // 3. 返回设置结果
}

// 门禁授权结果 (1205)  
public SendSmartDoorAckMessage handleSendSmartDoor(SendSmartDoorMessage message) {
    // 1. 处理门禁授权设置结果
    // 2. 记录授权状态
}

// 门禁事件上报 (1207)
public SendDoorEventAckMessage handleSendDoorEvent(SendDoorEventMessage message) {
    // 1. 处理SDoorEvent门禁事件
    // 2. 记录门禁事件日志
    // 3. 触发相关处理流程
}
```

## 技术实现要求
1. 使用Spring @Component注解
2. 异步处理机制 (CompletableFuture)
3. 完整的错误处理 (EnumFailureCode)
4. 日志记录和监控
5. 事务管理支持

## 输出要求
完整的CtccSuMessageProcessor类，包含所有27种消息的处理方法
```

#### 任务3.3.2: 实现FTP文件处理
**工作量**: 20小时
**参考**: CMCC的FTP处理架构

**AI提示词模板**:
```
# 实现CTCC FTP文件处理

## 参考CMCC FTP处理架构
参考CMCC的FTP处理模式（仅参考架构思路）：
```java
public class CmccFTPHelper {
    public CompletableFuture<List<ConfigDataFile>> getConfigDataAsync(CMCCFsu fsuInfo);
    public CompletableFuture<List<AlarmFile>> getAlarmFilesAsync(CMCCFsu fsuInfo, String date);
    public CompletableFuture<Boolean> uploadFileAsync(CMCCFsu fsuInfo, String fileName, byte[] fileData);
}
```

## CTCC FTP处理实现要求
根据CTCC B接口规范的FTP接口能力，实现文件传输管理：

### 1. CtccFtpHelper核心功能
```java
@Component  
public class CtccFtpHelper {
    
    // 标准化配置文件处理
    public CompletableFuture<Boolean> uploadSchemeConfigFiles(String suid, List<String> configFiles) {
        // 1. 连接SU的FTP服务
        // 2. 上传标准化配置文件
        // 3. 验证文件传输完整性
        // 4. 返回传输结果
    }
    
    public CompletableFuture<List<String>> downloadSchemeConfigFiles(String suid) {
        // 1. 从SU下载标准化配置文件
        // 2. 验证文件格式和内容
        // 3. 返回文件列表
    }
    
    // 厂家配置文件处理
    public CompletableFuture<Boolean> uploadFactoryConfigFiles(String suid, List<String> configFiles);
    public CompletableFuture<List<String>> downloadFactoryConfigFiles(String suid);
    
    // 历史数据文件处理
    public CompletableFuture<List<String>> downloadTodayHisDataFiles(String suid, String date) {
        // 1. 连接SU获取当天历史数据文件
        // 2. 按日期筛选数据文件
        // 3. 下载并解析数据文件
        // 4. 返回数据文件内容
    }
    
    // 告警同步文件处理
    public CompletableFuture<List<String>> getAlarmSyncFiles(String suid, String startDate, String endDate);
    
    // 监控图像文件处理
    public CompletableFuture<List<ImageFile>> getMonitorImages(String suid, String deviceId);
    
    // 日志文件获取
    public CompletableFuture<List<LogFile>> getLogFiles(String suid, String date);
}
```

### 2. FTP文件类型定义
```java
// 配置数据文件
@Data
public class ConfigDataFile {
    private String fileName;    // 文件名
    private String filePath;    // 文件路径  
    private String fileType;    // 配置文件类型
    private byte[] fileContent; // 文件内容
    private String checkSum;    // 文件校验码
}

// 告警同步文件
@Data  
public class AlarmSyncFile {
    private String fileName;
    private String suid;
    private String syncDate;    // 同步日期
    private List<TAlarm> alarms; // 告警列表
}

// 历史数据文件
@Data
public class HisDataFile {
    private String fileName;
    private String suid;
    private String dataDate;    // 数据日期
    private List<TSemaphore> dataPoints; // 历史数据点
}

// 图像文件
@Data
public class ImageFile {
    private String fileName;
    private String deviceId;
    private String imageType;   // 图像类型
    private byte[] imageData;   // 图像数据
}

// 日志文件  
@Data
public class LogFile {
    private String fileName;
    private String suid;
    private String logDate;     // 日志日期
    private String logLevel;    // 日志级别
    private String logContent;  // 日志内容
}
```

### 3. FTP连接管理
```java
@Component
public class CtccFtpConnectionManager {
    
    public FTPClient createConnection(String suid) {
        // 1. 获取SU的FTP连接参数
        // 2. 建立FTP连接
        // 3. 进行身份认证
        // 4. 返回FTP客户端
    }
    
    public void closeConnection(FTPClient ftpClient) {
        // 1. 关闭FTP连接
        // 2. 释放资源
    }
    
    public boolean testConnection(String suid) {
        // 1. 测试与SU的FTP连接
        // 2. 返回连接状态
    }
    
    private ConnectionPool<FTPClient> connectionPool;
    
    public FTPClient borrowConnection(String suid) {
        // 1. 从连接池获取FTP连接
        // 2. 验证连接有效性
    }
    
    public void returnConnection(String suid, FTPClient client) {
        // 1. 将连接归还连接池
    }
}
```

### 4. FTP文件操作工具
```java
@Component
public class CtccFtpFileOperator {
    
    public boolean uploadFile(FTPClient ftpClient, String remotePath, String fileName, byte[] fileData) {
        // 1. 上传文件到指定路径
        // 2. 验证上传结果
        // 3. 返回操作状态
    }
    
    public byte[] downloadFile(FTPClient ftpClient, String remotePath, String fileName) {
        // 1. 从指定路径下载文件
        // 2. 验证文件完整性
        // 3. 返回文件内容
    }
    
    public List<String> listFiles(FTPClient ftpClient, String remotePath, String pattern) {
        // 1. 列出指定路径下的文件
        // 2. 按模式筛选文件
        // 3. 返回文件列表
    }
    
    public boolean deleteFile(FTPClient ftpClient, String remotePath, String fileName) {
        // 1. 删除指定文件
        // 2. 返回删除结果
    }
}
```

## FTP处理流程
### 标准化配置文件同步流程：
1. SC接收到ASK_SCHEMECONFIG请求
2. 准备标准化配置文件
3. 通过FTP上传到SU
4. 返回ASK_SCHEMECONFIG_ACK响应

### 历史数据获取流程：
1. SC发送ASK_TODAYHISDATA请求
2. SU准备当天历史数据文件
3. SC通过FTP下载数据文件
4. 解析数据文件并存储

## 技术要求
1. 异步文件传输处理
2. 连接池管理
3. 文件传输进度监控  
4. 错误重试机制
5. 文件完整性校验

## 输出要求
完整的CTCC FTP处理组件，包括所有文件类型的上传下载功能
```

#### 任务3.3.3: 实现数据访问层
**工作量**: 20小时
**参考**: CMCC的数据库设计模式

**AI提示词模板**:
```
# 实现CTCC数据访问层

## 参考CMCC数据访问架构
参考CMCC的MyBatis Plus使用模式（仅参考架构思路）：
```java
// 实体类模式
@Data
@TableName("cmcc_fsus")
public class CMCCFsu {
    @TableId private Long id;
    private String fsuId;
    // ... 其他字段
}

// Mapper接口模式
@Mapper
public interface CMCCFsuMapper extends BaseMapper<CMCCFsu> {
    // 自定义查询方法
}
```

## CTCC数据访问层实现要求
基于前面设计的CTCC数据库结构，实现完整的数据访问层：

### 1. 实体类定义

#### CtccSu实体 (ctcc_sus表)
```java
@Data
@TableName("ctcc_sus")
@ApiModel(description = "CTCC SU基础信息")
public class CtccSu {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("suid")
    @ApiModelProperty("SU编码，长度17字节")
    @Size(max = 17)
    private String suid;
    
    @TableField("su_ip") 
    @ApiModelProperty("SU IP地址")
    @Size(max = 15)
    private String suIp;
    
    @TableField("su_port")
    @ApiModelProperty("SU WebService端口")
    private Integer suPort;
    
    @TableField("su_vendor")
    @ApiModelProperty("SU制造商")
    @Size(max = 50)
    private String suVendor;
    
    @TableField("su_model")
    @ApiModelProperty("SU型号")
    @Size(max = 50)
    private String suModel;
    
    @TableField("su_hard_ver")
    @ApiModelProperty("硬件版本")
    @Size(max = 20)
    private String suHardVer;
    
    @TableField("su_soft_ver")
    @ApiModelProperty("软件版本")
    @Size(max = 20)
    private String suSoftVer;
    
    @TableField("cpu_usage")
    @ApiModelProperty("CPU使用率")
    private Float cpuUsage;
    
    @TableField("mem_usage")
    @ApiModelProperty("内存使用率")
    private Float memUsage;
    
    @TableField("su_date_time")
    @ApiModelProperty("SU本地时间")
    private LocalDateTime suDateTime;
    
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
```

#### CtccDevice实体 (ctcc_devices表)
```java
@Data
@TableName("ctcc_devices")
@ApiModel(description = "CTCC设备信息")
public class CtccDevice {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("device_id")
    @Size(max = 7)
    private String deviceId;
    
    @TableField("suid")
    @Size(max = 17)  
    private String suid;
    
    @TableField("so_name")
    @Size(max = 30)
    private String soName;
    
    @TableField("so_manufac")
    @Size(max = 30)
    private String soManufac;
    
    @TableField("so_model")
    @Size(max = 30)
    private String soModel;
    
    @TableField("so_output_capacity")
    private Float soOutputCapacity;
    
    @TableField("so_output_unit")
    @Size(max = 10)
    private String soOutputUnit;
    
    @TableField("so_start_time")
    private LocalDate soStartTime;
    
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
```

#### CtccSignal实体 (ctcc_signals表)
```java
@Data
@TableName("ctcc_signals")
@ApiModel(description = "CTCC监控点信息")
public class CtccSignal {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("spid")
    @Size(max = 12)
    private String spid;
    
    @TableField("device_id")
    @Size(max = 7)
    private String deviceId;
    
    @TableField("signal_type")
    private EnumType signalType; // TI=1,DI=2,AI=3,DO=4,AO=5
    
    @TableField("measured_val")
    private Float measuredVal;
    
    @TableField("meanings")
    private EnumDeviceMeanings meanings;
    
    @TableField("report_time")
    private LocalDateTime reportTime;
    
    @TableField("status")
    private EnumState status;
    
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
```

#### CtccAlarm实体 (ctcc_alarms表)
```java
@Data
@TableName("ctcc_alarms")
@ApiModel(description = "CTCC告警信息")
public class CtccAlarm {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("serial_no")
    @Size(max = 10)
    private String serialNo;
    
    @TableField("suid")
    @Size(max = 17)
    private String suid;
    
    @TableField("device_id")
    @Size(max = 7)
    private String deviceId;
    
    @TableField("spid")
    @Size(max = 12)
    private String spid;
    
    @TableField("start_time")
    private LocalDateTime startTime;
    
    @TableField("end_time")
    private LocalDateTime endTime;
    
    @TableField("trigger_val")
    private Float triggerVal;
    
    @TableField("alarm_level")
    private EnumAlarmLevel alarmLevel;
    
    @TableField("alarm_flag")
    private EnumFlag alarmFlag;
    
    @TableField("alarm_desc")
    @Size(max = 200)
    private String alarmDesc;
    
    @TableField("alarm_fri_desc")
    @Size(max = 200)
    private String alarmFriDesc;
    
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
```

#### CtccSmartDoor实体 (ctcc_smart_doors表) - CTCC特有
```java
@Data
@TableName("ctcc_smart_doors")
@ApiModel(description = "CTCC智能门禁设备")
public class CtccSmartDoor {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("device_id")
    @Size(max = 7)
    private String deviceId;
    
    @TableField("suid")
    @Size(max = 17)
    private String suid;
    
    @TableField("door_type")
    @Size(max = 60)
    private String doorType;
    
    @TableField("card_type")
    private Short cardType;
    
    @TableField("smart_door_des")
    @Size(max = 200)
    private String smartDoorDes;
    
    @TableField("created_time")
    private LocalDateTime createdTime;
    
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
```

### 2. Mapper接口定义

#### CtccSuMapper
```java
@Mapper
public interface CtccSuMapper extends BaseMapper<CtccSu> {
    
    // 根据SUID查询SU信息
    CtccSu selectBySuid(@Param("suid") String suid);
    
    // 更新SU状态信息
    int updateSuStatus(@Param("suid") String suid, 
                      @Param("cpuUsage") Float cpuUsage,
                      @Param("memUsage") Float memUsage,
                      @Param("suDateTime") LocalDateTime suDateTime);
    
    // 查询所有在线SU
    List<CtccSu> selectOnlineSus();
    
    // 根据IP地址查询SU
    List<CtccSu> selectBySuIp(@Param("suIp") String suIp);
}
```

#### CtccAlarmMapper
```java
@Mapper
public interface CtccAlarmMapper extends BaseMapper<CtccAlarm> {
    
    // 查询活动告警
    List<CtccAlarm> selectActiveAlarms(@Param("suid") String suid);
    
    // 按时间范围查询告警
    List<CtccAlarm> selectAlarmsByTimeRange(@Param("suid") String suid,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
    
    // 批量插入告警
    int insertBatchAlarms(@Param("alarms") List<CtccAlarm> alarms);
    
    // 结束告警
    int endAlarm(@Param("serialNo") String serialNo, 
                @Param("endTime") LocalDateTime endTime);
    
    // 统计告警数量
    Map<String, Object> countAlarmsByLevel(@Param("suid") String suid);
}
```

#### CtccSignalMapper
```java
@Mapper
public interface CtccSignalMapper extends BaseMapper<CtccSignal> {
    
    // 根据设备ID和SPID列表查询信号
    List<CtccSignal> selectByDeviceAndSpids(@Param("deviceId") String deviceId,
                                           @Param("spids") List<String> spids);
    
    // 更新信号实时值
    int updateSignalValue(@Param("spid") String spid,
                         @Param("measuredVal") Float measuredVal,
                         @Param("status") EnumState status,
                         @Param("reportTime") LocalDateTime reportTime);
    
    // 批量更新信号值
    int batchUpdateSignalValues(@Param("signals") List<CtccSignal> signals);
    
    // 查询历史数据
    List<CtccSignal> selectHistoryData(@Param("suid") String suid,
                                      @Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);
}
```

### 3. 服务层接口

#### CtccSuService
```java
@Service
public interface CtccSuService extends IService<CtccSu> {
    
    // SU注册
    boolean registerSu(CtccSu su);
    
    // 更新SU状态
    boolean updateSuStatus(String suid, TSUStatus status);
    
    // 获取SU信息
    CtccSu getSuBySuid(String suid);
    
    // 验证SU权限
    boolean validateSuPermission(String suid, String ip);
}
```

#### CtccAlarmService
```java
@Service
public interface CtccAlarmService extends IService<CtccAlarm> {
    
    // 处理告警上报
    boolean processAlarmReport(List<TAlarm> alarms);
    
    // 获取活动告警
    List<TAlarm> getActiveAlarms(String suid);
    
    // 结束告警
    boolean endAlarm(String serialNo, LocalDateTime endTime);
    
    // 告警统计
    Map<String, Object> getAlarmStatistics(String suid);
}
```

## 技术要求
1. 使用MyBatis Plus框架
2. 支持多数据库类型
3. 实现分页查询
4. 事务管理支持
5. 缓存机制集成

## 输出要求
完整的CTCC数据访问层实现，包括所有实体类、Mapper接口和基础服务类
```

### 3.4 第四阶段：前端界面开发 (第6周 - 40小时)

#### 任务3.4.1: 实现CTCC管理界面
**工作量**: 40小时
**参考**: CMCC插件的前端界面架构

**AI提示词模板**:
```
# 实现CTCC管理界面

## 参考CMCC前端架构模式
参考CMCC插件的Vue 3前端架构（仅参考组件结构，不复制代码）：
- 使用Vue 3 Composition API
- Element Plus组件库
- TypeScript类型定义
- Pinia状态管理
- 模块化路由设计

## CTCC前端实现要求

### 1. SU设备管理界面 (CtccSuManagement.vue)
```vue
<template>
  <div class="ctcc-su-management">
    <!-- SU设备列表 -->
    <el-table :data="suList" stripe>
      <el-table-column prop="suid" label="SU编码" width="180"/>
      <el-table-column prop="suIp" label="SU IP地址" width="150"/>
      <el-table-column prop="suVendor" label="制造商" width="120"/>
      <el-table-column prop="suModel" label="型号" width="120"/>
      <el-table-column prop="cpuUsage" label="CPU使用率" width="100">
        <template #default="{ row }">
          <el-progress :percentage="row.cpuUsage" :status="getStatusByUsage(row.cpuUsage)"/>
        </template>
      </el-table-column>
      <el-table-column prop="memUsage" label="内存使用率" width="100">
        <template #default="{ row }">
          <el-progress :percentage="row.memUsage" :status="getStatusByUsage(row.memUsage)"/>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="viewSuDetail(row)">详情</el-button>
          <el-button type="warning" size="small" @click="rebootSu(row.suid)">重启</el-button>
          <el-button type="danger" size="small" @click="deleteSu(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- SU详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="SU设备详情" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="SU编码">{{ selectedSu.suid }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedSu.suIp }}</el-descriptions-item>
        <el-descriptions-item label="端口">{{ selectedSu.suPort }}</el-descriptions-item>
        <el-descriptions-item label="制造商">{{ selectedSu.suVendor }}</el-descriptions-item>
        <el-descriptions-item label="型号">{{ selectedSu.suModel }}</el-descriptions-item>
        <el-descriptions-item label="硬件版本">{{ selectedSu.suHardVer }}</el-descriptions-item>
        <el-descriptions-item label="软件版本">{{ selectedSu.suSoftVer }}</el-descriptions-item>
        <el-descriptions-item label="本地时间">{{ selectedSu.suDateTime }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// 实现SU管理逻辑
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { CtccSu } from '@/types/ctcc'
import { ctccSuApi } from '@/api/ctcc'

const suList = ref<CtccSu[]>([])
const detailDialogVisible = ref(false)
const selectedSu = ref<CtccSu>({})

// 加载SU列表
const loadSuList = async () => {
  try {
    const response = await ctccSuApi.getSuList()
    if (response.state) {
      suList.value = response.data
    } else {
      ElMessage.error(response.err_msg || '加载SU列表失败')
    }
  } catch (error) {
    ElMessage.error('网络错误')
  }
}

// SU重启
const rebootSu = async (suid: string) => {
  try {
    const response = await ctccSuApi.rebootSu(suid)
    if (response.state) {
      ElMessage.success('SU重启指令已发送')
    } else {
      ElMessage.error(response.err_msg || 'SU重启失败')
    }
  } catch (error) {
    ElMessage.error('网络错误')
  }
}

onMounted(() => {
  loadSuList()
})
</script>
```

### 2. 告警管理界面 (CtccAlarmManagement.vue)
```vue
<template>
  <div class="ctcc-alarm-management">
    <!-- 告警筛选条件 -->
    <el-form :model="searchForm" inline class="search-form">
      <el-form-item label="SU编码">
        <el-input v-model="searchForm.suid" placeholder="请输入SU编码" clearable/>
      </el-form-item>
      <el-form-item label="告警级别">
        <el-select v-model="searchForm.alarmLevel" placeholder="请选择告警级别" clearable>
          <el-option label="一级告警" :value="1"/>
          <el-option label="二级告警" :value="2"/>
          <el-option label="三级告警" :value="3"/>
          <el-option label="四级告警" :value="4"/>
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="searchForm.timeRange"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchAlarms">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 告警列表 -->
    <el-table :data="alarmList" stripe>
      <el-table-column prop="serialNo" label="告警序号" width="120"/>
      <el-table-column prop="suid" label="SU编码" width="140"/>
      <el-table-column prop="deviceId" label="设备ID" width="100"/>
      <el-table-column prop="spid" label="监控点ID" width="120"/>
      <el-table-column prop="startTime" label="开始时间" width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.startTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="endTime" label="结束时间" width="160">
        <template #default="{ row }">
          {{ row.endTime ? formatDateTime(row.endTime) : '进行中' }}
        </template>
      </el-table-column>
      <el-table-column prop="alarmLevel" label="告警级别" width="100">
        <template #default="{ row }">
          <el-tag :type="getAlarmLevelType(row.alarmLevel)">
            {{ getAlarmLevelText(row.alarmLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="alarmDesc" label="告警描述" show-overflow-tooltip/>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="viewAlarmDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { CtccAlarm } from '@/types/ctcc'
import { ctccAlarmApi } from '@/api/ctcc'

// 实现告警管理逻辑
</script>
```

### 3. 智能门禁管理界面 (CtccSmartDoorManagement.vue) - CTCC特有
```vue
<template>
  <div class="ctcc-smart-door-management">
    <el-tabs v-model="activeTab">
      <!-- 门禁设备管理 -->
      <el-tab-pane label="门禁设备" name="devices">
        <el-button type="primary" @click="showAddDoorDialog">添加门禁设备</el-button>
        <el-table :data="doorDeviceList" stripe style="margin-top: 16px;">
          <el-table-column prop="deviceId" label="设备ID" width="100"/>
          <el-table-column prop="suid" label="所属SU" width="140"/>
          <el-table-column prop="doorType" label="门禁类型" width="150"/>
          <el-table-column prop="cardType" label="读卡字节数" width="120"/>
          <el-table-column prop="smartDoorDes" label="设备描述" show-overflow-tooltip/>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="editDoorDevice(row)">编辑</el-button>
              <el-button type="warning" size="small" @click="manageAuth(row)">授权管理</el-button>
              <el-button type="danger" size="small" @click="deleteDoorDevice(row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- 门禁授权管理 -->
      <el-tab-pane label="授权管理" name="auth">
        <el-form :model="authForm" inline class="auth-form">
          <el-form-item label="门禁设备">
            <el-select v-model="authForm.deviceId" placeholder="请选择门禁设备">
              <el-option 
                v-for="device in doorDeviceList" 
                :key="device.deviceId"
                :label="device.smartDoorDes" 
                :value="device.deviceId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadAuthList">查询授权</el-button>
            <el-button type="success" @click="showAddAuthDialog">添加授权</el-button>
          </el-form-item>
        </el-form>

        <el-table :data="authList" stripe>
          <el-table-column prop="cardNumber" label="卡序号" width="100"/>
          <el-table-column prop="cardCode" label="卡号" width="150"/>
          <el-table-column prop="startDate" label="授权开始" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.startDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="endDate" label="授权结束" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.endDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="otherData" label="其他信息" show-overflow-tooltip/>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="editAuth(row)">编辑</el-button>
              <el-button type="danger" size="small" @click="deleteAuth(row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- 门禁事件监控 -->
      <el-tab-pane label="事件监控" name="events">
        <el-table :data="eventList" stripe>
          <el-table-column prop="serialNo" label="事件序号" width="120"/>
          <el-table-column prop="deviceId" label="设备ID" width="100"/>
          <el-table-column prop="eventType" label="事件类型" width="100">
            <template #default="{ row }">
              {{ getDoorEventTypeText(row.eventType) }}
            </template>
          </el-table-column>
          <el-table-column prop="eventTime" label="事件时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.eventTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="cardNumber" label="卡序号" width="100"/>
          <el-table-column prop="cardCode" label="卡号" width="150"/>
          <el-table-column prop="eventDes" label="事件描述" show-overflow-tooltip/>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
// 实现智能门禁管理逻辑
import { ref, reactive, onMounted } from 'vue'
import type { CtccSmartDoor, CtccDoorAuth, CtccDoorEvent } from '@/types/ctcc'
import { ctccSmartDoorApi } from '@/api/ctcc'

// 各种状态和数据
const activeTab = ref('devices')
const doorDeviceList = ref<CtccSmartDoor[]>([])
const authList = ref<CtccDoorAuth[]>([])
const eventList = ref<CtccDoorEvent[]>([])

// 实现智能门禁相关功能
</script>
```

### 4. TypeScript类型定义 (types/ctcc.ts)
```typescript
// CTCC相关的TypeScript接口定义

export interface CtccSu {
  id?: number
  suid: string
  suIp: string
  suPort: number
  suVendor: string
  suModel: string
  suHardVer: string
  suSoftVer: string
  cpuUsage?: number
  memUsage?: number
  suDateTime?: string
  createdTime?: string
  updatedTime?: string
}

export interface CtccAlarm {
  id?: number
  serialNo: string
  suid: string
  deviceId: string
  spid: string
  startTime: string
  endTime?: string
  triggerVal?: number
  alarmLevel: number
  alarmFlag: number
  alarmDesc: string
  alarmFriDesc?: string
  createdTime?: string
  updatedTime?: string
}

export interface CtccSmartDoor {
  id?: number
  deviceId: string
  suid: string
  doorType: string
  cardType: number
  smartDoorDes: string
  createdTime?: string
  updatedTime?: string
}

export interface CtccDoorAuth {
  id?: number
  cardNumber: number
  deviceId: string
  cardCode: string
  startDate: string
  endDate: string
  otherData?: string
  createdTime?: string
  updatedTime?: string
}

export interface CtccDoorEvent {
  id?: number
  serialNo: string
  deviceId: string
  eventType: number
  eventTime: string
  cardNumber: number
  cardCode: string
  eventDes: string
  createdTime?: string
  updatedTime?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  state: boolean
  data?: T
  err_msg?: string
}
```

### 5. API接口定义 (api/ctcc.ts)
```typescript
// CTCC API接口定义
import request from '@/utils/request'
import type { CtccSu, CtccAlarm, CtccSmartDoor, ApiResponse } from '@/types/ctcc'

export const ctccSuApi = {
  // 获取SU列表
  getSuList(): Promise<ApiResponse<CtccSu[]>> {
    return request.get('/api/ctcc/sus')
  },
  
  // SU重启
  rebootSu(suid: string): Promise<ApiResponse> {
    return request.post(`/api/ctcc/sus/${suid}/reboot`)
  },
  
  // 获取SU详情
  getSuDetail(suid: string): Promise<ApiResponse<CtccSu>> {
    return request.get(`/api/ctcc/sus/${suid}`)
  }
}

export const ctccAlarmApi = {
  // 获取告警列表
  getAlarmList(params?: any): Promise<ApiResponse<CtccAlarm[]>> {
    return request.get('/api/ctcc/alarms', { params })
  },
  
  // 获取活动告警
  getActiveAlarms(suid: string): Promise<ApiResponse<CtccAlarm[]>> {
    return request.get(`/api/ctcc/alarms/active/${suid}`)
  }
}

export const ctccSmartDoorApi = {
  // 获取门禁设备列表
  getDoorDevices(): Promise<ApiResponse<CtccSmartDoor[]>> {
    return request.get('/api/ctcc/smart-doors')
  },
  
  // 添加门禁设备
  addDoorDevice(data: CtccSmartDoor): Promise<ApiResponse> {
    return request.post('/api/ctcc/smart-doors', data)
  }
}
```

## 技术要求
1. 使用Vue 3 Composition API
2. TypeScript类型安全
3. Element Plus组件库
4. 响应式设计
5. 国际化支持 (但开发时使用中文)

## 输出要求
完整的CTCC前端管理界面，包括SU管理、告警管理、智能门禁管理等核心功能
```

### 3.5 第五阶段：集成测试和部署 (第7-8周 - 80小时)

#### 任务3.5.1: 单元测试和集成测试
**工作量**: 40小时

**AI提示词模板**:
```
# CTCC插件测试实现

## 测试架构要求
基于JUnit 5 + Mockito + Spring Boot Test实现完整的测试体系。

### 1. 协议层单元测试
```java
@ExtendWith(MockitoExtension.class)
class CtccEnumTest {
    
    @Test
    void testEnumTypeValues() {
        // 测试CTCC的EnumType枚举值
        assertEquals(1, EnumType.TI.getCode());
        assertEquals(2, EnumType.DI.getCode());
        assertEquals(3, EnumType.AI.getCode());
        assertEquals(4, EnumType.DO.getCode());
        assertEquals(5, EnumType.AO.getCode());
    }
    
    @Test
    void testEnumFailureCodeComplete() {
        // 测试24种失败原因码的完整性
        assertEquals(24, EnumFailureCode.values().length - 1); // 不包括OTHER_ERROR
        assertEquals(9999, EnumFailureCode.OTHER_ERROR.getCode());
    }
}

@ExtendWith(MockitoExtension.class)
class CtccMessageSerializationTest {
    
    @Test
    void testLoginMessageSerialization() {
        // 测试LOGIN消息的XML序列化
        LoginMessage message = new LoginMessage();
        message.getPkType().setName(CtccMessageType.LOGIN);
        message.getPkType().setCode(101);
        // ... 设置其他字段
        
        String xml = DataMapper.toXml(message);
        assertTrue(xml.contains("<Name>LOGIN</Name>"));
        assertTrue(xml.contains("<Code>101</Code>"));
    }
    
    @Test
    void testTAlarmStructureMapping() {
        // 测试TAlarm数据结构的字段映射
        TAlarm alarm = new TAlarm();
        alarm.setSerialNo("1234567890");
        alarm.setSuid("12345678901234567");
        alarm.setDeviceId("1234567");
        alarm.setSpid("123456789012");
        // ... 测试所有字段
    }
}
```

### 2. 消息处理器单元测试
```java
@ExtendWith(MockitoExtension.class)  
class CtccSuMessageProcessorTest {
    
    @Mock private CtccSuService suService;
    @Mock private CtccAlarmService alarmService;
    @InjectMocks private CtccSuMessageProcessor processor;
    
    @Test
    void testHandleLoginSuccess() {
        // 测试成功的LOGIN消息处理
        LoginMessage request = createValidLoginMessage();
        when(suService.validateSuPermission(any(), any())).thenReturn(true);
        when(suService.registerSu(any())).thenReturn(true);
        
        LoginAckMessage response = processor.handleLogin(request);
        
        assertEquals(EnumResult.SUCCESS, response.getInfo().getResult());
        assertNotNull(response.getInfo().getScip());
        verify(suService).registerSu(any(CtccSu.class));
    }
    
    @Test  
    void testHandleLoginFailureWithSuidError() {
        // 测试SUID错误的LOGIN处理
        LoginMessage request = createInvalidSuidLoginMessage();
        
        LoginAckMessage response = processor.handleLogin(request);
        
        assertEquals(EnumResult.FAILURE, response.getInfo().getResult());
        assertEquals(EnumFailureCode.SUID_ERROR, response.getInfo().getFailureCode());
    }
    
    @Test
    void testHandleSendAlarmWithMultipleAlarms() {
        // 测试多个告警的批量处理
        SendAlarmMessage request = createSendAlarmMessage(5); // 5个告警
        when(alarmService.processAlarmReport(any())).thenReturn(true);
        
        SendAlarmAckMessage response = processor.handleSendAlarm(request);
        
        assertEquals(EnumResult.SUCCESS, response.getInfo().getResult());
        verify(alarmService).processAlarmReport(argThat(list -> list.size() == 5));
    }
}
```

### 3. 数据访问层测试
```java
@SpringBootTest
@Transactional
class CtccSuMapperTest {
    
    @Autowired private CtccSuMapper suMapper;
    
    @Test
    void testInsertAndSelectSu() {
        // 测试SU的插入和查询
        CtccSu su = createTestSu();
        
        int result = suMapper.insert(su);
        assertEquals(1, result);
        
        CtccSu selected = suMapper.selectBySuid(su.getSuid());
        assertEquals(su.getSuid(), selected.getSuid());
        assertEquals(su.getSuIp(), selected.getSuIp());
    }
    
    @Test
    void testUpdateSuStatus() {
        // 测试SU状态更新
        CtccSu su = insertTestSu();
        
        int result = suMapper.updateSuStatus(su.getSuid(), 75.5f, 60.2f, LocalDateTime.now());
        assertEquals(1, result);
        
        CtccSu updated = suMapper.selectBySuid(su.getSuid());
        assertEquals(75.5f, updated.getCpuUsage(), 0.1f);
        assertEquals(60.2f, updated.getMemUsage(), 0.1f);
    }
}
```

### 4. 智能门禁功能测试 (CTCC特有)
```java
@SpringBootTest
class CtccSmartDoorServiceTest {
    
    @Autowired private CtccSmartDoorService smartDoorService;
    @Autowired private CtccDoorAuthService doorAuthService;
    
    @Test
    void testSmartDoorConfiguration() {
        // 测试智能门禁配置
        CtccSmartDoor door = createTestSmartDoor();
        boolean result = smartDoorService.saveDoorDevice(door);
        assertTrue(result);
        
        List<CtccSmartDoor> doors = smartDoorService.getDoorsByDevice(door.getDeviceId());
        assertEquals(1, doors.size());
    }
    
    @Test
    void testDoorAuthManagement() {
        // 测试门禁授权管理
        CtccDoorAuth auth = createTestDoorAuth();
        boolean result = doorAuthService.addAuthorization(auth);
        assertTrue(result);
        
        List<CtccDoorAuth> auths = doorAuthService.getAuthByDevice(auth.getDeviceId());
        assertEquals(1, auths.size());
    }
}
```

### 5. 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.yml")
class CtccPluginIntegrationTest {
    
    @Autowired private TestRestTemplate restTemplate;
    @LocalServerPort private int port;
    
    @Test
    void testFullLoginFlow() {
        // 测试完整的SU登录流程
        String loginXml = createLoginRequestXml();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);
        HttpEntity<String> entity = new HttpEntity<>(loginXml, headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(
            "http://localhost:" + port + "/ctcc/webservice", entity, String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().contains("LOGIN_ACK"));
        assertTrue(response.getBody().contains("SUCCESS"));
    }
    
    @Test
    void testAlarmReportingFlow() {
        // 测试告警上报流程
        String alarmXml = createSendAlarmRequestXml();
        
        HttpEntity<String> entity = new HttpEntity<>(alarmXml, createSoapHeaders());
        
        ResponseEntity<String> response = restTemplate.postForEntity(
            "http://localhost:" + port + "/ctcc/webservice", entity, String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().contains("SEND_ALARM_ACK"));
    }
}
```

## 测试覆盖率要求
1. **代码覆盖率**: ≥80%
2. **分支覆盖率**: ≥70%
3. **方法覆盖率**: ≥90%

## 性能测试要求
```java
@Test
void testMessageProcessingPerformance() {
    // 测试消息处理性能
    int messageCount = 1000;
    long startTime = System.currentTimeMillis();
    
    for (int i = 0; i < messageCount; i++) {
        processor.handleGetData(createGetDataMessage());
    }
    
    long endTime = System.currentTimeMillis();
    long avgTime = (endTime - startTime) / messageCount;
    
    assertTrue(avgTime < 100, "平均消息处理时间应小于100ms");
}
```

## 输出要求
1. 完整的测试套件，覆盖所有核心功能
2. 测试覆盖率报告
3. 性能测试报告
4. 集成测试验证报告
```

#### 任务3.5.2: 文档编写和部署准备
**工作量**: 40小时

**AI提示词模板**:
```
# CTCC插件文档和部署准备

## 文档编写要求

### 1. 用户使用手册 (CTCC插件使用指南.md)
```markdown
# TCS-SOUTH-CTCC插件使用指南

## 1. 插件概述
TCS-SOUTH-CTCC插件是基于中国电信动环监控系统B接口技术规范20230515版本开发的南向接入插件，支持CTCC标准的SU设备接入和管理。

## 2. 功能特性
### 2.1 核心功能
- 支持27种CTCC B接口消息类型
- 完整的SU设备生命周期管理
- 实时数据采集和历史数据查询
- 告警信息上报和活动告警查询
- 智能门禁设备管理 (CTCC特有)
- 标准化配置文件和厂家配置文件管理
- 监控点配置模板选项管理

### 2.2 技术特性
- 基于Spring Boot + Pekko Actor架构
- 支持WebService + FTP双重接口
- 多数据库支持 (MySQL/PostgreSQL/H2/DM/OpenGauss)
- 完整的错误处理 (24种EnumFailureCode)
- 异步消息处理机制
- 前端管理界面 (Vue 3 + Element Plus)

## 3. 安装部署
### 3.1 环境要求
- JDK 17+
- MySQL 8.0+ 或其他支持的数据库
- 不少于2GB内存
- 不少于10GB磁盘空间

### 3.2 安装步骤
1. 将插件jar包放入plugins目录
2. 配置数据库连接信息
3. 启动TCS主程序
4. 在插件管理界面启用CTCC插件

## 4. 配置说明
### 4.1 数据库配置
### 4.2 网络参数配置  
### 4.3 FTP服务配置

## 5. 使用说明
### 5.1 SU设备管理
### 5.2 告警管理
### 5.3 智能门禁管理
### 5.4 数据查询

## 6. 故障排除
### 6.1 常见问题
### 6.2 日志分析
### 6.3 联系支持
```

### 2. 开发文档 (CTCC插件开发文档.md)
```markdown
# TCS-SOUTH-CTCC插件开发文档

## 1. 架构设计
### 1.1 总体架构
### 1.2 模块设计
### 1.3 数据流图

## 2. 协议实现
### 2.1 CTCC B接口规范对照
### 2.2 消息类型实现
### 2.3 数据结构定义

## 3. API文档
### 3.1 REST API接口
### 3.2 WebService接口
### 3.3 错误码说明

## 4. 数据库设计
### 4.1 表结构设计
### 4.2 索引策略
### 4.3 数据迁移

## 5. 扩展开发
### 5.1 自定义消息处理
### 5.2 插件扩展点
### 5.3 开发最佳实践
```

### 3. 部署配置文件
```yaml
# application-ctcc.yml
server:
  port: 8550

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************
    username: ${DB_USERNAME:tcs}
    password: ${DB_PASSWORD:tcs123}
  
  flyway:
    enabled: true
    locations: classpath:db/south-ctcc-plugin/db/{vendor}
    baseline-on-migrate: true

# CTCC插件特定配置
ctcc:
  # SU连接配置
  su:
    connection-timeout: 30s
    heartbeat-interval: 60s
    max-retry-count: 3
  
  # FTP配置
  ftp:
    connection-timeout: 30s
    data-timeout: 60s
    passive-mode: true
    binary-mode: true
  
  # 消息处理配置
  message:
    async-processing: true
    thread-pool-size: 10
    queue-capacity: 1000
  
  # 告警配置
  alarm:
    batch-size: 100
    max-queue-size: 10000
    processing-interval: 5s
  
  # 智能门禁配置
  smart-door:
    enabled: true
    event-retention-days: 30
    auth-cache-size: 1000

# Pekko配置
pekko:
  cluster:
    seed-nodes: ["pekko://tcs@127.0.0.1:2551"]
    roles: ["south-plugin", "ctcc"]
    
logging:
  level:
    com.siteweb.tcs.south.ctcc: DEBUG
    org.apache.pekko: INFO
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{suid}] %logger{50} - %msg%n"
```

### 4. Docker部署文件
```dockerfile
# Dockerfile-ctcc
FROM openjdk:17-jre-slim

# 设置工作目录
WORKDIR /app

# 复制插件文件
COPY tcs-south-ctcc-*.jar /app/plugins/

# 复制配置文件
COPY application-ctcc.yml /app/config/

# 设置环境变量
ENV JAVA_OPTS="-Xmx2g -Xms1g"
ENV SPRING_PROFILES_ACTIVE="ctcc,prod"

# 暴露端口
EXPOSE 8550 2551

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --retries=3 \
  CMD curl -f http://localhost:8550/api/ctcc/health || exit 1

# 启动命令
CMD ["sh", "-c", "java $JAVA_OPTS -jar tcs-core.jar --spring.config.additional-location=config/application-ctcc.yml"]
```

```yaml
# docker-compose-ctcc.yml
version: '3.8'

services:
  tcs-ctcc:
    build:
      context: .
      dockerfile: Dockerfile-ctcc
    ports:
      - "8550:8550"
      - "2551:2551"
    environment:
      - SPRING_PROFILES_ACTIVE=ctcc,prod
      - DB_HOST=mysql
      - DB_USERNAME=tcs
      - DB_PASSWORD=tcs123
    volumes:
      - ./logs:/app/logs
      - ./plugins:/app/plugins
      - ./config:/app/config
    depends_on:
      - mysql
      - redis
    networks:
      - tcs-network
    restart: unless-stopped
    
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=tcs_ctcc
      - MYSQL_USER=tcs
      - MYSQL_PASSWORD=tcs123
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - tcs-network
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    networks:
      - tcs-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  tcs-network:
    driver: bridge
```

### 5. 部署脚本
```bash
#!/bin/bash
# deploy-ctcc.sh

set -e

echo "开始部署TCS-SOUTH-CTCC插件..."

# 检查环境
check_environment() {
    echo "检查部署环境..."
    if ! command -v docker &> /dev/null; then
        echo "错误：Docker未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "错误：Docker Compose未安装"
        exit 1
    fi
    
    echo "环境检查通过"
}

# 构建镜像
build_images() {
    echo "构建Docker镜像..."
    docker-compose -f docker-compose-ctcc.yml build
    echo "镜像构建完成"
}

# 启动服务
start_services() {
    echo "启动服务..."
    docker-compose -f docker-compose-ctcc.yml up -d
    echo "服务启动完成"
}

# 健康检查
health_check() {
    echo "等待服务启动..."
    sleep 30
    
    for i in {1..30}; do
        if curl -f http://localhost:8550/api/ctcc/health > /dev/null 2>&1; then
            echo "服务健康检查通过"
            return 0
        fi
        echo "等待服务启动... ($i/30)"
        sleep 10
    done
    
    echo "错误：服务启动失败"
    docker-compose -f docker-compose-ctcc.yml logs
    exit 1
}

# 主流程
main() {
    check_environment
    build_images
    start_services
    health_check
    
    echo "TCS-SOUTH-CTCC插件部署成功！"
    echo "管理界面: http://localhost:8550"
    echo "查看日志: docker-compose -f docker-compose-ctcc.yml logs -f"
}

main "$@"
```

## 输出要求
1. 完整的用户使用手册
2. 详细的开发文档
3. 部署配置文件和脚本
4. Docker部署方案
5. 故障排除指南
```

---

## 4. 开发验收标准

### 4.1 功能完整性验收
- [ ] 实现27种CTCC B接口消息类型处理
- [ ] 智能门禁管理功能完整
- [ ] SU设备生命周期管理完整
- [ ] 告警处理和查询功能正常
- [ ] FTP文件传输功能正常
- [ ] 前端管理界面功能完整

### 4.2 技术质量验收
- [ ] 代码测试覆盖率 ≥ 80%
- [ ] 单个消息处理时间 < 100ms
- [ ] 支持1000并发SU连接
- [ ] 内存使用 < 2GB
- [ ] 数据库连接池效率优化

### 4.3 规范符合性验收
- [ ] 100%符合CTCC B接口技术规范
- [ ] 所有枚举值与规范一致
- [ ] 消息格式与规范XML结构一致
- [ ] 错误处理支持24种失败原因码
- [ ] 数据结构字段长度符合规范

### 4.4 部署可用性验收  
- [ ] Docker容器正常启动
- [ ] 数据库迁移脚本执行成功
- [ ] 插件热插拔功能正常
- [ ] 配置文件加载正确
- [ ] 日志记录功能正常

---

## 5. 总结

本全新重写开发计划确保了CTCC插件的：

### 5.1 技术优势
1. **架构独立性**: 完全独立的代码库，避免改造风险
2. **规范严格性**: 100%按照CTCC B接口规范实现
3. **功能完整性**: 支持所有27种消息类型和智能门禁
4. **可维护性**: 清晰的模块划分和完整的测试覆盖

### 5.2 开发效率
1. **AI辅助开发**: 专业化提示词确保开发质量  
2. **分阶段实施**: 8周详细计划，320工作小时
3. **并行开发**: 前后端分离，可同时开发
4. **质量保证**: 完整的测试体系和验收标准

### 5.3 长期价值
1. **标准化基础**: 为其他运营商插件开发提供参考
2. **技术积累**: 形成成熟的南向插件开发模式
3. **可扩展性**: 支持未来功能扩展和技术升级
4. **商业价值**: 满足CTCC市场需求，具备商业化潜力

通过这个全新重写的开发计划，将构建一个高质量、高性能、高可用的TCS-SOUTH-CTCC插件！