package com.siteweb.tcs.hub.dal.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siteweb.tcs.hub.dal.entity.Region;
import com.siteweb.tcs.hub.dal.entity.RegionItem;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2024-06-05)
 **/
@Data
public class RegionDTO {

    private Long regionId;
    private Long parentId;
    private String regionName;
    private String description;
    private Integer displayIndex;

    private List<RegionDTO> children;
    private List<RegionItem> items;


    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public List<RegionItem> getItems(){
        if (items == null){
            items = new ArrayList<>();
        }
        return items;
    }

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public List<RegionDTO> getChildren(){
        if (children == null){
            children = new ArrayList<>();
        }
        return children;
    }


    public static RegionDTO from(Region region){
        RegionDTO dto = new RegionDTO();
        dto.setRegionId(region.getRegionId());
        dto.setRegionName(region.getRegionName());
        dto.setParentId(region.getParentId());
        dto.setDescription(region.getDescription());
        dto.setDisplayIndex(region.getDisplayIndex());
        return dto;
    }


}
