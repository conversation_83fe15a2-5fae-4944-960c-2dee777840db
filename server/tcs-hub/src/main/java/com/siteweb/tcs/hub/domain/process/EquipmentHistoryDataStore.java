package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentHisBatCurveData;
import com.siteweb.tcs.hub.domain.letter.EquipmentHisData;
import com.siteweb.tcs.hub.domain.letter.EquipmentHistoryData;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * <AUTHOR>
 */
@Slf4j
public class EquipmentHistoryDataStore extends ProbeActor {



    private final ActorRef spout;
    private final ForeignDevice deviceInfo;

    public EquipmentHistoryDataStore(ActorRef spout, ForeignDevice deviceInfo) {
        this.spout = spout;
        this.deviceInfo = deviceInfo;
        // Initialize probe if needed
    }

    public static Props props(ActorRef spout, ForeignDevice deviceInfo) {
        return Props.create(EquipmentHistoryDataStore.class, () -> new EquipmentHistoryDataStore(spout, deviceInfo));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentHisData.class, this::handleEquipmentHistoryData)
                .match(EquipmentHistoryData.class, this::handleEquipmentHistoryData)
                .match(EquipmentHisBatCurveData.class, this::handleEquipmentHistoryData)
                .build()
                .orElse(super.createReceive());
    }

    private void handleEquipmentHistoryData(EquipmentHisData equipmentHisData) {
        spout.tell(equipmentHisData, getSelf());
    }

    private void handleEquipmentHistoryData(EquipmentHistoryData equipmentHistoryData) {
        spout.tell(equipmentHistoryData, getSelf());
    }

    private void handleEquipmentHistoryData(EquipmentHisBatCurveData foreignDeviceHisBatCurveData) {
        spout.tell(foreignDeviceHisBatCurveData, getSelf());
    }

}

