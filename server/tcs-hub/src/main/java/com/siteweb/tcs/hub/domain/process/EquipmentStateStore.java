package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentChange;
import com.siteweb.tcs.hub.domain.letter.LiveEquipmentState;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

@Slf4j
public class EquipmentStateStore extends ProbeActor {

    private final ActorRef spout;
    private final LiveEquipmentState liveEquipmentState;

    public EquipmentStateStore(ForeignDevice device, ActorRef spout) {
        this.spout = spout;
        this.liveEquipmentState = new LiveEquipmentState();
        this.liveEquipmentState.setMonitorUnitId(device.getMonitorUnitId());
        this.liveEquipmentState.setEquipmentId(device.getEquipmentId());
    }

    public static Props props(ForeignDevice device, ActorRef spout) {
        return Props.create(EquipmentStateStore.class, device, spout);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentChange.class, this::onEquipmentChange)
                .build()
                .orElse(super.createReceive());
    }

    private void onEquipmentChange(EquipmentChange equipmentChange) {
        getProbe().info(" onEquipmentChange: equipmentId=" + equipmentChange.getEquipmentId() + ", muId=" + equipmentChange.getMonitorUnitId() + ", state=" + equipmentChange.getEquipmentState());
        this.liveEquipmentState.update(equipmentChange);
        this.spout.tell(equipmentChange, self());
        LocalEquipmentStateStore.saveEquipmentState(equipmentChange);
    }


}

