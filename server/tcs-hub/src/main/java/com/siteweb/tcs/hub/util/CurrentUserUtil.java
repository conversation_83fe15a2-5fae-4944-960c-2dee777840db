package com.siteweb.tcs.hub.util;

import com.siteweb.tcs.hub.dal.dto.AccountDTO;
import com.siteweb.tcs.hub.security.TokenUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Arrays;
import java.util.List;

/**
 * 当前用户信息获取工具类
 * 
 * 提供便捷的方法获取当前登录用户的各种信息，用于记录操作日志、权限检查等场景。
 * 
 * <AUTHOR> System
 * @since 1.0
 */
@Slf4j
public class CurrentUserUtil {

    /**
     * 获取当前登录用户的完整账号信息
     * 
     * @return 当前用户的AccountDTO对象，未登录时返回null
     */
    public static AccountDTO getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                // 根据项目的认证机制，TokenUser存储在Details中
                Object details = authentication.getDetails();
                if (details instanceof TokenUser) {
                    TokenUser tokenUser = (TokenUser) details;
                    return tokenUser.getUser();
                }
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败", e);
        }
        return null;
    }

    /**
     * 获取当前登录用户ID
     * 
     * @return 用户ID，未登录时返回null
     */
    public static Integer getCurrentUserId() {
        AccountDTO user = getCurrentUser();
        return user != null ? user.getUserId() : null;
    }

    /**
     * 获取当前登录用户名
     * 
     * @return 用户名，未登录时返回null
     */
    public static String getCurrentUserName() {
        AccountDTO user = getCurrentUser();
        return user != null ? user.getUserName() : null;
    }

    /**
     * 获取当前登录用户的登录ID
     * 
     * @return 登录ID，未登录时返回null
     */
    public static String getCurrentLoginId() {
        AccountDTO user = getCurrentUser();
        return user != null ? user.getLoginId() : null;
    }

    /**
     * 获取当前用户角色ID字符串
     * 
     * @return 角色ID（逗号分隔），未登录时返回null
     */
    public static String getCurrentUserRoles() {
        AccountDTO user = getCurrentUser();
        return user != null ? user.getRoleIds() : null;
    }

    /**
     * 获取当前用户角色ID列表
     * 
     * @return 角色ID列表，未登录或无角色时返回空列表
     */
    public static List<String> getCurrentUserRoleList() {
        String roles = getCurrentUserRoles();
        if (roles != null && !roles.trim().isEmpty()) {
            return Arrays.asList(roles.split(","));
        }
        return List.of();
    }

    /**
     * 获取当前用户部门ID
     * 
     * @return 部门ID，未登录时返回null
     */
    public static Long getCurrentUserDepartmentId() {
        AccountDTO user = getCurrentUser();
        return user != null ? user.getDepartmentId() : null;
    }

    /**
     * 获取当前用户部门名称
     * 
     * @return 部门名称，未登录时返回null
     */
    public static String getCurrentUserDepartmentName() {
        AccountDTO user = getCurrentUser();
        return user != null ? user.getDepartmentName() : null;
    }

    /**
     * 获取当前用户的别名
     * 
     * @return 用户别名，未登录时返回null
     */
    public static String getCurrentUserAlias() {
        AccountDTO user = getCurrentUser();
        return user != null ? user.getAlias() : null;
    }

    /**
     * 获取当前用户的登录类型
     * 
     * @return 登录类型，未登录时返回null
     */
    public static String getCurrentLoginType() {
        AccountDTO user = getCurrentUser();
        return user != null ? user.getLoginType() : null;
    }

    /**
     * 检查用户是否已登录
     * 
     * @return true表示已登录，false表示未登录
     */
    public static boolean isUserLoggedIn() {
        return getCurrentUser() != null;
    }

    /**
     * 检查当前用户是否有指定角色
     * 
     * @param roleId 角色ID（字符串）
     * @return true表示有该角色，false表示没有或未登录
     */
    public static boolean hasRole(String roleId) {
        if (roleId == null || roleId.trim().isEmpty()) {
            return false;
        }
        List<String> userRoles = getCurrentUserRoleList();
        return userRoles.contains(roleId.trim());
    }

    /**
     * 检查当前用户是否有指定角色
     * 
     * @param roleId 角色ID（整数）
     * @return true表示有该角色，false表示没有或未登录
     */
    public static boolean hasRole(Integer roleId) {
        if (roleId == null) {
            return false;
        }
        return hasRole(roleId.toString());
    }

    /**
     * 检查当前用户是否有任意一个指定角色
     * 
     * @param roleIds 角色ID数组
     * @return true表示有任意一个角色，false表示都没有或未登录
     */
    public static boolean hasAnyRole(String... roleIds) {
        if (roleIds == null || roleIds.length == 0) {
            return false;
        }
        List<String> userRoles = getCurrentUserRoleList();
        for (String roleId : roleIds) {
            if (roleId != null && userRoles.contains(roleId.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前用户账号是否启用
     * 
     * @return true表示启用，false表示禁用或未登录
     */
    public static boolean isUserEnabled() {
        AccountDTO user = getCurrentUser();
        return user != null && user.isEnable();
    }

    /**
     * 检查当前用户账号是否被锁定
     * 
     * @return true表示锁定，false表示未锁定或未登录
     */
    public static boolean isUserLocked() {
        AccountDTO user = getCurrentUser();
        return user != null && user.isLocked();
    }

    // ==================== 安全方法（未登录时抛出异常） ====================

    /**
     * 必须获取当前用户ID（未登录时抛出异常）
     * 
     * @return 用户ID
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static Integer requireCurrentUserId() {
        Integer userId = getCurrentUserId();
        if (userId == null) {
            throw new IllegalStateException("用户未登录，无法获取用户ID");
        }
        return userId;
    }

    /**
     * 必须获取当前用户信息（未登录时抛出异常）
     * 
     * @return 用户完整信息
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static AccountDTO requireCurrentUser() {
        AccountDTO user = getCurrentUser();
        if (user == null) {
            throw new IllegalStateException("用户未登录，无法获取用户信息");
        }
        return user;
    }

    /**
     * 必须获取当前用户名（未登录时抛出异常）
     * 
     * @return 用户名
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static String requireCurrentUserName() {
        String userName = getCurrentUserName();
        if (userName == null) {
            throw new IllegalStateException("用户未登录，无法获取用户名");
        }
        return userName;
    }

    /**
     * 必须获取当前用户部门ID（未登录时抛出异常）
     * 
     * @return 部门ID
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static Long requireCurrentUserDepartmentId() {
        Long departmentId = getCurrentUserDepartmentId();
        if (departmentId == null) {
            throw new IllegalStateException("用户未登录，无法获取部门ID");
        }
        return departmentId;
    }

    /**
     * 要求用户必须有指定角色（否则抛出异常）
     * 
     * @param roleId 角色ID
     * @throws IllegalStateException 用户未登录或没有指定角色时抛出
     */
    public static void requireRole(String roleId) {
        if (!hasRole(roleId)) {
            throw new IllegalStateException("用户没有必需的角色权限: " + roleId);
        }
    }

    /**
     * 要求用户必须有指定角色（否则抛出异常）
     * 
     * @param roleId 角色ID
     * @throws IllegalStateException 用户未登录或没有指定角色时抛出
     */
    public static void requireRole(Integer roleId) {
        if (!hasRole(roleId)) {
            throw new IllegalStateException("用户没有必需的角色权限: " + roleId);
        }
    }

    /**
     * 要求用户账号必须是启用状态（否则抛出异常）
     * 
     * @throws IllegalStateException 用户未登录或账号未启用时抛出
     */
    public static void requireUserEnabled() {
        if (!isUserEnabled()) {
            throw new IllegalStateException("用户账号未启用");
        }
    }
}