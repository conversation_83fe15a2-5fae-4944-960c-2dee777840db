package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 设备表
 */
@Data
@TableName("tcs_device")
public class TcsDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("GatewayId")
    private Long gatewayId;

    @TableField("SouthDeviceId")
    private String southDeviceId;

    @TableField("SouthDeviceName")
    private String southDeviceName;

    @TableField("SouthDeviceType")
    private Integer southDeviceType;

    @TableField(value = "Metadata", typeHandler = EnhancedJacksonTypeHandler.class)
    private JsonNode metadata;

    @TableLogic(value = "false", delval = "true")
    @TableField("Deleted")
    private Boolean deleted;

    /**
     * 设备下的信号列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<TcsSignal> signals;

    /**
     * 设备下的告警列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<TcsAlarm> alarms;

    /**
     * 设备下的控制列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<TcsControl> controls;
} 