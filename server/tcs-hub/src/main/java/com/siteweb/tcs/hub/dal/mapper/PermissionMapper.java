package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.dto.MenuItemWithRoleResult;
import com.siteweb.tcs.hub.dal.dto.RegionDTO;
import com.siteweb.tcs.hub.dal.entity.MenuItem;
import com.siteweb.tcs.hub.dal.entity.Permission;
import com.siteweb.tcs.hub.dal.entity.Region;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    @Select("SELECT * FROM tcs_menu_item ")
    List<MenuItem> getAllMenuItem();

    @Select("select * from tcs_menu_item where menuItemId in(select distinct(permissionId) from tcs_role_permission_map a " +
            "inner join tcs_account_role_map b on b.RoleId = a.RoleId and b.UserId = #{userId} " +
            "where PermissionType = 1)")
    List<MenuItem> getUserPermMenu(Integer userId);

    @Insert({
            "<script>",
            "INSERT INTO tcs_role_permission_map (PluginId, RoleId, PermissionId, permissionType) VALUES ",
            "<foreach collection='bindIdList' item='bindId' separator=','>",
            "(#{pluginId}, #{roleId}, CAST(#{bindId} AS BIGINT), #{permissionType})",
            "</foreach>",
            "</script>"
    })
    int createMenuRoleMap(@Param("roleId") Integer roleId,
                          @Param("pluginId") String pluginId,
                          @Param("bindIdList") List<String> bindIdList,
                          @Param("permissionType") Integer permissionType);

    @Delete("delete from tcs_role_permission_map where roleId = #{roleId} and pluginId = #{pluginId} and permissionType = #{permissionType} ")
    void deleteMenuRoleMap(Integer roleId, String pluginId, Integer permissionType);

    @Select("select * from tcs_menu_item where menuItemId in(select distinct(permissionId) from tcs_role_permission_map " +
            "where roleId = #{roleId} and permissionType = 1)")
    List<MenuItem> getUserPermMenuByRoleId(Integer roleId);


    @Select("select * from tcs_regions where regionId in " +
            "(select PermissionId from tcs_role_permission_map where roleId = #{roleId} and permissionType = 2)")
    List<Region> getRegionListByRoleId(Integer roleId);


    @Select("SELECT * FROM tcs_menu_item ORDER BY pluginId, rank, menuItemId")
    List<MenuItem> getMenuTreeWithPaths();

    @Select("""
             WITH RECURSIVE menu_path AS (
                             SELECT
                             MenuItemId,
                             MenuItemName,
                             Name,
                             ParentMenuItemId
                             FROM tcs_menu_item
                             WHERE MenuItemId IN (select distinct(permissionId) from tcs_role_permission_map a
                                               inner join tcs_account_role_map b on b.RoleId = a.RoleId and b.UserId = #{userId}
                                               where PermissionType = 1)
                             UNION ALL
                             -- 递归查询父级菜单
                             SELECT
                             p.MenuItemId,
                             p.MenuItemName,
                             p.Name,
                             p.ParentMenuItemId
                             FROM tcs_menu_item p
                             JOIN menu_path mp ON p.MenuItemId = mp.ParentMenuItemId
                         )
                         SELECT distinct * FROM menu_path
            """)
    List<MenuItem> getUserPermMenuTree(Integer userId);

    @Select("""
            WITH RECURSIVE region_info AS (
             SELECT
             regionId,
             regionName,
             ParentId
             FROM tcs_regions
             WHERE regionId IN (select distinct(permissionId) from tcs_role_permission_map a
            				   inner join tcs_account_role_map b on b.RoleId = a.RoleId and b.UserId = #{userId}
            				   where PermissionType = 2)
             UNION ALL
             -- 递归查询父级菜单
             SELECT
             p.regionId,
             p.regionName,
             p.ParentId
             FROM tcs_regions p
             JOIN region_info mp ON p.regionId = mp.ParentId
            )
            SELECT distinct * FROM region_info
                        
            """)
    List<RegionDTO> getRegionListByUserId(Integer userId);

    @Delete("DELETE FROM tcs_role_permission_map WHERE roleId = #{roleId}")
    void deletePermissionRoleMapByRoleId(Integer roleId);

    /**
     * 获取菜单项及其关联的角色信息
     * 使用LEFT JOIN提升查询性能，一次性获取所有菜单和角色关联信息
     * 
     * @return 菜单项及角色映射结果列表
     */
    @Select("""
            SELECT 
                m.menuItemId,
                m.pluginId,
                m.menuItemName,
                m.Name,
                m.parentMenuItemId,
                m.path,
                m.icon,
                m.component,
                m.showLink,
                m.showParent,
                m.activePath,
                m.redirect,
                m.rank,
                m.auths,
                r.role_code as roleCode,
                r.RoleName as roleName
            FROM tcs_menu_item m
            LEFT JOIN tcs_role_permission_map rpm ON m.menuItemId = rpm.PermissionId 
                AND rpm.permissionType = 1 
            LEFT JOIN tcs_role r ON rpm.RoleId = r.RoleId 
                AND r.status = 1
            ORDER BY m.pluginId, m.rank, m.menuItemId
            """)
    List<MenuItemWithRoleResult> getMenuItemsWithRoles();

    /**
     * 根据插件ID获取菜单项及其关联的角色信息
     * 
     * @param pluginIds 插件ID列表
     * @return 菜单项及角色映射结果列表
     */
    @Select("""
            <script>
            SELECT 
                m.menuItemId,
                m.pluginId,
                m.menuItemName,
                m.Name,
                m.parentMenuItemId,
                m.path,
                m.icon,
                m.component,
                m.showLink,
                m.showParent,
                m.activePath,
                m.redirect,
                m.rank,
                m.auths,
                r.role_code as roleCode,
                r.RoleName as roleName
            FROM tcs_menu_item m
            LEFT JOIN tcs_role_permission_map rpm ON m.menuItemId = rpm.PermissionId 
                AND rpm.permissionType = 1 
                AND rpm.PluginId = m.pluginId
            LEFT JOIN tcs_role r ON rpm.RoleId = r.RoleId 
                AND r.status = 1
            WHERE m.pluginId IN 
            <foreach collection="pluginIds" item="pluginId" open="(" separator="," close=")">
                #{pluginId}
            </foreach>
            ORDER BY m.pluginId, m.rank, m.menuItemId
            </script>
            """)
    List<MenuItemWithRoleResult> getMenuItemsWithRolesByPluginIds(@Param("pluginIds") List<String> pluginIds);

    /**
     * 获取所有菜单的权限代码映射
     * @return 菜单ID到权限代码列表的映射
     */
    @Select("""
            SELECT 
                ac.menu_item_id as menuItemId,
                ac.auth_code as authCode
            FROM tcs_auth_code ac
            WHERE ac.menu_item_id IS NOT NULL
            ORDER BY ac.menu_item_id, ac.auth_id
            """)
    @Results({
        @Result(property = "menuItemId", column = "menuItemId"),
        @Result(property = "authCode", column = "authCode")
    })
    List<MenuAuthCodeMapping> getMenuAuthCodeMappings();

    /**
     * 根据菜单ID列表批量查询插件ID
     * @param menuIds 菜单ID列表
     * @return 菜单ID和插件ID的映射列表
     */
    @Select("""
        <script>
        SELECT menuItemId, pluginId FROM tcs_menu_item 
        WHERE menuItemId IN 
        <foreach collection="menuIds" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
        </script>
        """)
    @Results({
        @Result(property = "menuItemId", column = "menuItemId"),
        @Result(property = "pluginId", column = "pluginId")
    })
    List<MenuPluginMapping> getPluginIdsByMenuIds(@Param("menuIds") List<Long> menuIds);

    /**
     * 菜单权限代码映射结果类
     */
    class MenuAuthCodeMapping {
        private Long menuItemId;
        private String authCode;
        
        public Long getMenuItemId() { return menuItemId; }
        public void setMenuItemId(Long menuItemId) { this.menuItemId = menuItemId; }
        
        public String getAuthCode() { return authCode; }
        public void setAuthCode(String authCode) { this.authCode = authCode; }
    }
    
    /**
     * 菜单ID与插件ID映射结果类
     */
    class MenuPluginMapping {
        private Long menuItemId;
        private String pluginId;
        
        public Long getMenuItemId() { return menuItemId; }
        public void setMenuItemId(Long menuItemId) { this.menuItemId = menuItemId; }
        
        public String getPluginId() { return pluginId; }
        public void setPluginId(String pluginId) { this.pluginId = pluginId; }
    }

}
