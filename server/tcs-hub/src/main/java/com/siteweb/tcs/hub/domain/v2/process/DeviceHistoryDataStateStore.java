package com.siteweb.tcs.hub.domain.v2.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 11:06
 **/

public class DeviceHistoryDataStateStore extends ProbeActor {
    private TcsDevice device;
    private ActorRef pipelinePublisher;

    private DeviceHistoryDataStateStore(TcsDevice device, ActorRef pipelinePublisher){
        this.device = device;
        this.pipelinePublisher = pipelinePublisher;
    }

    public static Props props(TcsDevice device, ActorRef pipelinePublisher){
        return Props.create(DeviceHistoryDataStateStore.class,device,pipelinePublisher);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build();
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
        }
    }
}