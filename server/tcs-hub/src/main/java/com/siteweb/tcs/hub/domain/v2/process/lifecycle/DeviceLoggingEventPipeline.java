package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.v2.process.DeviceLoggingEventProcessor;
import com.siteweb.tcs.hub.domain.v2.process.DeviceLoggingEventStateStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:45
 **/

public class DeviceLoggingEventPipeline  extends DataPipeline<TcsDevice>{

    private static final String stateStoreName = "DeviceLoggingEventStateStore%s";

    private static final String processorName = "DeviceLoggingEventProcessor%s";

    public DeviceLoggingEventPipeline(ActorContext context, ActorRef pipelinePublisher,TcsDevice device) {
        super(context, pipelinePublisher,device);
    }

    @Override
    public void create() {
        storeActor = getContext().actorOf(DeviceLoggingEventStateStore.props(configEntity,pipelinePublisher),String.format(stateStoreName,configEntity.getId()));
        processorActor = getContext().actorOf(DeviceLoggingEventProcessor.props(configEntity,storeActor),String.format(processorName,configEntity.getId()));
    }
}
