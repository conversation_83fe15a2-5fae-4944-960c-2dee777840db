package com.siteweb.stream.common.stream;

import cn.hutool.core.lang.Filter;
import cn.hutool.core.util.ClassUtil;
import com.siteweb.stream.common.annotations.StreamResourceDescription;
import com.siteweb.stream.common.enums.StreamResourceEnum;
import com.siteweb.stream.common.runtime.StreamResource;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
/**
 * @ClassName: StreamResourceFactory
 * @descriptions: 流资源工厂
 * @author: xsx
 * @date: 2/15/2025 10:20 AM
 **/
public class StreamResourceFactory {
    private Map<StreamResourceEnum, Class<? extends StreamResource>> resourceNameStreamResourceMap = new HashMap<>();
    // 静态内部类，只有在调用 getInstance 时才会加载
    private static class SingletonHolder {
        private static final StreamResourceFactory INSTANCE = new StreamResourceFactory();
    }
    // 提供全局访问点
    public static StreamResourceFactory getInstance() {
        return StreamResourceFactory.SingletonHolder.INSTANCE;
    }
    private StreamResourceFactory() {
    }
    public void registerStreamResourceType(StreamResourceEnum streamResourceEnum, Class<? extends StreamResource> streamResource){
        resourceNameStreamResourceMap.put(streamResourceEnum,streamResource);
    }
    public StreamResource createStreamResource(StreamResourceDescriptor streamResourceDescriptor) throws Exception {
        if(!resourceNameStreamResourceMap.containsKey(streamResourceDescriptor.getResourceType())){
            throw new Exception("can not found the access resource type");
        }
        Class<? extends StreamResource> streamResourceClass = resourceNameStreamResourceMap.get(streamResourceDescriptor.getResourceType());
        StreamResource streamResource = streamResourceClass.getDeclaredConstructor().newInstance(streamResourceDescriptor);
        return streamResource;
    }
    public Class<? extends StreamResource> getStreamResourceClassByType(StreamResourceEnum streamResourceEnum){
        if(resourceNameStreamResourceMap.containsKey(streamResourceEnum)){
            return resourceNameStreamResourceMap.get(streamResourceEnum);
        }
        return null;
    }
    public void scanResourceType(){
        Filter<Class<?>> filter = clazz -> StreamResource.class.isAssignableFrom(clazz) && !clazz.equals(StreamResource.class);
        Set<Class<?>> classSet =  ClassUtil.scanPackage("com.vertiv", filter);
        classSet.forEach(e -> {
            try {
                StreamResourceDescription[] annotationsByType = e.getAnnotationsByType(StreamResourceDescription.class);
                if(annotationsByType.length != 0){
                    StreamResourceEnum streamResourceEnum = annotationsByType[0].streamResourceType();
                    resourceNameStreamResourceMap.put(streamResourceEnum, (Class<? extends StreamResource>) e);
                }
            }catch (Exception ex){
                ex.printStackTrace();
            }
        });
    }
}