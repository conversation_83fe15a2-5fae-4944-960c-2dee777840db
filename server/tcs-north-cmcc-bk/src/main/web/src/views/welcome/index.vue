<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getServerStatus } from "@/api/serverstatus";

defineOptions({
  name: "Welcome"
});

// 按钮示例数据
const buttonTypes = ["primary", "success", "warning", "danger", "info"];
const buttonSizes = ["large", "default", "small"];

// 表格示例数据
const tableData = [
  {
    date: "2023-05-03",
    name: "张三",
    address: "北京市朝阳区"
  },
  {
    date: "2023-05-04",
    name: "李四",
    address: "上海市浦东新区"
  },
  {
    date: "2023-05-05",
    name: "王五",
    address: "广州市天河区"
  }
];

// 表单数据
const form = ref({
  name: "",
  region: "",
  date: "",
  delivery: false,
  type: [],
  resource: "",
  desc: ""
});

// 步骤条数据
const active = ref(0);
const nextStep = () => {
  if (active.value++ > 2) active.value = 0;
};

// 评分数据
const rateValue = ref(3.5);

// 开关数据
const switchValue = ref(true);

// API请求数据
const apiData = ref(null);
const apiLoading = ref(false);
const apiError = ref(null);

// 获取服务器状态
const fetchServerStatus = async () => {
  apiLoading.value = true;
  apiError.value = null;

  try {
    const response = await getServerStatus();
    apiData.value = response;
  } catch (error) {
    apiError.value = error.message || "获取服务器状态失败";
    console.error("API请求错误:", error);
  } finally {
    apiLoading.value = false;
  }
};

// 页面加载时获取数据
onMounted(() => {
  fetchServerStatus();
});
</script>

<template>
  <div class="plugin-container">
    <!-- 头部区域 -->
    <div class="header-section">
      <h1 class="main-title">Element Plus 组件展示</h1>

      <!-- API请求示例区域 -->
      <div class="section-block api-demo">
        <h2 class="section-title">API请求示例</h2>
        <div class="section-content">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>服务器状态</span>
                <el-button
                  type="primary"
                  size="small"
                  @click="fetchServerStatus"
                  :loading="apiLoading"
                >
                  刷新数据
                </el-button>
              </div>
            </template>
            <div v-if="apiLoading" class="api-loading">
              <el-skeleton :rows="3" animated />
            </div>
            <div v-else-if="apiError" class="api-error">
              <el-alert type="error" :title="apiError" :closable="false" />
            </div>
            <div v-else-if="apiData" class="api-result">
              <pre class="json-content">{{ JSON.stringify(apiData, null, 2) }}</pre>
            </div>
            <div v-else class="api-empty">
              <el-empty description="暂无数据" />
            </div>
          </el-card>
        </div>
      </div>

      <!-- 路由跳转示例区域（预留） -->
      <div class="section-block router-demo">
        <h2 class="section-title">路由跳转示例</h2>
        <div class="section-content">
          <!-- 路由跳转示例内容将在这里添加 -->
          <el-alert
            title="路由跳转功能区域预留"
            type="info"
            description="这里将展示不同路由跳转的示例"
            :closable="false"
          />
        </div>
      </div>

      <!-- 国际化示例区域（预留） -->
      <div class="section-block i18n-demo">
        <h2 class="section-title">国际化示例</h2>
        <div class="section-content">
          <!-- 国际化示例内容将在这里添加 -->
          <el-alert
            title="国际化功能区域预留"
            type="info"
            description="这里将展示国际化功能的示例"
            :closable="false"
          />
        </div>
      </div>
    </div>

    <!-- 组件展示区域 -->
    <div class="components-section">
      <h2 class="section-title">组件概览</h2>

      <!-- 基础组件 -->
      <div class="section-block">
        <h3 class="component-category">基础组件</h3>

        <!-- 按钮 -->
        <div class="component-demo">
          <h4>Button 按钮</h4>
          <div class="demo-content">
            <div class="button-row">
              <el-button v-for="type in buttonTypes" :key="type" :type="type">
                {{ type }}
              </el-button>
              <el-button>默认按钮</el-button>
            </div>
            <div class="button-row">
              <el-button
                v-for="type in buttonTypes"
                :key="type"
                :type="type"
                plain
              >
                朴素按钮
              </el-button>
            </div>
            <div class="button-row">
              <el-button
                v-for="type in buttonTypes"
                :key="type"
                :type="type"
                round
              >
                圆角按钮
              </el-button>
            </div>
            <div class="button-row">
              <el-button v-for="size in buttonSizes" :key="size" :size="size">
                {{ size }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 布局 -->
        <div class="component-demo">
          <h4>Layout 布局</h4>
          <div class="demo-content">
            <el-row :gutter="20">
              <el-col :span="6"><div class="grid-content bg-purple" /></el-col>
              <el-col :span="6"
                ><div class="grid-content bg-purple-light"
              /></el-col>
              <el-col :span="6"><div class="grid-content bg-purple" /></el-col>
              <el-col :span="6"
                ><div class="grid-content bg-purple-light"
              /></el-col>
            </el-row>
            <el-row :gutter="20" class="mt-4">
              <el-col :span="8"><div class="grid-content bg-purple" /></el-col>
              <el-col :span="8"
                ><div class="grid-content bg-purple-light"
              /></el-col>
              <el-col :span="8"><div class="grid-content bg-purple" /></el-col>
            </el-row>
          </div>
        </div>

        <!-- 链接 -->
        <div class="component-demo">
          <h4>Link 链接</h4>
          <div class="demo-content">
            <div class="link-row">
              <el-link>默认链接</el-link>
              <el-link type="primary">主要链接</el-link>
              <el-link type="success">成功链接</el-link>
              <el-link type="warning">警告链接</el-link>
              <el-link type="danger">危险链接</el-link>
              <el-link type="info">信息链接</el-link>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单组件 -->
      <div class="section-block">
        <h3 class="component-category">表单组件</h3>

        <!-- 表单 -->
        <div class="component-demo">
          <h4>Form 表单</h4>
          <div class="demo-content">
            <el-form :model="form" label-width="120px">
              <el-form-item label="活动名称">
                <el-input v-model="form.name" />
              </el-form-item>
              <el-form-item label="活动区域">
                <el-select v-model="form.region" placeholder="请选择活动区域">
                  <el-option label="区域一" value="shanghai" />
                  <el-option label="区域二" value="beijing" />
                </el-select>
              </el-form-item>
              <el-form-item label="即时配送">
                <el-switch v-model="form.delivery" />
              </el-form-item>
              <el-form-item label="活动形式">
                <el-input v-model="form.desc" type="textarea" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary">立即创建</el-button>
                <el-button>取消</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 评分 -->
        <div class="component-demo">
          <h4>Rate 评分</h4>
          <div class="demo-content">
            <el-rate
              v-model="rateValue"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
              show-text
            />
          </div>
        </div>

        <!-- 开关 -->
        <div class="component-demo">
          <h4>Switch 开关</h4>
          <div class="demo-content">
            <el-switch v-model="switchValue" />
            <el-switch
              v-model="switchValue"
              class="ml-2"
              style="
                --el-switch-on-color: #13ce66;
                --el-switch-off-color: #ff4949;
              "
            />
          </div>
        </div>
      </div>

      <!-- 数据展示组件 -->
      <div class="section-block">
        <h3 class="component-category">数据展示组件</h3>

        <!-- 表格 -->
        <div class="component-demo">
          <h4>Table 表格</h4>
          <div class="demo-content">
            <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="date" label="日期" width="180" />
              <el-table-column prop="name" label="姓名" width="180" />
              <el-table-column prop="address" label="地址" />
            </el-table>
          </div>
        </div>

        <!-- 标签 -->
        <div class="component-demo">
          <h4>Tag 标签</h4>
          <div class="demo-content">
            <el-tag>标签一</el-tag>
            <el-tag class="ml-2" type="success">标签二</el-tag>
            <el-tag class="ml-2" type="info">标签三</el-tag>
            <el-tag class="ml-2" type="warning">标签四</el-tag>
            <el-tag class="ml-2" type="danger">标签五</el-tag>
          </div>
        </div>

        <!-- 进度条 -->
        <div class="component-demo">
          <h4>Progress 进度条</h4>
          <div class="demo-content">
            <el-progress :percentage="50" />
            <el-progress :percentage="100" status="success" class="mt-2" />
            <el-progress :percentage="70" status="warning" class="mt-2" />
            <el-progress :percentage="50" status="exception" class="mt-2" />
          </div>
        </div>
      </div>

      <!-- 导航组件 -->
      <div class="section-block">
        <h3 class="component-category">导航组件</h3>

        <!-- 步骤条 -->
        <div class="component-demo">
          <h4>Steps 步骤条</h4>
          <div class="demo-content">
            <el-steps :active="active" finish-status="success">
              <el-step title="步骤 1" />
              <el-step title="步骤 2" />
              <el-step title="步骤 3" />
            </el-steps>
            <el-button style="margin-top: 12px" @click="nextStep"
              >下一步</el-button
            >
          </div>
        </div>

        <!-- 标签页 -->
        <div class="component-demo">
          <h4>Tabs 标签页</h4>
          <div class="demo-content">
            <el-tabs>
              <el-tab-pane label="用户管理">用户管理内容</el-tab-pane>
              <el-tab-pane label="配置管理">配置管理内容</el-tab-pane>
              <el-tab-pane label="角色管理">角色管理内容</el-tab-pane>
              <el-tab-pane label="定时任务补偿">定时任务补偿内容</el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>

      <!-- 反馈组件 -->
      <div class="section-block">
        <h3 class="component-category">反馈组件</h3>

        <!-- 警告 -->
        <div class="component-demo">
          <h4>Alert 警告</h4>
          <div class="demo-content">
            <el-alert title="成功提示的文案" type="success" />
            <el-alert title="消息提示的文案" type="info" class="mt-2" />
            <el-alert title="警告提示的文案" type="warning" class="mt-2" />
            <el-alert title="错误提示的文案" type="error" class="mt-2" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.plugin-container {
  padding: 20px;
}

.header-section {
  margin-bottom: 30px;
}

.main-title {
  font-size: 28px;
  margin-bottom: 20px;
  color: #303133;
}

.section-title {
  font-size: 22px;
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.section-block {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.component-category {
  font-size: 20px;
  margin-bottom: 15px;
  color: #409eff;
}

.component-demo {
  margin-bottom: 20px;
}

.component-demo h4 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #606266;
}

.demo-content {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.button-row,
.link-row {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-loading,
.api-error,
.api-result,
.api-empty {
  min-height: 100px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.json-content {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 400px;
  overflow: auto;
  text-align: left;
}
</style>
