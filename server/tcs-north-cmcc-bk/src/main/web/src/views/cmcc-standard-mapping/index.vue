<template>
  <div class="cmcc-standard-mapping">
    <el-card class="box-card">
      <template #header>
        <div class="card-header flex-bc">
          <span class="font-bold text-lg">移动标准化映射</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="设备类型标准化" name="device">
          <DeviceStandardTab 
            ref="deviceTabRef"
            @loading-change="handleLoadingChange"
          />
        </el-tab-pane>
        
        <el-tab-pane label="局站标准化" name="station">
          <StationStandardTab 
            ref="stationTabRef"
            @loading-change="handleLoadingChange"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import DeviceStandardTab from "./components/DeviceStandardTab.vue";
import StationStandardTab from "./components/StationStandardTab.vue";

const activeTab = ref("device");
const loading = ref(false);
const deviceTabRef = ref();
const stationTabRef = ref();

onMounted(() => {
  // 页面加载时初始化数据
  console.log('Component mounted, activeTab:', activeTab.value); // 调试日志
  // 延迟一下确保子组件已经挂载完成
  setTimeout(() => {
    handleTabClick({ name: activeTab.value });
  }, 100);
});

const handleTabClick = (tab: any) => {
  console.log('Tab clicked:', tab.name); // 调试日志
  
  if (tab.name === "device" && deviceTabRef.value) {
    console.log('Loading device tab data...'); // 调试日志
    deviceTabRef.value.loadData();
  } else if (tab.name === "station" && stationTabRef.value) {
    console.log('Loading station tab data...'); // 调试日志
    stationTabRef.value.loadData();
  }
};

const handleLoadingChange = (isLoading: boolean) => {
  loading.value = isLoading;
};
</script>

<style scoped>
.cmcc-standard-mapping {
  padding: 20px;
}

.box-card {
  min-height: calc(100vh - 120px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-bc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>