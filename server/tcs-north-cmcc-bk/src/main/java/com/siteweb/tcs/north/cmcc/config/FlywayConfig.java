package com.siteweb.tcs.north.cmcc.config;

import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;

/**
 * Flyway数据库迁移配�?
 */
@Configuration
public class FlywayConfig {

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Value("${plugin.id}")
    private String pluginId;

    @Autowired
    private ResourceRegistry resourceRegistry;

    @Bean(name = "northCmccFlyway")
    @DependsOn("northCmccDataSource")
    public Flyway flyway(@Qualifier("northCmccDataSource") DataSource dataSource) {
        Resource resource = resourceRegistry.get(dbResourceId, pluginId);
        String databaseProductName = switch (ResourceType.fromCode(resource.getType())){
            case POSTGRESQL -> "postgresql";
            case MYSQL -> "mysql";
            case H2 -> "h2";
            case OPENGAUSS -> "opengauss";
            case DAMENG -> "dameng";
            default -> "h2";
        };


        Flyway flyway = Flyway.configure()
            .dataSource(dataSource)
            .locations("classpath:db/tcs-north-cmcc/db/"+ databaseProductName)
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .table("north_cmcc_schema_history")
            .load();

        flyway.migrate();

        return flyway;
    }
}
