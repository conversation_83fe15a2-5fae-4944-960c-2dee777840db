package com.siteweb.tcs.north.cmcc.config;

import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.plugin.common.sharding.DefaultShardingMessageExtractor;
import com.siteweb.tcs.s6.access.connector.process.NorthS6GatewayProxy;
import com.siteweb.tcs.s6.access.dal.dto.SinkDTO;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * 北向S6Gateway集群配置
 * <p>
 * 用于创建和管理分片Actor
 * </p>
 */
@Configuration
public class NorthCmccGatewayCluster {
    
    @Value("${north.s6.gateway.sharding.name:NorthS6GatewayProxy}")
    private String gatewayShardingName;

    @Value("${plugin.gateway.sharding.count:10}")
    private int numberOfShards = 10;

    @Autowired
    private SinkDTO sinkDTO;

    /**
     * 创建Gateway分片�?
     */
    @Bean("tcs-north-s6-gateway-sharding")
    public ActorRef createGatewaySharding() throws IOException {
        var extractor = new DefaultShardingMessageExtractor(numberOfShards);
        return ClusterContext.createGatewaySharding(gatewayShardingName,
                entityId -> NorthS6GatewayProxy.props(Long.parseLong(entityId),sinkDTO),
                extractor);
    }
} 
