package com.siteweb.tcs.north.cmcc.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配�?
 */
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.north.cmcc.dal.mapper"}, sqlSessionFactoryRef = "northCmccSqlSessionFactory")
public class DataSourceConfig {

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Value("${plugin.id}")
    private String pluginId;

    @Autowired
    private ResourceRegistry resourceRegistry;

    @Autowired
    private ServiceRegistry serviceRegistry;

    @Value("${plugin.middleware.siteweb-persistent.primary}")
    private String sitewebPersistentResourceId;



    @Bean(name = "northCmccDataSource")
    public DataSource northCmccDataSource() {
        // 使用带引用计数的方法，传入插件ID作为引用�?
        return resourceRegistry.getDataSource(dbResourceId, pluginId);
    }

    @Bean(name = "northCmccTransactionManager")
    public DataSourceTransactionManager northCmccTransactionManager(@Qualifier("northCmccDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "northCmccSqlSessionFactory")
    public SqlSessionFactory northCmccSqlSessionFactory(@Qualifier("northCmccDataSource") DataSource dataSource,
                                                       @Autowired(required = false) MybatisPlusInterceptor mybatisPlusInterceptor) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/tcs-north-cmcc/*.xml"));

        // 设置实体类扫描包路径
        bean.setTypeAliasesPackage("com.siteweb.tcs.north.cmcc.dal.entity");

        // 添加MyBatis Plus插件
        if (mybatisPlusInterceptor != null) {
            bean.setPlugins(mybatisPlusInterceptor);
        }

        return bean.getObject();
    }

    @Bean(name = "cmccSitewebPersistentService")
    public SitewebPersistentService cmccSitewebPersistentService() throws Exception {
        return serviceRegistry.getSitewebPersistentService(sitewebPersistentResourceId,pluginId,pluginId,"north-cmcc-plugin");
    }

}
