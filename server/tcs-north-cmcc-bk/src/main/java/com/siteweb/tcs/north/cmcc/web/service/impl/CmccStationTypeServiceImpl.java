package com.siteweb.tcs.north.cmcc.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.cmcc.dal.dto.CmccSiteInfoDTO;
import com.siteweb.tcs.north.cmcc.dal.entity.CmccStationType;
import com.siteweb.tcs.north.cmcc.dal.mapper.CmccStationTypeMapper;
import com.siteweb.tcs.north.cmcc.web.service.ICmccStationTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 移动标准化局站类型表 Service 实现�?
 */
@Service
public class CmccStationTypeServiceImpl extends ServiceImpl<CmccStationTypeMapper, CmccStationType> implements ICmccStationTypeService {

    @Resource
    private CmccStationTypeMapper cmccStationTypeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAndBatchInsert(List<CmccStationType> stationTypeList) {
        // 清空表数�?
        this.remove(null);
        
        // 批量插入新数�?
        if (stationTypeList != null && !stationTypeList.isEmpty()) {
            return this.saveBatch(stationTypeList);
        }
        return true;
    }

    @Override
    public void clearAndAdd(CmccSiteInfoDTO cmccSiteInfoDTO){
        LambdaQueryWrapper<CmccStationType> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CmccStationType::getStationTypeId,cmccSiteInfoDTO.getSiteType());
        CmccStationType cmccStationType = cmccSiteInfoDTO.toCmccStationType();
        baseMapper.insert(cmccStationType);
    }
}

