package com.siteweb.tcs.north.cmcc.web.lcm;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.north.cmcc.dal.dto.CmccSiteInfoDTO;
import com.siteweb.tcs.north.cmcc.dal.entity.CmccSiteMap;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;
import com.siteweb.tcs.s6.access.web.sevice.IGatewayMapService;
import com.siteweb.tcs.siteweb.dto.CreateMonitorUnitDTO;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * CMCC FSU处理�?
 * 负责处理FSU相关的创建、更新、删除逻辑
 */
@Component
@Slf4j
public class CmccFSUHandler extends CmccBaseHandler {

    @Autowired
    private IGatewayMapService gatewayMapService;

    @Autowired
    private CmccDeviceHandler cmccDeviceHandler;

    /**
     * 处理FSU信息
     */
    public Integer handleFSUInfo(GatewayConfigChangeDto gatewayConfigChangeDto,
                                 Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteInfoDTO siteInfoDTO = cmccConfigParser.parseSiteInfo(gatewayConfigChangeDto);
        
        switch (gatewayConfigChangeDto.getLifeCycleEvent()) {
            case CREATE:
                return createFSU(gatewayConfigChangeDto, siteInfoDTO, cmccSiteMapMap);
            case DELETE:
                return deleteFSU(gatewayConfigChangeDto);
            case UPDATE:
                return updateFSU(gatewayConfigChangeDto, siteInfoDTO, cmccSiteMapMap);
            case NOT_CHANGE:
                GatewayMap gatewayMap = gatewayMapService.getByGatewayId(gatewayConfigChangeDto.getId());
                return gatewayMap.getNorthMonitorUnitId();
            default:
                return null;
        }
    }

    /**
     * 创建FSU
     */
    private Integer createFSU(GatewayConfigChangeDto gatewayConfigChangeDto,
                              CmccSiteInfoDTO siteInfoDTO,
                              Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteMap cmccSiteMap = getSiteMap(siteInfoDTO.getSiteId(), cmccSiteMapMap);
        if (cmccSiteMap == null) {
            throw new IllegalStateException("站点映射不存�? " + siteInfoDTO.getSiteId());
        }

        CreateMonitorUnitDTO createMonitorUnitDTO = cmccConfigParser.parseFSUInfo(gatewayConfigChangeDto);
        MonitorUnitDTO monitorUnit = createMonitorUnitDTO.toMonitorUnit(cmccSiteMap.getStationId());
        try {
            sitewebPersistentService.executeTransaction(()->{
                sitewebPersistentService.getConfigAPI().createV3ForMonitorUnit(monitorUnit);
            });
        }catch (Exception ex){
            log.error("创建监控单元失败", ex);
            return null;
        }
        // 保存网关映射
        saveGatewayMap(gatewayConfigChangeDto.getId(), monitorUnit.getMonitorUnitId(),cmccSiteMap.getStationId());

        return monitorUnit.getMonitorUnitId();
    }

    /**
     * 删除FSU
     */
    private Integer deleteFSU(GatewayConfigChangeDto gatewayConfigChangeDto) {
        // TODO: 实现删除逻辑
        GatewayMap gatewayMap = gatewayMapService.getByGatewayId(gatewayConfigChangeDto.getId());
        if(ObjectUtil.isEmpty(gatewayMap)) return null;
        sitewebPersistentService.getConfigAPI().deleteForMonitorUnit(gatewayMap.getNorthMonitorUnitId(),true);
        //删除DeviceMap，模�?
        cmccDeviceHandler.handleDeleteByGatewayId(gatewayConfigChangeDto.getId());
        //删除GatewayMap
        gatewayMapService.deleteByGatewayId(gatewayConfigChangeDto.getId());
        return gatewayMap.getNorthMonitorUnitId();
    }

    /**
     * 更新FSU
     */
    private Integer updateFSU(GatewayConfigChangeDto gatewayConfigChangeDto,
                              CmccSiteInfoDTO siteInfoDTO,
                              Map<String, CmccSiteMap> cmccSiteMapMap) {
        // TODO: 实现更新逻辑
        // 查出GatewayMap
        CmccSiteMap cmccSiteMap = getSiteMap(siteInfoDTO.getSiteId(), cmccSiteMapMap);
        GatewayMap gatewayMap = gatewayMapService.getByGatewayId(gatewayConfigChangeDto.getId());
        if(ObjectUtil.isEmpty(gatewayMap)) return null;
        CreateMonitorUnitDTO createMonitorUnitDTO = cmccConfigParser.parseFSUInfo(gatewayConfigChangeDto);
        MonitorUnitDTO monitorUnit = createMonitorUnitDTO.toMonitorUnit(cmccSiteMap.getStationId());
        monitorUnit.setMonitorUnitId(gatewayMap.getNorthMonitorUnitId());
        sitewebPersistentService.getConfigAPI().updateForMonitorUnit(monitorUnit);
        return gatewayMap.getNorthMonitorUnitId();
    }

    /**
     * 保存网关映射
     */
    private void saveGatewayMap(Long gatewayId, Integer monitorUnitId,Integer stationId) {
        GatewayMap gatewayMap = new GatewayMap();
        gatewayMap.setGatewayId(gatewayId);
        gatewayMap.setNorthMonitorUnitId(monitorUnitId);
        gatewayMap.setNorthStationId(stationId);
        gatewayMapService.save(gatewayMap);
    }
}
