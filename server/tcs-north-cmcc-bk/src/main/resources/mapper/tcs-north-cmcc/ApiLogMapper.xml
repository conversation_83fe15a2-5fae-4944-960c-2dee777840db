<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.s6.access.dal.mapper.ApiLogMapper">
    
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.s6.access.dal.entity.ApiLog">
        <id column="id" property="id" />
        <result column="api_path" property="apiPath" />
        <result column="request_method" property="requestMethod" />
        <result column="request_params" property="requestParams" />
        <result column="response_data" property="responseData" />
        <result column="response_status" property="responseStatus" />
        <result column="execution_time" property="executionTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id, api_path, request_method, request_params, response_data, 
        response_status, execution_time, create_time, update_time, is_deleted
    </sql>

    <!-- 根据API路径查询日志 -->
    <select id="selectByApiPath" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_north_s6_api_log
        WHERE api_path = #{apiPath}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据请求方法查询日志 -->
    <select id="selectByRequestMethod" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_north_s6_api_log
        WHERE request_method = #{requestMethod}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询指定时间范围内的日志 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_north_s6_api_log
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 统计成功请求数量 -->
    <select id="countSuccessRequests" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM tcs_north_s6_api_log
        WHERE response_status >= 200 AND response_status = 300
        AND is_deleted = 0
    </select>

    <!-- 统计失败请求数量 -->
    <select id="countErrorRequests" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM tcs_north_s6_api_log
        WHERE response_status >= 400
        AND is_deleted = 0
    </select>

    <!-- 计算平均响应时间 -->
    <select id="calculateAvgResponseTime" resultType="java.lang.Double">
        SELECT AVG(execution_time)
        FROM tcs_north_s6_api_log
        WHERE execution_time IS NOT NULL
        AND is_deleted = 0
    </select>

    <!-- 插入API日志 -->
    <insert id="insert" parameterType="com.siteweb.tcs.s6.access.dal.entity.ApiLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tcs_north_s6_api_log (
            api_path, request_method, request_params, response_data, 
            response_status, execution_time, create_time, update_time, is_deleted
        ) VALUES (
            #{apiPath}, #{requestMethod}, #{requestParams}, #{responseData}, 
            #{responseStatus}, #{executionTime}, #{createTime}, #{updateTime}, #{deleted}
        )
    </insert>

    <!-- 批量插入API日志 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO tcs_north_s6_api_log (
            api_path, request_method, request_params, response_data, 
            response_status, execution_time, create_time, update_time, is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.apiPath}, #{item.requestMethod}, #{item.requestParams}, #{item.responseData}, 
             #{item.responseStatus}, #{item.executionTime}, #{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>

</mapper> 
