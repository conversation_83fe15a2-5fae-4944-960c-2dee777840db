plugin:
  etl:
    # 数据库配置
    datasource:
      url: ${SPRING_DATASOURCE_URL:********************************************************************************************************************}
      username: ${SPRING_DATASOURCE_USERNAME:root}
      password: ${SPRING_DATASOURCE_PASSWORD:password}
      driver-class-name: com.mysql.cj.jdbc.Driver

    # 缓存配置
    cache:
      # 缓存类型：redis, simple
      type: redis
      # 缓存过期时间（秒）
      expiration: 3600

    # 数据处理配置
    processing:
      # 批处理大小
      batch-size: 1000
      # 线程池大小
      thread-pool-size: 5
      # 最大缓存大小
      max-cache-size: 10000

    # 数据质量配置
    quality:
      # 报告生成间隔（小时）
      report-interval: 24
      # 质量阈值
      quality-threshold: 0.8
      # 异常检测敏感度
      anomaly-sensitivity: 0.7

    # 日志配置
    logging:
      # 日志级别
      level: INFO
      # 日志保留天数
      retention-days: 30

    # NiFi配置
    nifi:
      # 是否启用模拟NiFi客户端
      mock-enabled: ${NIFI_MOCK_ENABLED:true}
      # NiFi API基础URL
      base-url: ${NIFI_BASE_URL:http://localhost:8080/nifi-api}
      # 用户名
      username: ${NIFI_USERNAME:}
      # 密码
      password: ${NIFI_PASSWORD:}
      # 访问令牌
      token: ${NIFI_TOKEN:}
      # 是否使用SSL
      use-ssl: ${NIFI_USE_SSL:false}
      # 信任所有证书
      trust-all-certs: ${NIFI_TRUST_ALL_CERTS:false}

      # SSL配置
      # 信任库路径
      trust-store-path: ${NIFI_TRUST_STORE_PATH:}
      # 信任库密码
      trust-store-password: ${NIFI_TRUST_STORE_PASSWORD:}
      # 密钥库路径
      key-store-path: ${NIFI_KEY_STORE_PATH:}
      # 密钥库密码
      key-store-password: ${NIFI_KEY_STORE_PASSWORD:}
      # 密钥密码
      key-password: ${NIFI_KEY_PASSWORD:}

      # Kerberos配置
      # 是否使用Kerberos认证
      use-kerberos: ${NIFI_USE_KERBEROS:false}
      # Kerberos配置文件路径
      krb5-conf-path: ${NIFI_KRB5_CONF_PATH:}
      # Kerberos用户
      kerberos-user: ${NIFI_KERBEROS_USER:}
      # Kerberos密码
      kerberos-password: ${NIFI_KERBEROS_PASSWORD:}

      # 连接池配置
      # 连接超时（毫秒）
      connect-timeout: 30000
      # 读取超时（毫秒）
      read-timeout: 30000
      # 最大连接数
      max-connections: 100
      # 每个路由的最大连接数
      max-connections-per-route: 20

      # NiFi配置
      # 根进程组ID
      root-process-group-id: ${NIFI_ROOT_PROCESS_GROUP_ID:root}
      # 轮询间隔（毫秒）
      polling-interval: 5000

      # 自定义头信息
      custom-headers:
        X-ProxyContextPath: ${NIFI_PROXY_CONTEXT_PATH:}

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.siteweb.tcs.north.etl.dal.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0

# Flyway配置
spring:
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/db
    table: ${plugin.etl.datasource.table-prefix:etl_}schema_history
