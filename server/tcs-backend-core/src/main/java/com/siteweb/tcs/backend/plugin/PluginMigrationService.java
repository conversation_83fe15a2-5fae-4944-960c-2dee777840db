package com.siteweb.tcs.backend.plugin;

import com.siteweb.tcs.common.db.DatabaseMigrationService;
import org.flywaydb.core.Flyway;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.io.File;
import java.util.Map;

/**
 * 插件数据库迁移服务
 * 负责管理插件相关的数据库迁移操作
 */
@Service
public class PluginMigrationService {

    private static final Logger logger = LoggerFactory.getLogger(PluginMigrationService.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private DatabaseMigrationService databaseMigrationService;

    @Value("${tcs.plugin.db.table-prefix:plugin_}")
    private String tablePrefix;

    @Value("${tcs.plugin.db.baseline-version:1}")
    private String baselineVersion;

    /**
     * 执行插件数据库迁移
     *
     * @param pluginId 插件ID
     * @param pluginPath 插件路径
     * @return 是否迁移成功
     */
    public boolean migratePluginDatabase(String pluginId, String pluginPath) {
        logger.info("开始执行插件 {} 的数据库迁移", pluginId);

        try {
            // 构建插件特定的迁移配置
            String migrationLocation = String.format("filesystem:%s/sql", pluginPath);
            String schemaHistoryTable = tablePrefix + pluginId + "_schema_history";

            // 创建插件特定的Flyway实例
            org.flywaydb.core.api.configuration.Configuration configuration = Flyway.configure()
                    .dataSource(dataSource)
                    .locations(migrationLocation)
                    .baselineOnMigrate(true)
                    .baselineVersion(baselineVersion)
                    .table(schemaHistoryTable)
                    .validateOnMigrate(true)
                    .outOfOrder(false);

            Flyway flyway = new Flyway(configuration);

            // 检查SQL脚本目录是否存在
            File sqlDir = new File(pluginPath + "/sql");
            if (!sqlDir.exists() || !sqlDir.isDirectory()) {
                logger.info("插件 {} 没有SQL迁移脚本，跳过数据库迁移", pluginId);
                return true;
            }

            // 执行迁移
            flyway.migrate();

            // 验证迁移结果
            Map<String, Object> currentVersion = databaseMigrationService.getCurrentVersion();
            logger.info("插件 {} 数据库迁移完成，当前版本: {}", pluginId, currentVersion.get("version"));

            return true;
        } catch (Exception e) {
            logger.error("插件 {} 数据库迁移失败", pluginId, e);
            return false;
        }
    }

    /**
     * 清理插件数据库
     *
     * @param pluginId 插件ID
     * @return 是否清理成功
     */
    public boolean cleanPluginDatabase(String pluginId) {
        logger.info("开始清理插件 {} 的数据库", pluginId);

        try {
            String schemaHistoryTable = tablePrefix + pluginId + "_schema_history";

            // 创建用于清理的Flyway实例
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .table(schemaHistoryTable)
                    .load();

            // 清理数据库
            flyway.clean();

            logger.info("插件 {} 数据库清理完成", pluginId);
            return true;
        } catch (Exception e) {
            logger.error("清理插件 {} 数据库失败", pluginId, e);
            return false;
        }
    }

    /**
     * 修复插件数据库迁移
     *
     * @param pluginId 插件ID
     * @return 是否修复成功
     */
    public boolean repairPluginMigration(String pluginId) {
        logger.info("开始修复插件 {} 的数据库迁移", pluginId);

        try {
            String schemaHistoryTable = tablePrefix + pluginId + "_schema_history";

            // 创建用于修复的Flyway实例
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .table(schemaHistoryTable)
                    .load();

            // 修复迁移
            flyway.repair();

            logger.info("插件 {} 数据库迁移修复完成", pluginId);
            return true;
        } catch (Exception e) {
            logger.error("修复插件 {} 数据库迁移失败", pluginId, e);
            return false;
        }
    }
}