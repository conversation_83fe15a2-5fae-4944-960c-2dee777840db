package com.siteweb.tcs.backend.service;

import com.siteweb.tcs.hub.domain.letter.LiveMonitorUnitState;
import com.siteweb.tcs.hub.domain.process.LocalMonitorUnitStateCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MonitorUnitStateService {

    @Autowired
    private LocalMonitorUnitStateCache localMonitorUnitStateCache;

    public List<LiveMonitorUnitState> getAllMonitorUnitState()
    {
        return localMonitorUnitStateCache.queryAllMonitorUnitState();
    }
}
