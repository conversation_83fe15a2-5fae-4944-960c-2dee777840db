package com.siteweb.tcs.backend.plugin;

import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.runtime.ThingConnectPlugin;
import com.siteweb.tcs.common.runtime.ThingConnectPluginWrapper;
import lombok.extern.slf4j.Slf4j;
import org.pf4j.DefaultPluginFactory;
import org.pf4j.Plugin;
import org.pf4j.PluginWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Constructor;

/**
 * 此模块为插件对象工厂模块，负责创建插件实例及完成对插件依赖注入
 * <AUTHOR> (2024-05-08)
 **/
@Slf4j
@Component
public class ThingConnectPluginFactory extends DefaultPluginFactory implements ApplicationContextAware {

    private ApplicationContext hostApplicationContext;
    @Autowired
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    @Override
    protected Plugin createInstance(Class<?> pluginClass, PluginWrapper pluginWrapper) {
        PluginContext context = new PluginContext((ThingConnectPluginWrapper)pluginWrapper, hostApplicationContext, requestMappingHandlerMapping);
        ThingConnectPlugin plugin = null;
        try {
            Constructor<?> constructor = pluginClass.getConstructor(PluginContext.class);
            plugin = (ThingConnectPlugin) constructor.newInstance(context);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return plugin;
    }

    @Override
    public void setApplicationContext(ApplicationContext hostApplicationContext) {
        this.hostApplicationContext = hostApplicationContext;
    }
}