package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.backend.service.EquipmentControlCommandService;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
@RestController
@RequestMapping("/control_command")
public class ControlCommandController {

    EquipmentControlCommandService equipmentControlCommandService;

    public ControlCommandController() {
        this.equipmentControlCommandService = new EquipmentControlCommandService();
    }


    //获取所有活动告警列表
    @ApiOperation("获取所有活动控制命令列表")
    @GetMapping("/active_list")
    public ResponseEntity<ResponseResult> getAllActiveAlarms() {
        return ResponseHelper.successful(equipmentControlCommandService.getAllControlCommands());
    }



}
