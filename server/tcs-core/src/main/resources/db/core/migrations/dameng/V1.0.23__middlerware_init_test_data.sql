INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-h2-config-001',
    'H2',
    '测试H2资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "testdb",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'cucc-h2-config-primary',
    'H2',
    'cucc H2 数据库资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "test_cucc",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'cmcc-h2-config-primary',
    'H2',
    'cmcc H2 数据库资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "test_cmcc",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'omc-h2-config-primary',
    'H2',
    'omc H2 数据库资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "test_omc",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

-- 1. MySQL测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-mysql-config-001',
    'MYSQL',
    '测试MySQL资源',
    '用于测试的MySQL资源配置',
    '{
        "host": "localhost",
        "port": 3306,
        "database": "tcs_middleware",
        "username": "root",
        "password": "root",
        "minIdle": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000
    }',
    'ENABLED',
    'system'
);


-- 2. PostgreSQL测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-postgresql-config-001',
    'POSTGRESQL',
    '测试PostgreSQL资源',
    '用于测试的PostgreSQL资源配置',
    '{
        "host": "*************",
        "port": 5432,
        "database": "postgres",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test"
    }',
    'ENABLED',
    'system'
);
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'omc-postgres-config-primary',
    'POSTGRESQL',
    'omc PostgreSQL资源',
    'omc PostgreSQL资源配置',
    '{
        "host": "************",
        "port": 5432,
        "database": "siteweb_omc",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test"
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'cmcc-postgres-config-primary',
    'POSTGRESQL',
    'cmcc PostgreSQL资源',
    'cmcc PostgreSQL资源配置',
    '{
        "host": "************",
        "port": 5432,
        "database": "tcs_cmcc",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test"
    }',
    'ENABLED',
    'system'
);

-- 3. Redis测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-redis-config-001',
    'REDIS',
    '测试Redis资源',
    '用于测试的Redis资源配置',
    '{
        "host": "**************",
        "port": 6379,
        "password": "siteweb1!",
        "database": 0,
        "connectionTimeout": 2000,
        "maxTotal": 8,
        "maxIdle": 8,
        "minIdle": 0
    }',
    'ENABLED',
    'system'
);

-- 4. MQTT测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-mqtt-config-001',
    'MQTT',
    '测试MQTT资源',
    '用于测试的MQTT资源配置',
    '{
        "serverUri": "tcp://*************:1883",
        "clientId": "tcs-middleware-test",
        "username": "",
        "password": "",
        "cleanSession": true,
        "connectionTimeout": 30,
        "keepAliveInterval": 60,
        "automaticReconnect": true
    }',
    'ENABLED',
    'system'
);

-- 5. HTTP服务器测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-http-server-config-001',
    'AKKA_HTTP_SERVER',
    '测试HTTP服务器',
    '用于测试的HTTP服务器资源配置',
    '{
        "host": "localhost",
        "port": 8088,
        "idleTimeout": 60,
        "backlog": 100
    }',
    'ENABLED',
    'system'
);

-- =====================================================
-- 测试数据初始化 - 服务配置
-- =====================================================

-- 1. 数据库服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-db-service-001',
    'DATABASE',
    '测试数据库服务',
    '用于测试的数据库服务配置',
    '{}',
    'test-postgresql-config-001',
    'ENABLED',
    'system'
);

INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'omc-siteweb-persistent-service',
    'SITEWEB_PERSISTENT',
    'omc Siteweb持久化服务',
    'omc Siteweb持久化服务配置，封装tcs-siteweb模块的服务，使用PostgreSQL数据库',
    '{}',
    'omc-postgres-config-primary',
    'ENABLED',
    'system'
);

INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'omc-siteweb-persistent-service-h2',
    'SITEWEB_PERSISTENT',
    'omc Siteweb持久化服务',
    'omc Siteweb持久化服务配置，封装tcs-siteweb模块的服务，使用PostgreSQL数据库',
    '{}',
    'omc-h2-config-primary',
    'ENABLED',
    'system'
);


-- 2. OpenGauss测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-opengauss-config-001',
    'OPENGAUSS',
    '测试OpenGauss资源',
    '用于测试的OpenGauss资源配置',
    '{
        "host": "*************",
        "port": 5432,
        "database": "test_cmcc",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test",
        "compatibilityMode": "PG",
        "characterEncoding": "UTF-8"
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'test-local-filesystem-config-001',
           'LOCAL_FILESYSTEM',
           '测试本地文件系统',
           '用于测试的本地文件系统资源配置',
           '{
               "rootDirectory": "./data/files/test",
               "createDirectories": true
           }',
           'ENABLED',
           'system'
       );

-- =====================================================
-- 文件系统服务初始化 - 服务配置
-- =====================================================

-- 1. 本地文件系统服务配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'test-local-filesystem-service-001',
           'FILESYSTEM',
           '测试本地文件系统服务',
           '基于本地文件系统的文件管理服务',
           '{
               "threadPoolSize": 5,
               "asyncMode": false
           }',
           'test-local-filesystem-config-001',
           'ENABLED',
           'system'
       );


INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'cmcc-akka-http-server-config',
           'AKKA_HTTP_SERVER',
           'CMCC Akka HTTP服务器',
           'CMCC模块使用的Akka HTTP服务器配置',
           '{
               "host": "0.0.0.0",
               "port": 8080,
               "idleTimeout": 60,
               "backlog": 100
           }',
           'ENABLED',
           'system'
       );

-- 添加Netty HTTP服务器配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'cmcc-netty-http-server-config',
           'NETTY_HTTP_SERVER',
           'CMCC Netty HTTP服务器',
           'CMCC模块使用的Netty HTTP服务器配置',
           '{
               "host": "0.0.0.0",
               "port": 8080,
               "idleTimeout": 60,
               "backlog": 100
           }',
           'ENABLED',
           'system'
       );
-- 添加HTTP服务器服务配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'cmcc-akka-http-server-service',
           'HTTP_SERVER',
           'CMCC HTTP服务器服务',
           'CMCC模块使用的HTTP服务器服务',
            '{
               "maxRequestFailureRecords": 8000,
               "enableRequestFailureRecording": true,
               "requestFailureRetentionHours": 48
           }',
           'cmcc-akka-http-server-config',
           'ENABLED',
           'system'
       );

-- 添加Netty HTTP服务器服务配置示例
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'cmcc-netty-http-server-service',
           'HTTP_SERVER',
           'CMCC Netty HTTP服务器服务',
           'CMCC模块使用的Netty HTTP服务器服务',
            '{
               "maxRequestFailureRecords": 8000,
               "enableRequestFailureRecording": true,
               "requestFailureRetentionHours": 48
           }',
           'cmcc-netty-http-server-config',
           'ENABLED',
           'system'
       );

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'test-nio-udp-server-config-001',
           'NIO_UDP_SERVER',
           '测试NIO UDP DS服务器',
           '用于测试的NIO UDP服务器资源配置',
           '{
               "bindIp": "0.0.0.0",
               "bindPort": 9000,
               "receiveBufferSize": 65536,
               "sendBufferSize": 65536,
               "reuseAddress": false,
               "broadcast": false
           }',
           'ENABLED',
           'system'
       );

INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'test-nio-udp-server-service-001',
           'UDP_SERVER',
           '测试NIO UDP DS服务器服务',
           '基于NIO UDP服务器的通信服务',
           '{
               "maxTrafficStatsRecords": 5000,
               "enableTrafficStatsRecording": true,
               "trafficStatsRetentionHours": 12,
               "enableErrorLogging": true,
               "errorLogRetentionHours": 24
           }',
           'test-nio-udp-server-config-001',
           'ENABLED',
           'system'
       );

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'test-nio-udp-server-config-002',
           'NIO_UDP_SERVER',
           '测试NIO UDP RDS服务器',
           '用于测试的NIO UDP 服务器资源配置',
           '{
               "bindIp": "0.0.0.0",
               "bindPort": 7000,
               "receiveBufferSize": 65536,
               "sendBufferSize": 65536,
               "reuseAddress": false,
               "broadcast": false
           }',
           'ENABLED',
           'system'
       );

INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'test-nio-udp-server-service-002',
           'UDP_SERVER',
           '测试NIO UDP RDS服务器服务',
           '基于NIO UDP服务器的通信服务',
           '{
               "maxTrafficStatsRecords": 5000,
               "enableTrafficStatsRecording": true,
               "trafficStatsRetentionHours": 12,
               "enableErrorLogging": true,
               "errorLogRetentionHours": 24
           }',
           'test-nio-udp-server-config-002',
           'ENABLED',
           'system'
       );