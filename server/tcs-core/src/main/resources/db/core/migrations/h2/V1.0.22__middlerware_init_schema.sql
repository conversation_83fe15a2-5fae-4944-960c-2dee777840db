-- =====================================================
-- 表结构定义
-- =====================================================

-- 创建资源类型表
CREATE TABLE IF NOT EXISTS mw_resource_type (
    id VARCHAR(50) PRIMARY KEY COMMENT '资源类型唯一标识 (如 "MYSQL", "KAFKA")',
    name VARCHAR(100) NOT NULL COMMENT '资源类型名称 (如 "MySQL", "Kafka")',
    category VARCHAR(50) NOT NULL COMMENT '资源类别（如 RELATIONAL_DB, MESSAGE_QUEUE）',
    description TEXT COMMENT '资源类型描述',
    default_config TEXT NOT NULL COMMENT '配置模板（TEXT），定义了创建该类型资源配置所需的参数结构',
    ui_component VARCHAR(50) NOT NULL COMMENT '前端页面资源配置组件',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源类型表';

-- 创建资源配置表
CREATE TABLE IF NOT EXISTS mw_resource_configuration (
    id VARCHAR(50) PRIMARY KEY COMMENT '资源配置唯一标识 (UUID 或其他全局唯一 ID)',
    resource_id VARCHAR(50) NOT NULL COMMENT '关联的资源类型ID',
    name VARCHAR(100) NOT NULL COMMENT '资源配置名称（如"生产主库"，"测试Redis"）',
    description TEXT COMMENT '资源配置描述',
    config TEXT NOT NULL COMMENT '资源配置详情（TEXT），存储了具体的连接参数、账号密码等',
    status VARCHAR(20) NOT NULL DEFAULT 'DISABLED' COMMENT '资源配置的管理状态（ENABLED/DISABLED/PENDING）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    FOREIGN KEY (resource_id) REFERENCES mw_resource_type(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源配置表';

-- 创建服务类型表
CREATE TABLE IF NOT EXISTS mw_service_type (
    id VARCHAR(50) PRIMARY KEY COMMENT '服务类型唯一标识 (如 "DATABASE_SERVICE", "CACHE_SERVICE")',
    name VARCHAR(100) NOT NULL COMMENT '服务类型名称 (如 "Database Service", "Cache Service")',
    description TEXT COMMENT '服务类型描述',
    default_config TEXT COMMENT '配置模板（TEXT），定义服务层面的配置参数',
    ui_component VARCHAR(50) NOT NULL COMMENT '前端页面资源配置组件',
    supported_resource_category VARCHAR(50) NOT NULL COMMENT '支持的资源类别（如 RELATIONAL_DB, KEY_VALUE）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务类型表';

-- 创建服务配置表
CREATE TABLE IF NOT EXISTS mw_service_configuration (
    id VARCHAR(50) PRIMARY KEY COMMENT '服务配置唯一标识',
    service_id VARCHAR(50) NOT NULL COMMENT '关联的服务类型ID',
    name VARCHAR(100) NOT NULL COMMENT '服务配置名称（如"主库服务"，"用户缓存服务"）',
    description TEXT COMMENT '服务配置描述',
    config TEXT COMMENT '服务配置详情（TEXT），存储服务实例特有的参数',
    resource_configuration_id VARCHAR(50) COMMENT '关联的资源配置ID',
    status VARCHAR(20) NOT NULL DEFAULT 'DISABLED' COMMENT '服务配置的管理状态（ENABLED/DISABLED/PENDING）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    FOREIGN KEY (service_id) REFERENCES mw_service_type(id),
    FOREIGN KEY (resource_configuration_id) REFERENCES mw_resource_configuration(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务配置表';

-- =====================================================
-- 索引创建
-- =====================================================

-- 创建资源配置表索引
CREATE INDEX idx_resource_configuration_resource_id ON mw_resource_configuration(resource_id);
CREATE INDEX idx_service_configuration_service_id ON mw_service_configuration(service_id);
CREATE INDEX idx_service_configuration_resource_configuration_id ON mw_service_configuration(resource_configuration_id);

-- =====================================================
-- 基础数据初始化 - 资源类型
-- =====================================================

-- 1. 关系型数据库资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'MYSQL',
    'MySQL',
    'RELATIONAL_DB',
    'MySQL关系型数据库',
    '{
        "host": "localhost",
        "port": 3306,
        "database": "tcs_middleware",
        "username": "root",
        "password": "ENC(password)",
        "minIdle": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000
    }',
    'mysql-config'
);

INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'POSTGRESQL',
    'PostgreSQL',
    'RELATIONAL_DB',
    'PostgreSQL关系型数据库',
    '{
        "host": "localhost",
        "port": 5432,
        "database": "tcs_middleware",
        "username": "postgres",
        "password": "ENC(password)",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "maxLifetime": 1800000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware",
        "autoCommit": true,
        "transactionIsolation": "TRANSACTION_READ_COMMITTED"
    }',
    'postgresql-config'
);

INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component, create_time, update_time) VALUES('OPENGAUSS', 'OpenGauss', 'RELATIONAL_DB', 'OpenGauss关系型数据库', '{"host": "localhost", "port": 5432, "schema": "public", "sslMode": "prefer", "database": "tcs_middleware", "password": "ENC(password)", "username": "gaussdb", "autoCommit": true, "sslEnabled": false, "idleTimeout": 600000, "maxLifetime": 1800000, "maxPoolSize": 20, "minPoolSize": 5, "applicationName": "TCS-Middleware", "characterEncoding": "UTF-8", "compatibilityMode": "PG", "connectionTimeout": 30000, "transactionIsolation": "TRANSACTION_READ_COMMITTED"}', 'opengauss-config', '2025-08-07 17:10:33.242', '2025-08-07 17:10:33.242');
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component, create_time, update_time) VALUES('DAMENG', 'Dameng', 'RELATIONAL_DB', 'Dameng关系型数据库', '{"host": "localhost", "port": 5236, "schema": "SYSDBA", "password": "ENC(password)", "poolName": "DamengPool", "username": "SYSDBA", "batchSize": 1000, "autoCommit": true, "jmxEnabled": false, "sslEnabled": false, "idleTimeout": 600000, "maxLifetime": 1800000, "maxPoolSize": 20, "minPoolSize": 5, "batchEnabled": true, "testOnBorrow": true, "testOnReturn": false, "testWhileIdle": true, "applicationName": "TCS-Middleware", "validationQuery": "SELECT 1 FROM DUAL", "characterEncoding": "UTF-8", "connectionTimeout": 30000, "prepStmtCacheSize": 250, "validationTimeout": 5, "prepStmtCacheEnabled": true, "transactionIsolation": "TRANSACTION_READ_COMMITTED", "prepStmtCacheSqlLimit": 2048, "leakDetectionThreshold": 60000, "timeBetweenEvictionRunsMillis": 60000}', 'dameng-config', '2025-08-07 17:10:33.242', '2025-08-07 17:10:33.242');

INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'H2',
    'H2 Database',
    'RELATIONAL_DB',
    'H2内存/文件数据库',
    '{
        "dbName": "testdb",
        "mode": "MEMORY",
        "filePath": "./h2db/testdb",
        "username": "sa",
        "password": "ENC(password)",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'h2-config'
);

-- 2. 键值存储资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'REDIS',
    'Redis',
    'KEY_VALUE_STORE',
    'Redis键值存储',
    '{
        "host": "localhost",
        "port": 6379,
        "password": "ENC(password)",
        "database": 0,
        "connectionTimeout": 2000,
        "maxTotal": 8,
        "maxIdle": 8,
        "minIdle": 0
    }',
    'redis-config'
);

-- 3. 消息队列资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'KAFKA',
    'Kafka',
    'MESSAGE_QUEUE',
    'Kafka消息队列',
    '{
        "bootstrapServers": "localhost:9092",
        "clientId": "tcs-middleware",
        "keySerializer": "org.apache.kafka.common.serialization.StringSerializer",
        "valueSerializer": "org.apache.kafka.common.serialization.StringSerializer",
        "acks": "all",
        "retries": 3,
        "batchSize": 16384,
        "lingerMs": 1,
        "bufferMemory": 33554432
    }',
    'kafka-config'
);

INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'MQTT',
    'MQTT(Mosquito)',
    'MESSAGE_QUEUE',
    'MQTT消息队列',
    '{
        "serverUri": "tcp://localhost:1883",
        "clientId": "tcs-middleware",
        "username": "",
        "password": "ENC(password)",
        "cleanSession": true,
        "connectionTimeout": 30,
        "keepAliveInterval": 60,
        "automaticReconnect": true
    }',
    'mos-mqtt-config'
);

-- 4. 时序数据库资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'INFLUXDB',
    'InfluxDB',
    'TIME_SERIES_DATABASE',
    'InfluxDB时序数据库，支持用户名密码认证（InfluxDB 1.x）',
    '{
        "url": "http://localhost:8086",
        "user": "admin",
        "password": "",
        "database": "siteweb_v2",
        "connectTimeout": 3600,
        "readTimeout": 3600,
        "writeTimeout": 3600,
        "enableBatching": true,
        "batchSize": 1000,
        "flushInterval": 1000,
        "retryInterval": 5000,
        "maxRetries": 5,
        "enableGzip": false,
        "logLevel": "NONE"
    }',
    'influxdb-config'
);

-- 5. Web服务器资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'AKKA_HTTP_SERVER',
    'Akka HTTP服务器',
    'WEB_SERVER',
    '基于Akka HTTP的高性能HTTP服务器资源',
    '{
        "host": "0.0.0.0",
        "port": 8080,
        "idleTimeout": 60,
        "backlog": 100
    }',
    'akka-http-server-config'
);

INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'NETTY_HTTP_SERVER',
    'Netty HTTP服务器',
    'WEB_SERVER',
    '基于Netty的高性能HTTP服务器资源',
    '{
        "host": "0.0.0.0",
        "port": 8080,
        "idleTimeout": 60,
        "backlog": 100
    }',
    'netty-http-server-config'
);

-- 6. UDP服务器资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'NIO_UDP_SERVER',
    'NIO UDP服务器',
    'UDP_SERVER',
    '基于Java NIO的高性能UDP服务器资源',
    '{
        "bindIp": "0.0.0.0",
        "bindPort": 8888,
        "receiveBufferSize": 65536,
        "sendBufferSize": 65536,
        "reuseAddress": false,
        "broadcast": false
    }',
    'nio-udp-server-config'
);

INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'NETTY_UDP_SERVER',
    'Netty UDP服务器',
    'UDP_SERVER',
    '基于Netty框架的高性能UDP服务器资源',
    '{
        "bindIp": "0.0.0.0",
        "bindPort": 8888,
        "receiveBufferSize": 131072,
        "sendBufferSize": 131072,
        "reuseAddress": true,
        "broadcast": false
    }',
    'netty-udp-server-config'
);



INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component, create_time, update_time) VALUES('LOCAL_FILESYSTEM', '本地文件系统', 'FILE_STORAGE', '本地文件系统存储资源', '{"rootDirectory": "/data/files", "createDirectories": true}', 'local-filesystem-config', '2025-08-07 17:10:33.242', '2025-08-07 17:10:33.242');
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component, create_time, update_time) VALUES('MINIO_FILESYSTEM', 'Minio对象存储', 'FILE_STORAGE', 'Minio分布式对象存储资源', '{"region": "us-east-1", "endpoint": "http://localhost:9000", "useHttps": false, "accessKey": "minioadmin", "secretKey": "ENC(minioadmin)", "bucketName": "default", "readTimeout": 30, "writeTimeout": 30, "connectTimeout": 30}', 'minio-filesystem-config', '2025-08-07 17:10:33.242', '2025-08-07 17:10:33.242');
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component, create_time, update_time) VALUES('ZOOKEEPER_FILESYSTEM', 'Zookeeper分布式存储', 'FILE_STORAGE', 'Zookeeper分布式协调存储资源', '{"rootPath": "/filesystem", "enableAcl": false, "namespace": "", "maxRetries": 3, "aclPassword": "ENC(password)", "aclUsername": "", "maxDataSize": 1048576, "connectString": "localhost:2181", "baseSleepTimeMs": 1000, "sessionTimeoutMs": 60000, "connectionTimeoutMs": 15000}', 'zookeeper-filesystem-config', '2025-08-07 17:10:33.242', '2025-08-07 17:10:33.242');
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component, create_time, update_time) VALUES('CONSUL_FILESYSTEM', 'Consul配置存储', 'FILE_STORAGE', 'Consul服务发现与配置存储资源', '{"host": "localhost", "port": 8500, "token": "ENC(token)", "scheme": "http", "rootPath": "filesystem", "datacenter": "dc1", "maxDataSize": 524288, "readTimeout": 30000, "writeTimeout": 30000, "connectTimeout": 30000}', 'consul-filesystem-config', '2025-08-07 17:10:33.242', '2025-08-07 17:10:33.242');
-- =====================================================
-- 基础数据初始化 - 服务类型
-- =====================================================

-- 1. 数据库服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'DATABASE',
    '数据库服务',
    '提供数据库访问服务',
    '{}',
    'database-service-config',
    'RELATIONAL_DB'
);

-- 2. 键值存储服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'KEY_VALUE_STORE',
    '键值存储服务',
    '提供键值存储服务',
    '{
        "batchThreshold": 100,
        "timeThresholdMs": 3000
    }',
    'key-value-store-service-config',
    'KEY_VALUE_STORE'
);

-- 3. 消息队列服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'MESSAGE_QUEUE',
    '消息队列服务',
    '提供消息队列服务',
    '{
        "defaultTopic": "default-topic",
        "producerConfig": {
            "acks": "all",
            "retries": 3,
            "batchSize": 16384
        },
        "consumerConfig": {
            "autoCommit": true,
            "autoCommitInterval": 5000,
            "maxPollRecords": 500
        }
    }',
    'message-queue-service-config',
    'MESSAGE_QUEUE'
);

-- 4. Siteweb持久化服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'SITEWEB_PERSISTENT',
    'Siteweb持久化服务',
    '用于封装tcs-siteweb模块中的各种service，提供统一的访问接口，支持关系型数据库',
    '{}',
    'siteweb-persistent-service-config',
    'RELATIONAL_DB'
);

-- 5. HTTP服务器服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'HTTP_SERVER',
    'HTTP服务器服务',
    '基于HTTP服务器资源的Web服务，支持路由管理、统计功能等',
    '{
        "maxParsingFailureRecords": 10000,
        "enableParsingFailureRecording": true,
        "parsingFailureRetentionHours": 24
    }',
    'http-server-service-config',
    'WEB_SERVER'
);

-- 6. 文件系统服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'FILESYSTEM',
    '文件系统服务',
    '提供统一的分布式文件管理接口，支持多种存储后端',
    '{
        "threadPoolSize": 10,
        "asyncMode": true
    }',
    'filesystem-service-config',
    'FILE_STORAGE'
);

-- 7. UDP服务器服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'UDP_SERVER',
    'UDP服务器服务',
    '基于UDP服务器资源的通信服务，支持数据包接收、流量监控、异常记录等功能',
    '{
        "maxTrafficStatsRecords": 10000,
        "enableTrafficStatsRecording": true,
        "trafficStatsRetentionHours": 24,
        "enableErrorLogging": true,
        "errorLogRetentionHours": 48
    }',
    'udp-server-service-config',
    'UDP_SERVER'
);
