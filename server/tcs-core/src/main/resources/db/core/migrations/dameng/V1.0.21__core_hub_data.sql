-- Flyway migration script for Hub initial data (H2 compatible)

-- Insert initial data for tcs_plugin_dependencies
-- Note: H2 does not support JSON type directly, dependencies stored as VARCHAR(MAX)
INSERT INTO tcs_plugin_dependencies (applicationName, pluginId, dependencies) VALUES ('default', 'south-ctcc-plugin', '["north-s6-plugin"]');
INSERT INTO tcs_plugin_dependencies (applicationName, pluginId, dependencies) VALUES ('default', 'north-s6-plugin', '[]');

-- Insert initial data for tcs_account
-- Note: H2 uses NUMBER(1) for bit(1), 1/0 for b'1'/b'0', TIMESTAMP for datetime
INSERT INTO tcs_account (UserId, UserName, LoginId, Password, Enable, MaxError, Locked, ValidTime, PasswordValidTime, Description, DepartmentId)
VALUES(-1, '系统管理员', 'admin', 'd82494f05d6917ba02f7aaa29689ccb444bb73f20380876cb05d1f37537b7892', 1, 5, 0, '2050-05-02 23:59:59', '2050-05-02 23:59:59', '系统默认超级管理员', 1);

-- Insert initial data for tcs_role
INSERT INTO tcs_role (RoleId, RoleName, Description, role_code, status, sort_order, create_time, update_time) VALUES
(-1, '系统管理员', '拥有系统所有权限', 'ADMIN', 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert additional sample roles
INSERT INTO tcs_role (RoleName, Description, role_code, status, sort_order, create_time, update_time) VALUES
('部门经理', '部门经理，负责部门内人员和事务管理', 'DEPT_MANAGER', 1, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('普通用户', '普通用户，基础查看权限', 'NORMAL_USER', 1, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('访客用户', '访客用户，受限访问权限', 'GUEST_USER', 0, 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert initial data for tcs_account_role_map
INSERT INTO tcs_account_role_map (UserId,RoleId) VALUES (-1,-1);

-- Insert initial data for tcs_menu_item
-- 根据前端路由结构重新设计菜单项，使用字符串组件路径
INSERT INTO tcs_menu_item (MenuItemId, PluginId, MenuItemName, Name, ParentMenuItemId, Path, Icon, Component, ShowLink, ShowParent, Redirect, Rank, ActivePath, Auths) VALUES

-- 顶级菜单项 (id: 1-99)
(2, 'tcs_hub', '系统管理', 'SystemManagement', 0, '/system-management', 'ep:monitor', NULL, 1, 0, NULL, 10, NULL, NULL),
(3, 'tcs_hub', '流计算', 'StreamComputation', 0, '/stream-computation', 'material-symbols:stream', NULL, 1, 0, NULL, 11, NULL, NULL),

-- 系统管理子菜单 (id: 100-199)
(100, 'tcs_hub', '插件管理', 'PluginManagementPage', 2, '/system-management/plugin-management', 'mingcute:plugin-2-line', 'system-management/plugin-management/index', 1, 1, NULL, 0, NULL, NULL),
(101, 'tcs_hub', '插件详情', 'PluginManagementDetail', 2, '/system-management/plugin-management/detail/:pluginId', NULL, 'system-management/plugin-management/detail/index', 0, 0, NULL, 0, '/system-management/plugin-management', NULL),

(110, 'tcs_hub', '用户管理', 'UserManagement', 2, '/system-management/user-management', 'ep:user', 'system-management/user-management/index', 1, 1, NULL, 0, NULL, '["system:user:list"]'),

(120, 'tcs_hub', '部门管理', 'DepartmentManagement', 2, '/system-management/department-management', 'ep:office-building', 'system-management/department-management/index', 1, 1, NULL, 0, NULL, '["system:dept:list"]'),

(125, 'tcs_hub', '区域管理', 'RegionManagement', 2, '/system-management/region-management', 'ep:map-location', 'system-management/region-management/index', 1, 1, NULL, 0, NULL, '["system:region:view"]'),

(130, 'tcs_hub', '角色管理', 'RoleManagement', 2, '/system-management/role-management', 'ep:user-filled', 'system-management/role-management/index', 1, 1, NULL, 0, NULL, '["system:role:list"]'),

(140, 'tcs_hub', '中间件管理', 'MiddlewareManagement', 2, '/system-management/middleware-management', 'mingcute:package-2-line', 'system-management/middleware-management/index', 1, 1, NULL, 0, NULL, NULL),

(150, 'tcs_hub', 'Pekko运行监控', 'PekkoClusterMonitoring', 2, '/system-management/pekko-cluster-monitoring', 'ep:monitor', 'system-management/pekko-cluster-monitoring/index', 1, 1, NULL, 0, NULL, '["system:cluster:view"]'),

(155, 'tcs_hub', 'Probe网关监控', 'ProbeMonitoring', 2, '/system-management/probe-monitoring', 'ep:data-analysis', 'system-management/probe-monitoring/index', 1, 1, NULL, 0, NULL, '["system:probe:view"]'),

-- 中间件管理子菜单 (id: 200-299)
(200, 'tcs_hub', '资源管理', 'ResourceManagement', 140, '/system-management/middleware-management/resource-management', NULL, 'system-management/middleware-management/resource-management/index', 1, 0, NULL, 0, NULL, NULL),
(201, 'tcs_hub', '新增资源配置', 'ResourceManagementNew', 140, '/system-management/middleware-management/resource-management/new', NULL, 'system-management/middleware-management/resource-management/form', 0, 0, NULL, 0, '/system-management/middleware-management/resource-management', NULL),
(202, 'tcs_hub', '编辑资源配置', 'ResourceManagementEdit', 140, '/system-management/middleware-management/resource-management/:id/edit', NULL, 'system-management/middleware-management/resource-management/form', 0, 0, NULL, 0, '/system-management/middleware-management/resource-management', NULL),
(203, 'tcs_hub', '资源详情', 'ResourceManagementDetail', 140, '/system-management/middleware-management/resource-management/:id/detail', NULL, 'system-management/middleware-management/resource-management/detail', 0, 0, NULL, 0, '/system-management/middleware-management/resource-management', NULL),

(210, 'tcs_hub', '服务管理', 'ServiceManagement', 140, '/system-management/middleware-management/service-management', NULL, 'system-management/middleware-management/service-management/index', 1, 0, NULL, 0, NULL, NULL),
(211, 'tcs_hub', '新增服务配置', 'ServiceManagementNew', 140, '/system-management/middleware-management/service-management/new', NULL, 'system-management/middleware-management/service-management/form', 0, 0, NULL, 0, '/system-management/middleware-management/service-management', NULL),
(212, 'tcs_hub', '编辑服务配置', 'ServiceManagementEdit', 140, '/system-management/middleware-management/service-management/:id/edit', NULL, 'system-management/middleware-management/service-management/form', 0, 0, NULL, 0, '/system-management/middleware-management/service-management', NULL),
(213, 'tcs_hub', '服务详情', 'ServiceManagementDetail', 140, '/system-management/middleware-management/service-management/:id/detail', NULL, 'system-management/middleware-management/service-management/detail', 0, 0, NULL, 0, '/system-management/middleware-management/service-management', NULL),

-- 流计算子菜单 (id: 300-399)
(300, 'tcs_hub', '图元库管理', 'StreamPluginManagement', 3, '/stream-computation/stream-plugin-management', 'mingcute:plugin-2-line', 'stream-computation/stream-plugin-management/index', 1, 0, NULL, 0, NULL, NULL),
(301, 'tcs_hub', '插件详情', 'StreamPluginDetail', 3, '/stream-computation/plugin-detail/:id?', NULL, 'stream-computation/stream-plugin-management/detail', 0, 0, NULL, 0, '/stream-computation/stream-plugin-management', NULL),

(310, 'tcs_hub', '计算图管理', 'ComputationGraphManagement', 3, '/stream-computation/computation-graph', 'material-symbols:account-tree-outline', 'stream-computation/computation-graph/index', 1, 0, NULL, 0, NULL, NULL),
(311, 'tcs_hub', '计算图实例', 'ComputationGraphInstances', 3, '/stream-computation/computation-graph/instances/:graphId', NULL, 'stream-computation/computation-graph/instances', 0, 0, NULL, 0, '/stream-computation/computation-graph', NULL);

-- Insert initial data for tcs_regions
-- Note: H2 does not require LOCK TABLES/UNLOCK TABLES
INSERT INTO tcs_regions (regionId, regionName, parentId, displayIndex, description, resourceStructureId) VALUES 
(1,'南山智园',NULL,NULL,NULL, 0), -- Assuming resourceStructureId is required and defaulting to 0 if not specified in original
(2,'B1栋',1,NULL,NULL, 0),
(3,'B2栋',1,NULL,NULL, 0),
(4,'C1栋',1,NULL,NULL, 0),
(5,'C2栋',1,NULL,NULL, 0),
(6,'B2-1F',3,NULL,NULL, 0),
(7,'B2-2F',3,NULL,NULL, 0),
(8,'B2-3F',3,NULL,NULL, 0),
(9,'B2-4F',3,NULL,NULL, 0),
(10,'B2-5F',3,NULL,NULL, 0),
(11,'B2-6F',3,NULL,NULL, 0),
(12,'B2-7F',3,NULL,NULL, 0),
(13,'501',10,NULL,NULL, 0),
(14,'502',10,NULL,NULL, 0),
(15,'503',10,NULL,NULL, 0);

INSERT INTO "tcs_plugins" ("pluginId","pluginName","version","provider","description","className","buildTime","fileName","enabled","uploadJARDate","updateJARDate","changeDate","operateDate","applicationName") VALUES ('south-cucc-plugin','中国联通南向接入插件','1.2.3','Undefined','','com.siteweb.tcs.south.cucc.SouthCuccPlugin','2025-03-13 03:06:40','tcs-south-cucc-0.0.1-SNAPSHOT.jar',1,'2025-03-13 14:27:02','2025-03-13 14:27:02',NULL,NULL,NULL);
INSERT INTO "tcs_plugins" ("pluginId","pluginName","version","provider","description","className","buildTime","fileName","enabled","uploadJARDate","updateJARDate","changeDate","operateDate","applicationName") VALUES ('south-cmcc-plugin','中国移动南向接入插件','1.2.3','Undefined','','com.siteweb.tcs.south.cmcc.SouthCmccPlugin','2025-03-13 03:06:40','tcs-south-cmcc-0.0.1-SNAPSHOT.jar',1,'2025-03-13 14:27:02','2025-03-13 14:27:02',NULL,NULL,NULL);
INSERT INTO "tcs_plugins" ("pluginId","pluginName","version","provider","description","className","buildTime","fileName","enabled","uploadJARDate","updateJARDate","changeDate","operateDate","applicationName") VALUES ('south-crcc-plugin','中国铁路南向接入插件','1.2.3','Undefined','','com.siteweb.tcs.south.crcc.SouthCrccPlugin','2025-03-13 03:06:40','tcs-south-crcc-0.0.1-SNAPSHOT.jar',1,'2025-03-13 14:27:02','2025-03-13 14:27:02',NULL,NULL,NULL);
INSERT INTO "tcs_plugins" ("pluginId","pluginName","version","provider","description","className","buildTime","fileName","enabled","uploadJARDate","updateJARDate","changeDate","operateDate","applicationName") VALUES ('south-omc-siteweb','siteweb-omc','1.2.3','Undefined','','com.siteweb.tcs.south.omc.OmcSitewebPlugin','2025-03-13 03:06:40','tcs-south-omc-siteweb-0.0.1-SNAPSHOT.jar',1,'2025-03-13 14:27:02','2025-03-13 14:27:02',NULL,NULL,NULL);

-- Insert initial role permission mappings for demonstration
-- 动态分配系统管理员角色(-1)所有菜单权限
-- 通过查询 tcs_menu_item 表为系统管理员分配所有菜单权限
INSERT INTO tcs_role_permission_map (PluginId, RoleId, PermissionId, permissionType) 
SELECT PluginId, -1, MenuItemId, 1 FROM tcs_menu_item WHERE PluginId = 'tcs_hub';
-- todo xsx 测试数据后面删除
INSERT INTO tcs_foreign_gateway (
  ForeignGatewayId,
  PluginId,
  MonitorUnitId,
  StationId
) VALUES (
  'FSU123456',
  'tcs-south-cmcc',
  201010002,
  -201
);

-- Insert initial data for tcs_auth_code
INSERT INTO tcs_auth_code (auth_code, auth_name, menu_item_id, plugin_id) VALUES
-- 用户管理权限
('system:user:list', '查看用户', 110, 'tcs_hub'),
('system:user:add', '新增用户', 110, 'tcs_hub'),
('system:user:edit', '编辑用户', 110, 'tcs_hub'),
('system:user:delete', '删除用户', 110, 'tcs_hub'),
('system:user:batch-delete', '批量删除用户', 110, 'tcs_hub'),
('system:user:reset-pwd', '重置密码', 110, 'tcs_hub'),
('system:user:assign-role', '分配角色', 110, 'tcs_hub'),
('system:user:toggle-enable', '启用停用用户', 110, 'tcs_hub'),

-- 角色管理权限
('system:role:list', '查看角色', 130, 'tcs_hub'),
('system:role:add', '新增角色', 130, 'tcs_hub'),
('system:role:edit', '编辑角色', 130, 'tcs_hub'),
('system:role:delete', '删除角色', 130, 'tcs_hub'),
('system:role:batch-delete', '批量删除角色', 130, 'tcs_hub'),
('system:role:permission', '配置权限', 130, 'tcs_hub'),
('system:role:toggle-status', '启用停用角色', 130, 'tcs_hub'),

-- 部门管理权限
('system:dept:list', '查看部门', 120, 'tcs_hub'),
('system:dept:add', '新增部门', 120, 'tcs_hub'),
('system:dept:edit', '编辑部门', 120, 'tcs_hub'),
('system:dept:delete', '删除部门', 120, 'tcs_hub'),
('system:dept:toggle-status', '启用停用部门', 120, 'tcs_hub'),

-- 区域管理权限
('system:region:view', '查看区域', 125, 'tcs_hub'),
('system:region:add', '新增区域', 125, 'tcs_hub'),
('system:region:edit', '编辑区域', 125, 'tcs_hub'),
('system:region:delete', '删除区域', 125, 'tcs_hub'),
('system:region:sync', '同步区域', 125, 'tcs_hub'),
('system:region:item', '管理区域关联', 125, 'tcs_hub'),

-- 插件管理权限
('system:plugin:list', '查看插件', 100, 'tcs_hub'),
('system:plugin:upload', '上传插件', 100, 'tcs_hub'),
('system:plugin:enable', '启用插件', 100, 'tcs_hub'),
('system:plugin:disable', '停用插件', 100, 'tcs_hub'),
('system:plugin:delete', '删除插件', 100, 'tcs_hub'),
('system:plugin:detail', '插件详情', 100, 'tcs_hub'),

-- 中间件管理权限
('system:middleware:resource:list', '查看资源配置', 200, 'tcs_hub'),
('system:middleware:resource:add', '新增资源配置', 200, 'tcs_hub'),
('system:middleware:resource:edit', '编辑资源配置', 200, 'tcs_hub'),
('system:middleware:resource:delete', '删除资源配置', 200, 'tcs_hub'),
('system:middleware:resource:detail', '资源配置详情', 200, 'tcs_hub'),

('system:middleware:service:list', '查看服务配置', 210, 'tcs_hub'),
('system:middleware:service:add', '新增服务配置', 210, 'tcs_hub'),
('system:middleware:service:edit', '编辑服务配置', 210, 'tcs_hub'),
('system:middleware:service:delete', '删除服务配置', 210, 'tcs_hub'),
('system:middleware:service:detail', '服务配置详情', 210, 'tcs_hub'),

-- 流计算管理权限
('system:stream:plugin:list', '查看图元库', 300, 'tcs_hub'),
('system:stream:plugin:upload', '上传图元', 300, 'tcs_hub'),
('system:stream:plugin:delete', '删除图元', 300, 'tcs_hub'),
('system:stream:plugin:detail', '图元详情', 300, 'tcs_hub'),

('system:stream:graph:list', '查看计算图', 310, 'tcs_hub'),
('system:stream:graph:add', '新增计算图', 310, 'tcs_hub'),
('system:stream:graph:edit', '编辑计算图', 310, 'tcs_hub'),
('system:stream:graph:delete', '删除计算图', 310, 'tcs_hub'),
('system:stream:graph:instances', '计算图实例', 310, 'tcs_hub'),

-- Pekko运行监控权限
('system:cluster:view', '查看运行监控', 150, 'tcs_hub');

-- 角色权限代码映射
-- 为系统管理员分配所有权限
INSERT INTO tcs_role_permission_map (PluginId, RoleId, PermissionId, permissionType) 
SELECT 'tcs_hub', -1, auth_id, 3 FROM tcs_auth_code WHERE plugin_id = 'tcs_hub';

