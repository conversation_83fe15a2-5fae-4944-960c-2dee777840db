package com.siteweb.tcs;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.common.config.UidChangeEpochStrInitializer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.awt.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

@SpringBootApplication
@MapperScan(basePackages = {
        "com.siteweb.tcs.hub.dal.mapper",
        "com.siteweb.tcs.backend.mapper",
        "com.siteweb.tcs.middleware.mapper",
        "com.siteweb.stream.core.mapper"
})
@ComponentScan(basePackages ={
        "com.siteweb.tcs.common",
        "com.siteweb.tcs.hub",
//        "org.siteweb.config.client",
        "com.siteweb.tcs.backend",
        "com.siteweb.tcs.middleware",
        "com.github.wujun234",
        "com.siteweb.tcs.middleware.factory",
        "com.siteweb.tcs.middleware.common",
        "com.siteweb.stream.core",
        "com.siteweb.stream.service",
        "com.siteweb.stream.plugin.defaults",
//        "com.vertiv.stream",
})
@EnableScheduling
@EnableTransactionManagement
public class SiteWebThingConnectServerApplication  {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(SiteWebThingConnectServerApplication.class);
        BufferingApplicationStartup startup = new BufferingApplicationStartup(2048);
//        startup.addFilter(startupStep -> startupStep.getName().matches("spring.beans.instantiate"));
        app.setApplicationStartup(startup);
        app.addInitializers(new UidChangeEpochStrInitializer());
        app.run(args);
//        openBrowser("http://localhost:8080");
    }

    private static void openBrowser(String url) {
        try {
            // Check if Desktop is supported
            if (Desktop.isDesktopSupported()) {
                Desktop desktop = Desktop.getDesktop();
                desktop.browse(new URI(url));
            } else {
                // Fallback for systems that do not support Desktop
                Runtime runtime = Runtime.getRuntime();
                runtime.exec("rundll32 url.dll,FileProtocolHandler " + url);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
