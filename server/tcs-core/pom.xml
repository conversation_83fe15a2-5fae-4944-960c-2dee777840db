<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>thing-connect-server</artifactId>
        <groupId>com.siteweb</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>tcs-core</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>tcs-core</name>
    <properties>
        <spring.plugins.runtime-mode>development</spring.plugins.runtime-mode>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <docker.image.prefix>vertiv</docker.image.prefix>
    </properties>
    <profiles>

        <profile>
            <id>Debug-Plugin(CUCC)</id>
            <properties>
                <spring.plugins.runtime-mode>development</spring.plugins.runtime-mode>
            </properties>
            <dependencies>
                <!-- 调试模式下的插件依赖 -->
                <dependency>
                    <groupId>com.siteweb</groupId>
                    <artifactId>tcs-south-cucc</artifactId>
                    <version>0.0.1-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>Debug-North-Plugin(CMCC)</id>
            <properties>
                <spring.plugins.runtime-mode>development</spring.plugins.runtime-mode>
            </properties>
            <dependencies>
                <!-- 调试模式下的插件依赖 -->
                <dependency>
                    <groupId>com.siteweb</groupId>
                    <artifactId>tcs-north-cmcc</artifactId>
                    <version>0.0.1-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>Build-Web</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>1.15.0</version>
                        <configuration>
                            <!-- 指定构建目录 -->
                            <workingDirectory>../../web</workingDirectory>
                        </configuration>
                        <executions>
                            <execution>
                                <id>install-node-and-npm</id>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                                <configuration>
                                    <nodeVersion>${node.version}</nodeVersion>
                                    <npmVersion>${npm.version}</npmVersion>
                                </configuration>
                            </execution>
                            <execution>
                                <id>npm-install-pnpm</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>install -g pnpm --registry=https://registry.npmmirror.com</arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>pnpm-install</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>exec pnpm install --registry=https://registry.npmmirror.com</arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>pnpm-build</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <configuration>
                                    <arguments>exec pnpm run build</arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Debug-Plugin(CMCC)</id>
            <properties>
                <spring.plugins.runtime-mode>development</spring.plugins.runtime-mode>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.siteweb</groupId>
                    <artifactId>tcs-south-cmcc</artifactId>
                    <version>0.0.1-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>Debug-Plugin(OMC-SITEWEB)</id>
            <properties>
                <spring.plugins.runtime-mode>development</spring.plugins.runtime-mode>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.siteweb</groupId>
                    <artifactId>tcs-south-omc-siteweb</artifactId>
                    <version>0.0.1-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>Debug-Plugin(CTCC)</id>
            <properties>
                <spring.plugins.runtime-mode>development</spring.plugins.runtime-mode>
            </properties>
            <dependencies>
                <!-- CTCC插件调试模式依赖 -->
                <dependency>
                    <groupId>com.siteweb</groupId>
                    <artifactId>tcs-south-ctcc</artifactId>
                    <version>0.0.1-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>Debug-Plugin(SWAP)</id>
            <properties>
                <spring.plugins.runtime-mode>development</spring.plugins.runtime-mode>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.siteweb</groupId>
                    <artifactId>tcs-south-swap</artifactId>
                    <version>0.0.1-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
    <dependencies>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-backend-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-middleware</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-middleware-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>stream-plugin-defaults</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-plugin-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
            <version>1.2.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.github.loki4j</groupId>
            <artifactId>loki-logback-appender</artifactId>
            <version>2.0.0</version> <!-- 检查 GitHub 上的最新版 -->
        </dependency>
        <dependency>
            <groupId>com.github.loki4j</groupId>
            <artifactId>loki-protobuf</artifactId>
            <version>0.0.2_pb4.31.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version> <!-- 可根据需要选择最新兼容版本 -->
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.16</version>
        </dependency>


        <!--        <dependency>-->
        <!--            <groupId>com.siteweb</groupId>-->
        <!--            <artifactId>tcs-south-cmcc</artifactId>-->
        <!--            <version>0.1.0</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.2.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.12.1</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.2.2</version>
                <configuration>
                    <!--<forceTags>false</forceTags>
                    <imageTags>
                        <imageTag>6.2-${timestamp}</imageTag>
                    </imageTags>-->
                    <imageName>${docker.image.prefix}/siteweb_video_server</imageName>
                    <dockerDirectory>${project.basedir}/docker</dockerDirectory>
                    <skipDockerBuild>false</skipDockerBuild>
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                    </resources>
                    <serverId>docker-hub</serverId>
                    <registryUrl>https://index.docker.io/v1/</registryUrl>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>