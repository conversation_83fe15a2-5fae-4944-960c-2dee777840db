package com.siteweb.tcs.south.omc.config;

import com.siteweb.tcs.common.util.PluginResourceHelper;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

@Slf4j
//@Configuration
public class FlywayConfig {

    @Value("${plugin.database.fromhop:false}")
    private Boolean fromHop;
    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;
    @Value("${plugin.id}")
    private String pluginId;
    @Value("${spring.plugins.runtime-mode:production}")
    private String runtimeMode;
    @Autowired
    private ResourceRegistry resourceRegistry;
    @Autowired
    private PluginResourceHelper pluginResourceHelper;

    @Bean(name = "omcFlyway")
    @DependsOn("omcDataSource")
    public Flyway flyway() throws SQLException {
        Resource resource = resourceRegistry.get(dbResourceId, pluginId);

        String databaseProductName = switch (ResourceType.fromCode(resource.getType())){
            case POSTGRESQL -> "postgresql";
            case MYSQL -> "h2";
            case H2 -> "h2";
            case OPENGAUSS -> "opengauss";
            default -> "h2";
        };
        String flywaylocation = pluginResourceHelper.getFlywayLocationByPluginPath(pluginId, databaseProductName) + (fromHop ? "_hop" : "");

        Flyway flyway = Flyway.configure()
            .dataSource(resource.getNativeResource())
            .locations(flywaylocation)
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .table("omc_schema_history")
            .load();
        
        try {
            var result = flyway.migrate();
            log.info("插件 {} Flyway迁移完成，执行了 {} 个脚本", pluginId, result.migrationsExecuted);
        } catch (Exception e) {
            log.error("插件 {} Flyway迁移失败: {}", pluginId, e.getMessage(), e);
            throw e;
        }
        
        return flyway;
    }
} 