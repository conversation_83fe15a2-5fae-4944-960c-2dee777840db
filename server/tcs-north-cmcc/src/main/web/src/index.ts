// 引入重置样式
import "./style/reset.scss";
// 导入公共样式
import "./style/index.scss";
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./style/tailwind.css";
import "element-plus/dist/index.css";

console.warn("[CTCC插件] 开始加载插件...", new Date().toISOString());
console.warn("[CTCC插件] 全局对象检查:", {
  Vue全局对象: window.Vue === undefined ? "不存在" : "存在",
  VueRouter全局对象: window.VueRouter === undefined ? "不存在" : "存在",
  microFrontendRegistry:
    window.microFrontendRegistry === undefined ? "不存在" : "存在"
});

// 声明全局微前端注册对象类型
declare global {
  interface Window {
    microFrontendRegistry?: {
      registerRoutes: (routes: any[]) => void;
    };
    Vue?: any;
    VueRouter?: any;
  }
}

// 提供注册方法
function registerPluginRoutes() {
  if (window.microFrontendRegistry) {
    // 注册所有路由模块
    const modules: Record<string, any> = import.meta.glob(
      ["./router/modules/**/*.ts", "!./router/modules/**/remaining.ts"],
      {
        eager: true
      }
    );

    /** 原始静态路由（未做任何处理） */
    const routes = [];

    Object.keys(modules).forEach(key => {
      routes.push(modules[key].default);
    });

    // 删除一级路由的component属性
    const processedRoutes = routes.map(route => {
      // 创建一个路由的副本，避免修改原始对象
      const routeCopy = { ...route };
      // 删除一级路由的component属性
      delete routeCopy.component;
      return routeCopy;
    });

    window.microFrontendRegistry.registerRoutes(processedRoutes);
    console.log("[Plugin] 已注册路由模块:", processedRoutes);
  } else {
    console.warn("[Plugin] 主应用未初始化插件注册表");
  }
}

// 注册所有插件
function registerPlugins() {
  console.warn("[CTCC插件] 开始注册插件组件...");
  console.warn("[CTCC插件] 所有插件注册完成");
  registerPluginRoutes();
}

// 启动注册流程
console.warn("[CTCC插件] 初始化完成，开始注册流程");
registerPlugins();
