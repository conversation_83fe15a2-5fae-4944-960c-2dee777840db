-- 移动设备种类表
CREATE TABLE cmcc_device_type (
  ID INT NOT NULL AUTO_INCREMENT,
  DeviceTypeID VARCHAR(45) DEFAULT NULL,
  DeviceTypeName VARCHAR(128) DEFAULT NULL,
  DeviceSubTypeID VARCHAR(45) DEFAULT NULL,
  DeviceSubTypeName VARCHAR(128) DEFAULT NULL,
  PRIMARY KEY (ID)
);
COMMENT ON TABLE cmcc_device_type IS '移动设备种类表';

-- 移动设备类型映射表
CREATE TABLE cmcc_device_type_map (
  ID INT NOT NULL AUTO_INCREMENT,
  DeviceTypeID VARCHAR(45) DEFAULT NULL,
  DeviceSubTypeID VARCHAR(45) DEFAULT NULL,
  EquipmentCategoryID VARCHAR(45) DEFAULT NULL,
  PRIMARY KEY (ID)
);
COMMENT ON TABLE cmcc_device_type_map IS '移动设备类型映射表';
COMMENT ON COLUMN cmcc_device_type_map.DeviceTypeID IS '设备大类id';
COMMENT ON COLUMN cmcc_device_type_map.DeviceSubTypeID IS '设备子类ID';
COMMENT ON COLUMN cmcc_device_type_map.EquipmentCategoryID IS 'SiteWeb设备种类id';

-- 移动标准化局站类型表
CREATE TABLE cmcc_station_type (
  ID INT NOT NULL AUTO_INCREMENT,
  StationTypeID INT DEFAULT NULL,
  StationTypeName VARCHAR(128) DEFAULT NULL,
  PRIMARY KEY (ID)
);
COMMENT ON TABLE cmcc_station_type IS '移动标准化局站类型';
COMMENT ON COLUMN cmcc_station_type.StationTypeID IS '局站类型';

-- 移动标准化局站类型与Siteweb局站类型映射表
CREATE TABLE cmcc_station_type_map (
  ID INT NOT NULL AUTO_INCREMENT,
  StationTypeID INT DEFAULT NULL,
  StationCategoryID INT DEFAULT NULL,
  PRIMARY KEY (ID)
);
COMMENT ON TABLE cmcc_station_type_map IS '移动标准化局站类型与Siteweb局站类型映射';

-- cmcc设备拓展表
CREATE TABLE cmcc_device_ext (
  DeviceGuid BIGINT NOT NULL,
  DeviceTypeID VARCHAR(45) DEFAULT NULL,
  DeviceSubTypeID VARCHAR(45) DEFAULT NULL,
  EquipmentCategoryID INT DEFAULT NULL,
  PRIMARY KEY (DeviceGuid)
);
COMMENT ON TABLE cmcc_device_ext IS 'cmcc设备拓展表';
COMMENT ON COLUMN cmcc_device_ext.DeviceGuid IS 'hub设备id';
COMMENT ON COLUMN cmcc_device_ext.DeviceTypeID IS 'CMCC设备大类id';
COMMENT ON COLUMN cmcc_device_ext.DeviceSubTypeID IS 'CMCC设备子类id';
COMMENT ON COLUMN cmcc_device_ext.EquipmentCategoryID IS 'siteweb设备种类id';

-- cmcc机房映射表
CREATE TABLE cmcc_room_map (
  CmccSiteID VARCHAR(128) DEFAULT NULL,
  CmccRoomID VARCHAR(128) NOT NULL,
  CmccRoomName VARCHAR(256) DEFAULT NULL,
  CmccRoomType INT DEFAULT NULL,
  StationID INT DEFAULT NULL,
  HouseID INT DEFAULT NULL,
  PRIMARY KEY (CmccRoomID)
);
COMMENT ON TABLE cmcc_room_map IS 'cmcc机房映射表';
COMMENT ON COLUMN cmcc_room_map.CmccSiteID IS 'CMCC站点id';
COMMENT ON COLUMN cmcc_room_map.CmccRoomID IS 'CMCC机房id';
COMMENT ON COLUMN cmcc_room_map.CmccRoomName IS 'CMCC机房名称';
COMMENT ON COLUMN cmcc_room_map.CmccRoomType IS 'CMCC机房类型';
COMMENT ON COLUMN cmcc_room_map.StationID IS 'SiteWeb局站id';
COMMENT ON COLUMN cmcc_room_map.HouseID IS 'SiteWeb机房id';

-- cmcc站点映射表
CREATE TABLE cmcc_site_map (
  CmccSiteID VARCHAR(128) NOT NULL,
  CmccSiteName VARCHAR(256) DEFAULT NULL,
  CmccSiteType INT DEFAULT NULL,
  StationID INT DEFAULT NULL,
  StationCategoryID INT DEFAULT NULL,
  PRIMARY KEY (CmccSiteID)
);
COMMENT ON TABLE cmcc_site_map IS 'cmcc站点映射表';
COMMENT ON COLUMN cmcc_site_map.CmccSiteID IS 'cmcc站点id';
COMMENT ON COLUMN cmcc_site_map.CmccSiteName IS 'cmcc站点名称';
COMMENT ON COLUMN cmcc_site_map.CmccSiteType IS 'cmcc站点类型（对应局站类型）';
COMMENT ON COLUMN cmcc_site_map.StationID IS 'siteweb局站id';
COMMENT ON COLUMN cmcc_site_map.StationCategoryID IS 'siteweb局站类型';
