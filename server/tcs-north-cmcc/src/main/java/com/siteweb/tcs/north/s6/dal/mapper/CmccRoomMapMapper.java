package com.siteweb.tcs.north.s6.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.CmccRoomMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * CMCC机房映射表 Mapper 接口
 */
@Mapper
public interface CmccRoomMapMapper extends BaseMapper<CmccRoomMap> {

    /**
     * 根据CMCC机房ID查询机房映射信息
     * @param cmccRoomId CMCC机房ID
     * @return 机房映射信息
     */
    CmccRoomMap selectByCmccRoomId(@Param("cmccRoomId") String cmccRoomId);

    /**
     * 根据CMCC站点ID查询机房映射信息列表
     * @param cmccSiteId CMCC站点ID
     * @return 机房映射信息列表
     */
    java.util.List<CmccRoomMap> selectByCmccSiteId(@Param("cmccSiteId") String cmccSiteId);

    /**
     * 根据SiteWeb局站ID查询机房映射信息列表
     * @param stationId SiteWeb局站ID
     * @return 机房映射信息列表
     */
    java.util.List<CmccRoomMap> selectByStationId(@Param("stationId") Integer stationId);
}
