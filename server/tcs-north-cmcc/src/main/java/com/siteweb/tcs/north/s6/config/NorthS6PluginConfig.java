package com.siteweb.tcs.north.s6.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * NorthS6插件配置类
 * <p>
 * 用于配置插件需要扫描的包路径，包括依赖的tcs-s6-access模块
 * </p>
 */
@Configuration
@ComponentScan(basePackages = {
        "com.siteweb.tcs.s6.access"
})
public class NorthS6PluginConfig {
}
