package com.siteweb.tcs.north.s6.web.lcm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.dal.dto.AlarmConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;
import com.siteweb.tcs.s6.access.dal.entity.AlarmMap;
import com.siteweb.tcs.s6.access.dal.entity.DeviceMap;
import com.siteweb.tcs.s6.access.web.sevice.IAlarmMapService;
import com.siteweb.tcs.siteweb.dto.EventConditionDTO;
import com.siteweb.tcs.siteweb.dto.EventConfigItem;
import com.siteweb.tcs.siteweb.entity.Event;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * CMCC告警处理器
 * 负责处理告警相关的创建、更新、删除逻辑
 */
@Component
public class CmccAlarmHandler extends CmccBaseHandler {

    @Autowired
    private IAlarmMapService alarmMapService;

    /**
     * 处理告警信息
     */
    public void handleAlarm(DeviceConfigChangeDto deviceConfigChangeDto, DeviceMap deviceMap) {
        List<AlarmConfigChangeDto> alarms = deviceConfigChangeDto.getAlarms();
        if(CollectionUtil.isEmpty(alarms)) return;
        for (AlarmConfigChangeDto alarm : alarms) {
            switch (alarm.getLifeCycleEvent()) {
                case CREATE:
                    createAlarm(alarm, deviceMap);
                    break;
                case DELETE:
                    deleteAlarm(alarm, deviceMap);
                    break;
                case UPDATE:
                    updateAlarm(alarm, deviceMap);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 创建告警
     */
    private void createAlarm(AlarmConfigChangeDto alarm, DeviceMap deviceMap) {
        EventConfigItem eventConfigItem = cmccConfigParser.parseAlarmConfigItem(alarm, deviceMap);
        Event event = sitewebPersistentService.getConfigAPI().createForEvent(eventConfigItem);
        AlarmMap alarmMap = new AlarmMap();
        alarmMap.setAlarmId(alarm.getId());
        alarmMap.setDeviceId(alarm.getDeviceId());
        alarmMap.setNorthEquipmentId(deviceMap.getNorthEquipmentId());
        alarmMap.setNorthEventId(event.getEventId());
        List<EventConditionDTO> eventConditionList = eventConfigItem.getEventConditionList();
        if(CollectionUtil.isNotEmpty(eventConditionList)){
            alarmMap.setNorthEventConditionId(eventConditionList.get(0).getEventConditionId());
        }
        alarmMapService.save(alarmMap);
    }

    /**
     * 删除告警
     */
    private void deleteAlarm(AlarmConfigChangeDto alarm, DeviceMap deviceMap) {
        AlarmMap alarmMap = alarmMapService.getByAlarmId(alarm.getId());
        if(ObjectUtil.isEmpty(alarmMap)) return;
        sitewebPersistentService.getConfigAPI().deleteForEvent(deviceMap.getNorthEquipmentTemplateId(), alarmMap.getNorthEventId());
        boolean flag = alarmMapService.deleteByAlarmId(alarm.getId());
    }

    /**
     * 更新告警
     */
    private void updateAlarm(AlarmConfigChangeDto alarm, DeviceMap deviceMap) {
        AlarmMap alarmMap = alarmMapService.getByAlarmId(alarm.getId());
        if(ObjectUtil.isEmpty(alarmMap)) return;
        EventConfigItem eventConfigItem = cmccConfigParser.parseAlarmConfigItem(alarm, deviceMap);
        eventConfigItem.setEventId(alarmMap.getNorthEventId());
        Event event = sitewebPersistentService.getConfigAPI().updateForEvent(eventConfigItem);
    }

    public boolean handleDeleteByDeviceId(Long deviceId) {
        boolean flag = alarmMapService.deleteByDeviceId(deviceId);
        return flag;
    }
}
