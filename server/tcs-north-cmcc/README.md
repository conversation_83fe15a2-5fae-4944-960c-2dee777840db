# siteweb6北向接口插件

## 插件简介

siteweb6北向接口插件是一个用于与第三方系统集成的北向接口插件，提供标准的RESTful API接口，支持数据同步、设备管理等功能。

## 插件信息

- **插件ID**: `north-s6`
- **插件名称**: siteweb6北向接口
- **版本**: 1.0.0
- **提供商**: Siteweb

## 功能特性

- RESTful API接口
- API调用日志记录
- 数据同步与转换
- 监控与统计
- 前端管理界面

## 项目结构

```
tcs-north-s6/
├── pom.xml                                 # Maven配置
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/siteweb/tcs/north/s6/   # Java源码
│   │   │       ├── NorthS6Plugin.java     # 插件主类
│   │   │       ├── config/                # 配置类
│   │   │       ├── connector/             # 连接器相关
│   │   │       ├── dal/                   # 数据访问层
│   │   │       └── web/                   # Web层
│   │   ├── resources/                     # 资源文件
│   │   │   ├── mapper/north-s6/           # MyBatis映射文件
│   │   │   ├── db/north-s6/migration/     # 数据库迁移脚本
│   │   │   ├── plugin.yml                # 插件配置
│   │   │   └── application.yml           # 应用配置
│   │   └── web/                          # 前端代码
│   │       ├── src/                      # 前端源码
│   │       ├── package.json              # 前端依赖
│   │       └── vite.config.ts           # 构建配置
└── README.md                             # 说明文档
```

## 快速开始

### 环境要求

- JDK 17+
- Maven 3.8+
- Node.js 18+
- MySQL 8.0+

### 构建插件

```bash
# 构建后端
mvn clean package

# 构建前端
cd src/main/web
pnpm install
pnpm run build
```

### 部署插件

1. 将构建好的插件JAR文件放到`plugins`目录
2. 重启TCS服务
3. 检查插件是否成功加载

### 开发调试

1. 在`tcs-core`项目中激活插件profile：
   ```bash
   mvn spring-boot:run -P"Debug-Plugin(S6)"
   ```

2. 访问测试接口验证插件加载：
   ```
   http://localhost:8080/api/north/s6/test/helloworld
   ```

## API接口

### 测试接口

- **GET** `/api/north/s6/test/helloworld` - 测试连接
- **GET** `/api/north/s6/test/info` - 获取插件信息

### 数据接口

- **GET** `/api/north/s6/stats` - 获取统计信息
- **GET** `/api/north/s6/logs` - 获取API日志

## 数据库配置

插件使用独立的数据库配置，需要在`application.yml`中配置：

```yaml
plugin:
  datasource:
    north-s6:
      url: ****************************************
      username: your_username
      password: your_password
      driver-class-name: com.mysql.cj.jdbc.Driver
```

## 前端界面

插件提供了完整的前端管理界面，包括：

- 首页：插件介绍和快速操作
- 仪表盘：统计信息和服务状态
- API日志：接口调用记录和详情

访问地址：`http://localhost:8080/plugins/north-s6/`

## 配置说明

### 插件配置 (plugin.yml)

```yaml
plugin:
  id: tcs-tcs-north-s6
  name: siteweb6北向接口
  version: 1.0.0
  provider: Siteweb
  class: com.siteweb.tcs.north.s6.NorthS6Plugin
```

### 应用配置 (application.yml)

```yaml
# 数据源配置
plugin:
  datasource:
    north-s6:
      url: ****************************************
      username: root
      password: root

# 北向接口配置
north:
  s6:
    api:
      base-url: /api/north/s6
      timeout: 30000
    enable-logging: true
```

## 开发指南

### 添加新的API接口

1. 在`web/controller`包下创建Controller类
2. 使用`@RestController`和`@RequestMapping`注解
3. 所有Mapping注解必须包含`value`属性

### 数据库操作

1. 在`dal/entity`包下创建实体类
2. 在`dal/mapper`包下创建Mapper接口
3. 在`resources/mapper/north-s6/`下创建对应的XML文件

### 前端开发

1. 在`src/main/web/src/views/`下创建Vue组件
2. 在`src/router/index.ts`中添加路由配置
3. 遵循Element Plus组件库规范

## 注意事项

1. 插件ID必须唯一，不能与其他插件冲突
2. 数据库表名建议使用`tcs_north_s6_`前缀
3. API路径建议使用`/api/north/s6/`前缀
4. 前端资源会打包到`META-INF/com-siteweb-webroot/plugins/north-s6/`

## 常见问题

### Q: 插件无法加载？
A: 检查插件是否已添加到tcs-core的pom.xml和application.yml中，确保profile已激活。

### Q: 前端页面无法访问？
A: 确保前端代码已正确构建，检查vite.config.ts中的输出路径配置。

### Q: 数据库连接失败？
A: 检查application.yml中的数据源配置，确保数据库已创建且权限正确。

## 许可证

Copyright © 2024 Siteweb. All rights reserved. 