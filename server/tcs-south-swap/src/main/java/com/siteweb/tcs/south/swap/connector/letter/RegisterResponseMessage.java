package com.siteweb.tcs.south.swap.connector.letter;

import com.scaleout.client.util.BitConverter;
import com.siteweb.tcs.south.swap.dal.entity.HostInfo;
import com.siteweb.tcs.south.swap.enums.EncryptType;
import com.siteweb.tcs.south.swap.enums.MessageType;
import com.siteweb.tcs.south.swap.enums.PipelineType;
import com.siteweb.tcs.south.swap.enums.ProtocolType;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.Map;

/**
 * SWAP注册响应消息
 * <p>
 * 简化版本，类似MobileBResponseMessage
 * 主要包含响应内容（字节数组）
 * </p>
 */
@Getter
@Setter
public class RegisterResponseMessage extends SitewebBResponseMessage {
    /**
     * 注册结果编码长度
     */
    public static final int RESULT_CODE_LEN = 1;

    /**
     * 注册结果
     */
    private int resultCode;
    /**
     * 管道列表
     */
    private Map<PipelineType, String> pipeLinePairs;
    /**
     * 构造函数
     */
    public RegisterResponseMessage() {
        super(MessageType.REGISTER_RESPONSE);
    }

    /**
     * 构造函数（包含原始请求数据包）
     * @param originalRequestPacket 原始请求数据包
     */
    public RegisterResponseMessage(byte[] originalRequestPacket) {
        super(MessageType.REGISTER_RESPONSE, originalRequestPacket);
    }

    @Override
    public SiteWebPacket getResponsePacket(SiteWebPacket siteWebPacket) {
        SiteWebPacket siteWebPacket1 = new SiteWebPacket();
        siteWebPacket1.setMessageType(MessageType.REGISTER_RESPONSE);
        siteWebPacket1.setDestinationHostId(siteWebPacket.getSourceHostId());
        siteWebPacket1.setSourceHostId(0);
        siteWebPacket1.setRequireAcknowledge(true);
        siteWebPacket1.setPipelineType(PipelineType.DIAGNOSTICS);
        siteWebPacket1.setProtocolType(ProtocolType.SWCP);
        siteWebPacket1.setEncryptType(EncryptType.NON_ENCRYPT);
        siteWebPacket1.setSequenceNumber(siteWebPacket.getSequenceNumber());
        return siteWebPacket1;
    }

    /**
     * 实现getResponseInfo方法
     * 生成注册响应的信息体
     */
    @Override
    public byte[] getResponseInfo() {
        byte[] data = new byte[RESULT_CODE_LEN + HostInfo.LENGTH_LEN];

        int index = 0;
        // 结果代码
        data[index] = (byte) this.getResultCode();
        index += RESULT_CODE_LEN;
        System.arraycopy(BitConverter.getBytes((short) this.getPipeLinePairs().size()), 0, data, index, HostInfo.LENGTH_LEN);
        // 服务地址集合
        index += HostInfo.LENGTH_LEN;
        int len = 0;
        for (Map.Entry<PipelineType, String> entry : this.getPipeLinePairs().entrySet()) {
            String url = entry.getValue();
            len = url.length();
            data = Arrays.copyOf(data, index + 2 + len);
            // 消息管道
            data[index] = (byte) entry.getKey().value();
            index += 1;
            // 服务地址
            data[index] = (byte) len;
            index += 1;
            System.arraycopy(BitConverter.getBytes(url), 0, data, index, len);
            index += len;
        }
        return data;
    }

    /**
     * 转换为字节数组
     * 保持StreamMessage体系的一致性，但实际调用getResponseInfo()
     */
    @Override
    public byte[] toByteArray() {
        return getResponseInfo();
    }

    /**
     * 创建注册成功响应
     * @return 注册成功响应
     */
    public static RegisterResponseMessage createSuccessResponse() {
        return new RegisterResponseMessage();
    }

    /**
     * 创建注册失败响应
     * @return 注册失败响应
     */
    public static RegisterResponseMessage createFailureResponse() {
        return new RegisterResponseMessage();
    }

}
