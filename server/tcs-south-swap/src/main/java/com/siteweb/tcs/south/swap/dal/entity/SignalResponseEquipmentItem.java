package com.siteweb.tcs.south.swap.dal.entity;

import com.siteweb.tcs.south.swap.enums.MonitorValueType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 信号响应设备项
 * 
 * <AUTHOR> zhou
 * @description SignalResponseEquipmentItem
 * @createTime 2025-01-21 15:30:00
 */
@Data
@NoArgsConstructor
public class SignalResponseEquipmentItem {

    /**
     * 设备ID
     */
    private int equipmentId;

    /**
     * 信号响应项列表
     */
    private List<SignalResponseItem> signalResponseItems;

    // 删除重复的构造函数，使用@NoArgsConstructor注解提供的默认构造函数
    {
        // 实例初始化块
        this.signalResponseItems = new ArrayList<>();
    }

    /**
     * 解析信号项
     * @param data 字节数组
     * @param index 起始索引
     * @param signalCount 信号数量
     * @return 解析后的索引位置
     */
    public int parseSignalItems(byte[] data, int index, int signalCount) {
        List<SignalResponseItem> items = new ArrayList<>();
        
        for (int i = 0; i < signalCount; i++) {
            if (index + 16 > data.length) { // 最小信号项长度检查
                break;
            }
            
            SignalResponseItem item = new SignalResponseItem();
            
            // 数据项Id (4字节)
            item.setDataId(((data[index] & 0xFF)) |
                          ((data[index + 1] & 0xFF) << 8) |
                          ((data[index + 2] & 0xFF) << 16) |
                          ((data[index + 3] & 0xFF) << 24));
            index += 4;
            
            // 信号类型 (1字节)
            item.setSignalType(data[index] & 0xFF);
            index += 1;
            
            // 事件等级 (1字节)
            item.setEventSeverity(data[index] & 0xFF);
            index += 1;
            
            // 基类信号ID (4字节)
            item.setBaseTypeId(((data[index] & 0xFF)) |
                              ((data[index + 1] & 0xFF) << 8) |
                              ((data[index + 2] & 0xFF) << 16) |
                              ((data[index + 3] & 0xFF) << 24));
            index += 4;
            
            // 信号标志 (1字节)
            item.setFlag(data[index] & 0xFF);
            index += 1;
            
            // 采集时间 (4字节，Unix时间戳)
            long timestamp = ((data[index] & 0xFF)) |
                           ((data[index + 1] & 0xFF) << 8) |
                           ((data[index + 2] & 0xFF) << 16) |
                           ((data[index + 3] & 0xFF) << 24);
            if (timestamp > 0) {
                item.setSampleTime(new Date(timestamp * 1000));
            }
            index += 4;
            
            // 值类型 (1字节)
            int valueTypeValue = data[index] & 0xFF;
            item.setValueType(MonitorValueType.from(valueTypeValue));
            index += 1;
            
            // 根据值类型解析值
            if (item.getValueType() == MonitorValueType.FLOAT) {
                if (index + 4 <= data.length) {
                    // 解析float值 (4字节)
                    int floatBits = ((data[index] & 0xFF)) |
                                   ((data[index + 1] & 0xFF) << 8) |
                                   ((data[index + 2] & 0xFF) << 16) |
                                   ((data[index + 3] & 0xFF) << 24);
                    item.setFloatValue(Float.intBitsToFloat(floatBits));
                    index += 4;
                }
            } else if (item.getValueType() == MonitorValueType.STRING) {
                if (index + 2 <= data.length) {
                    // 字符串长度 (2字节)
                    int stringLength = ((data[index] & 0xFF)) |
                                     ((data[index + 1] & 0xFF) << 8);
                    index += 2;
                    
                    if (index + stringLength <= data.length && stringLength > 0) {
                        // 字符串内容
                        item.setStringValue(new String(data, index, stringLength).trim());
                        index += stringLength;
                    }
                }
            }
            
            // 设置设备ID和局站ID
            item.setEquipmentId(this.equipmentId);
            
            items.add(item);
        }
        
        this.signalResponseItems = items;
        return index;
    }
}
