package com.siteweb.tcs.south.swap.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scaleout.client.util.BitConverter;
import com.siteweb.tcs.south.swap.dal.entity.HostInfo;
import com.siteweb.tcs.south.swap.enums.EncryptType;
import com.siteweb.tcs.south.swap.enums.MessageType;
import com.siteweb.tcs.south.swap.enums.PipelineType;
import com.siteweb.tcs.south.swap.enums.ProtocolType;
import com.siteweb.tcs.south.swap.util.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * SWAP心跳响应消息
 * <p>
 * 简化版本，类似MobileBResponseMessage
 * 主要包含响应内容（字节数组）
 * </p>
 */
@Getter
@Setter
public class RDSHeartbeatResponseMessage extends SitewebBResponseMessage {

    /**
     * 端口长度
     */
    @JsonProperty("PortLen")
    private static final int PORT_LEN = 4;

    /**
     * 实时数据发送间隔长度
     */
    @JsonProperty("IntervalLen")
    private static final int INTERVAL_LEN = 4;

    /**
     * 实时数据接收端口号，默认7000
     */
    @JsonProperty("RealtimePort")
    private int realtimePort;

    /**
     * 实时数据发送间隔，默认30秒
     */
    @JsonProperty("RealtimeInterval")
    private int realtimeInterval;

    /**
     * 心跳时间
     */
    @JsonProperty("HeartbeatTime")
    private Date heartbeatTime;
    /**
     * 构造函数
     */
    public RDSHeartbeatResponseMessage() {
        super(MessageType.HEARTBEAT_RESPONSE_RDS);
    }

    /**
     * 构造函数（包含原始请求数据包）
     * @param originalRequestPacket 原始请求数据包
     */
    public RDSHeartbeatResponseMessage(byte[] originalRequestPacket) {
        super(MessageType.HEARTBEAT_RESPONSE_RDS, originalRequestPacket);

    }

    @Override
    public SiteWebPacket getResponsePacket(SiteWebPacket siteWebPacket) {
        SiteWebPacket packet = new SiteWebPacket();
        packet.setMessageType(MessageType.HEARTBEAT_RESPONSE_RDS);
        packet.setDestinationHostId(siteWebPacket.getSourceHostId());
        packet.setSourceHostId(0);
        packet.setRequireAcknowledge(true);
        packet.setPipelineType(PipelineType.DIAGNOSTICS);
        packet.setProtocolType(ProtocolType.SWCP);
        packet.setEncryptType(EncryptType.NON_ENCRYPT);
        packet.setSequenceNumber(siteWebPacket.getSequenceNumber());
        return packet;
    }

    /**
     * 实现getResponseInfo方法
     * 类似原来的HeartBeatResponse.toByteArray()，只生成心跳时间数据
     */
    @Override
    public byte[] getResponseInfo() {
        byte[] data = new byte[HostInfo.STARTUP_TIME_LEN + PORT_LEN + INTERVAL_LEN];
        // 心跳时间
        long ntpTime = DateUtil.getTimeStamp(this.getHeartbeatTime());
        System.arraycopy(BitConverter.getBytes(ntpTime), 0, data, 0, HostInfo.STARTUP_TIME_LEN);
        System.arraycopy(BitConverter.getBytes(this.getRealtimePort()), 0, data, HostInfo.STARTUP_TIME_LEN, PORT_LEN);
        System.arraycopy(BitConverter.getBytes(this.getRealtimeInterval()), 0, data, HostInfo.STARTUP_TIME_LEN + PORT_LEN, INTERVAL_LEN);
        return data;
    }

    /**
     * 转换为字节数组
     * 保持StreamMessage体系的一致性，但实际调用getResponseInfo()
     */
    @Override
    public byte[] toByteArray() {
        return getResponseInfo();
    }


    /**
     * 创建心跳失败响应
     * @param deviceId 设备ID
     * @return 心跳失败响应
     */
    public static RDSHeartbeatResponseMessage createFailureResponse(String deviceId) {
        return new RDSHeartbeatResponseMessage();
    }
}
