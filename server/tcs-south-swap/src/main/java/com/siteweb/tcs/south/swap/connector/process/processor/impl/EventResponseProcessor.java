package com.siteweb.tcs.south.swap.connector.process.processor.impl;

import com.siteweb.tcs.south.swap.connector.letter.EventResponseMessage;
import com.siteweb.tcs.south.swap.connector.letter.SiteWebPacket;
import com.siteweb.tcs.south.swap.connector.letter.SitewebBRequestMessage;
import com.siteweb.tcs.south.swap.connector.letter.SitewebRawMessage;
import com.siteweb.tcs.south.swap.connector.process.processor.SitewebMessageProcessor;
import com.siteweb.tcs.south.swap.dal.entity.EventEquipmentItem;
import com.siteweb.tcs.south.swap.dal.entity.EventResponseItem;
import com.siteweb.tcs.south.swap.enums.EventReport;
import com.siteweb.tcs.south.swap.enums.MessageType;
import com.siteweb.tcs.south.swap.service.EventDisplayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 事件响应处理器
 * 处理来自FSU设备的事件响应消息
 * 注意：此处理器不需要UDP响应，只需要展示事件数据
 * 
 * <AUTHOR> zhou
 * @description EventResponseProcessor
 * @createTime 2025-01-21 16:00:00
 */
@Slf4j
@Component
public class EventResponseProcessor implements SitewebMessageProcessor {

    @Autowired
    private EventDisplayService eventDisplayService;

    @Override
    public MessageType getSupportedMessageType() {
        return MessageType.EVENT_RESPONSE;
    }

    @Override
    public SitewebBRequestMessage parseRawSignal(byte[] rawData, SitewebRawMessage rawMessage) {
        try {
            log.debug("Parsing event response message, data length: {}", rawData.length);

            // 创建事件响应消息
            EventResponseMessage eventResponse = new EventResponseMessage();
            
            // 设置局站ID（从原始消息中获取）
            if (rawMessage.getSiteWebPacket() != null && rawMessage.getSiteWebPacket().getSiteWebMessage() != null) {
                eventResponse.setStationId(rawMessage.getSiteWebPacket().getSiteWebMessage().getStationId());
            }
            
            // 解析字节数组
            EventResponseMessage parsedMessage = eventResponse.fromByteArray(
                rawData, 
                SiteWebPacket.HEAD_LENGTH + SiteWebPacket.SWDP_OFFSET
            );
            List<EventResponseItem> eventResponseItems = new ArrayList<>();
            if (eventResponse.getEventReport() == EventReport.GENERAL) {
                eventResponseItems = getEventResponseItems(eventResponse);
            } else if (eventResponse.getEventReport() == EventReport.FINISHED) {

            } else if (eventResponse.getEventReport() == EventReport.HISTORY) {
                getEventResponseItems(eventResponse);
            }
            
            if (parsedMessage != null) {
                log.debug("Successfully parsed event response with {} equipment items, event report: {}", 
                         parsedMessage.getEquipmentItems().size(),
                         parsedMessage.getEventReport());
                return parsedMessage;
            } else {
                log.warn("Failed to parse event response message");
                return null;
            }

        } catch (Exception e) {
            log.error("Error parsing event response message", e);
            return null;
        }
    }

    private List<EventResponseItem> getEventResponseItems(EventResponseMessage eventResponse) {
        List<EventResponseItem> eventResponseItems = new ArrayList<>();
        for (EventEquipmentItem equipmentItem : eventResponse.getEquipmentItems()) {
            for (EventResponseItem eventResponseItem : equipmentItem.getEventResponseItems()) {
                eventResponseItem.setStationId(eventResponse.getStationId());
                eventResponseItem.setEquipmentId(equipmentItem.getEquipmentId());
                eventResponseItems.add(eventResponseItem);
            }
        }
        return eventResponseItems;
    }
    @Override
    public boolean handleMessage(SitewebBRequestMessage requestMessage, SitewebRawMessage rawMessage) {
        if (!(requestMessage instanceof EventResponseMessage)) {
            log.warn("Expected EventResponseMessage, got: {}", requestMessage.getClass().getSimpleName());
            return false;
        }

        EventResponseMessage eventResponse = (EventResponseMessage) requestMessage;

        try {
            log.info("Processing event response - StationId: {}, EventReport: {}, EquipmentCount: {}, TotalEvents: {}", 
                     eventResponse.getStationId(), 
                     eventResponse.getEventReport(),
                     eventResponse.getEquipmentItems().size(),
                     eventResponse.getTotalEventCount());

            // 处理事件数据 - 只展示，不存储
            return processEventData(eventResponse);

        } catch (Exception e) {
            log.error("Error handling event response message", e);
            return false;
        }
    }

    @Override
    public byte[] generateResponsePacket(SitewebBRequestMessage requestMessage, 
                                        SitewebRawMessage rawMessage, 
                                        boolean handleSuccess) {
        // 事件响应不需要UDP响应，直接返回null
        log.debug("Event response does not require UDP response, returning null");
        return null;
    }

    /**
     * 处理事件数据
     * 只展示数据，不进行Redis存储等操作
     * 
     * @param eventResponse 事件响应消息
     * @return 处理是否成功
     */
    private boolean processEventData(EventResponseMessage eventResponse) {
        try {
            int totalEventCount = 0;
            int validEventCount = 0;

            // 遍历所有设备项
            for (EventEquipmentItem equipmentItem : eventResponse.getEquipmentItems()) {
                log.info("Processing equipment {} with {} events", 
                         equipmentItem.getEquipmentId(), 
                         equipmentItem.getEventResponseItems().size());

                // 遍历设备的所有事件项
                for (EventResponseItem eventItem : equipmentItem.getEventResponseItems()) {
                    totalEventCount++;
                    
                    // 过滤无效事件
                    if (!isValidEvent(eventItem)) {
                        log.debug("Skipping invalid event - EquipmentId: {}, DataId: {}", 
                                 eventItem.getEquipmentId(), eventItem.getDataId());
                        continue;
                    }
                    
                    validEventCount++;
                    
                    // 设置设备ID和局站ID
                    eventItem.setEquipmentId(equipmentItem.getEquipmentId());
                    eventItem.setStationId(eventResponse.getStationId());
                    
                    // 展示事件数据
                    displayEventData(eventItem, eventResponse);
                }
            }

            log.info("Event processing completed - Total: {}, Valid: {}, Invalid: {}", 
                     totalEventCount, validEventCount, totalEventCount - validEventCount);

            // 调用展示服务
            eventDisplayService.displayEventData(
                eventResponse.getEquipmentItems(), 
                eventResponse.getEventReport(),
                eventResponse.getStationId()
            );

            return true;

        } catch (Exception e) {
            log.error("Error processing event data", e);
            return false;
        }
    }

    /**
     * 检查事件是否有效
     * 
     * @param eventItem 事件项
     * @return true表示有效，false表示无效
     */
    private boolean isValidEvent(EventResponseItem eventItem) {
        // 过滤掉开始时间为null的记录
        if (eventItem.getStartTime() == null) {
            return false;
        }
        
        return true;
    }

    /**
     * 展示事件数据（日志输出）
     * 
     * @param eventItem 事件项
     * @param eventResponse 事件响应消息
     */
    private void displayEventData(EventResponseItem eventItem, EventResponseMessage eventResponse) {
        log.info("received activeEvent {}", eventItem);
    }
}
