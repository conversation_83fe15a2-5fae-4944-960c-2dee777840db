<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.siteweb</groupId>
        <artifactId>thing-connect-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>tcs-north-s6</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
        <plugin.id>tcs-north-s6</plugin.id>
        <plugin.name>siteweb6北向接口</plugin.name>
        <plugin.class>com.siteweb.tcs.north.s6.NorthS6Plugin</plugin.class>
        <plugin.version>1.0.0</plugin.version>
        <plugin.provider>Siteweb</plugin.provider>
        <plugin.applicationName>siteweb6北向接口</plugin.applicationName>
        <plugin.dependencies/>
        <webroot.resource.path>META-INF/com-siteweb-webroot/plugins/${plugin.id}</webroot.resource.path>
    </properties>

    <dependencies>
        <!-- TCS 核心依赖 -->
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-middleware</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- 插件通用依赖 -->
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-plugin-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Hub依赖 -->
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-hub</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- 中间件通用依赖 -->
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-middleware-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- MyBatis-Plus 依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.3</version>
        </dependency>
        
        <!-- MySQL 驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>
        
        <!-- Flyway 依赖 -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>9.22.3</version>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- HTTP客户端 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 写入插件信息 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Plugin-Id>${plugin.id}</Plugin-Id>
                            <Plugin-Name>${plugin.name}</Plugin-Name>
                            <Plugin-Version>${plugin.version}</Plugin-Version>
                            <Plugin-Provider>${plugin.provider}</Plugin-Provider>
                            <Plugin-Class>${plugin.class}</Plugin-Class>
                            <Plugin-Dependencies>${plugin.dependencies}</Plugin-Dependencies>
                            <Plugin-BuildTime>${maven.build.timestamp}</Plugin-BuildTime>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <!-- 前端构建插件 -->
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>1.15.0</version>
                <configuration>
                    <workingDirectory>src/main/web</workingDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>install-node-and-npm</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>${node.version}</nodeVersion>
                            <npmVersion>${npm.version}</npmVersion>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm-install-pnpm</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <arguments>install -g pnpm --registry=https://registry.npmmirror.com</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>pnpm-install</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <arguments>exec pnpm install --registry=https://registry.npmmirror.com</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>pnpm-build</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>exec pnpm run build</arguments>
                            <environmentVariables>
                                <PROJECT_VERSION>${project.version}</PROJECT_VERSION>
                            </environmentVariables>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            
            <!-- 插件打包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <descriptors>
                        <descriptor>../plugin.release.xml</descriptor>
                    </descriptors>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <!-- 前端资源 -->
            <resource>
                <directory>target/dist</directory>
                <targetPath>${webroot.resource.path}</targetPath>
                <filtering>false</filtering>
            </resource>
            <!-- 后端资源 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project> 