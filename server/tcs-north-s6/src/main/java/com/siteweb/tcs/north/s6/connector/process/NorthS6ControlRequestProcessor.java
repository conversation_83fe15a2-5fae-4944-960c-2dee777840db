package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.util.TCSTopicUtil;
import com.siteweb.tcs.north.s6.connector.letter.NorthS6ControlRequestMessage;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description: 北向S6控制请求处理器-接受来自北向系统的控制请求
 * @author: xsx
 * @create: 2025-08-28 10:07
 **/

public class NorthS6ControlRequestProcessor extends ProbeActor {

    /**
     * 发布消息主题
     */
    private final String topicName;


    private NorthS6ControlRequestProcessor(GatewayMap gatewayMap){
        this.topicName = TCSTopicUtil.getTopicName(SubscriptionTypeEnum.CONTROL_COMMAND_REQUEST,gatewayMap.getGatewayId());
    }

    public static Props props(GatewayMap gatewayMap){
        return Props.create(NorthS6ControlRequestProcessor.class,gatewayMap);
    }

    @Override
    public Receive createReceive() {
        Receive receive = ReceiveBuilder.create().match(NorthS6ControlRequestMessage.class, this::onNorthS6ControlRequestMessage).build();
        return super.createReceive().orElse(receive);
    }

    private void onNorthS6ControlRequestMessage(NorthS6ControlRequestMessage controlRequestMessage) {

    }
}