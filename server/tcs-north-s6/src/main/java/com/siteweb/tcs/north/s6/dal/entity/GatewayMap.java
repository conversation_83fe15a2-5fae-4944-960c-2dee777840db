package com.siteweb.tcs.north.s6.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 网关映射表实体类
 */
@Data
@TableName("tcs_gateway_map")
public class GatewayMap implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * hub全局网关ID
     */
    @TableField("GatewayId")
    private Long gatewayId;

    /**
     * siteweb采集单元ID
     */
    @TableField("NorthMonitorUnitId")
    private Integer northMonitorUnitId;

    /**
     * 局站id
     */
    @TableField("NorthStationId")
    private Integer northStationId;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "false", delval = "true")
    @TableField("Deleted")
    private Boolean deleted;

    @TableField(exist = false)
    private List<DeviceMap> deviceMapList;
} 