package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandResponseChange;
import com.siteweb.tcs.hub.util.TCSTopicUtil;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description: 北向S6控制响应处理器-接受hub的控制响应
 * @author: xsx
 * @create: 2025-08-28 10:08
 **/

public class NorthS6ControlResponseProcessor  extends ProbeActor {

    private final ActorRef mediator;


    public static Props props(GatewayMap gatewayMap){
        return Props.create(NorthS6ControlResponseProcessor.class,gatewayMap);
    }


    private NorthS6ControlResponseProcessor(GatewayMap gatewayMap){
        this.mediator = DistributedPubSub.get(getContext().system()).mediator();
        handlerSubscribe(gatewayMap.getGatewayId());
        handleInit(gatewayMap);
    }

    private void handlerSubscribe(Long gatewayGuid) {
        String topicName = TCSTopicUtil.getTopicName(SubscriptionTypeEnum.CONTROL_COMMAND_RESPONSE, gatewayGuid);
        mediator.tell(new DistributedPubSubMediator.Subscribe(topicName, getSelf()), getSelf());
    }

    private void handleInit(GatewayMap gatewayMap) {
        //初始化数据
    }


    @Override
    public Receive createReceive() {
        Receive receive = ReceiveBuilder.create().match(DeviceControlCommandResponseChange.class, this::onDeviceControlCommandResponseChange).build();
        return super.createReceive().orElse(receive);
    }

    private void onDeviceControlCommandResponseChange(DeviceControlCommandResponseChange controlCommandResponseChange) {

    }
}
