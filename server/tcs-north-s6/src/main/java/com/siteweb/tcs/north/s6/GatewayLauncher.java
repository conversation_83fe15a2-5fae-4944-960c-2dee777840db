package com.siteweb.tcs.north.s6;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.north.s6.connector.letter.TestNorthS6Message;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.web.service.IGatewayMapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-08-28 17:29
 **/
@Slf4j
@Service
public class GatewayLauncher {

    @Autowired
    private IGatewayMapService gatewayMapService;

    @Autowired
    @Qualifier(value = "tcs-north-s6-gateway-sharding")
    private ActorRef actorRef;


    public void loadGateway(){
        List<GatewayMap> list = gatewayMapService.list();
        if(CollectionUtil.isNotEmpty(list)){
            for (GatewayMap gatewayMap : list) {
                Long gatewayId = gatewayMap.getGatewayId();
                TestNorthS6Message northS6Message = new TestNorthS6Message(gatewayId);
                actorRef.tell(northS6Message,ActorRef.noSender());
            }
        }
    }
}
