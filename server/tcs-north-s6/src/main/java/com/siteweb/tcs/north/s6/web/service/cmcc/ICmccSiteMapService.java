package com.siteweb.tcs.north.s6.web.service.cmcc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;

import java.util.List;

/**
 * CMCC站点映射表 服务接口
 */
public interface ICmccSiteMapService extends IService<CmccSiteMap> {

    /**
     * 根据CMCC站点ID查询站点映射信息
     * @param cmccSiteId CMCC站点ID
     * @return 站点映射信息
     */
    CmccSiteMap getByCmccSiteId(String cmccSiteId);

    /**
     * 根据SiteWeb局站ID查询站点映射信息列表
     * @param stationId SiteWeb局站ID
     * @return 站点映射信息列表
     */
    List<CmccSiteMap> getByStationId(Integer stationId);

    /**
     * 根据CMCC站点类型查询站点映射信息列表
     * @param cmccSiteType CMCC站点类型
     * @return 站点映射信息列表
     */
    List<CmccSiteMap> getByCmccSiteType(Integer cmccSiteType);

    /**
     * 批量保存站点映射信息
     * @param siteMapList 站点映射信息列表
     * @return 是否成功
     */
    boolean batchSave(List<CmccSiteMap> siteMapList);

    /**
     * 清空并批量插入站点映射信息
     * @param siteMapList 站点映射信息列表
     * @return 是否成功
     */
    boolean clearAndBatchInsert(List<CmccSiteMap> siteMapList);

    CmccSiteMap getBySiteId(String siteId);
}
