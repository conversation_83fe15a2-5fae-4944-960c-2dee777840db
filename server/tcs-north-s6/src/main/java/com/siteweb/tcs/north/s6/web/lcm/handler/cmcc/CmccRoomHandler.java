package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccRoomInfoDTO;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccRoomMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccRoomMapService;
import com.siteweb.tcs.siteweb.entity.House;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * CMCC机房处理器
 * 负责处理机房相关的创建、更新、删除逻辑
 */
@Component
public class CmccRoomHandler extends CmccBaseHandler {

    @Autowired
    private ICmccRoomMapService cmccRoomMapService;

    /**
     * 处理机房信息
     * @param cmccRoomInfoDTO 机房信息
     * @param cmccRoomMapMap 机房映射表
     * @param cmccSiteMap 站点信息，机房挂到哪个站点下
     */
    public void handleRoomInfo(CmccRoomInfoDTO cmccRoomInfoDTO, 
                               Map<String, CmccRoomMap> cmccRoomMapMap,
                               CmccSiteMap cmccSiteMap) {
        // 已存在，直接返回
        String cmccSiteId = cmccSiteMap.getCmccSiteId();
        if (cmccRoomMapMap.containsKey(cmccRoomInfoDTO.getUniqueKey())) {
            return;
        }

        // 从数据库查询
        CmccRoomMap cmccRoomMap = cmccRoomMapService.getByCmccSiteIdAndRoomId(cmccSiteId, cmccRoomInfoDTO.getRoomId());
        
        // 判断是更新还是新建
        boolean needUpdate = ObjectUtil.isNotEmpty(cmccRoomMap) &&
                (!StringUtils.equals(cmccRoomMap.getCmccRoomName(), cmccRoomInfoDTO.getRoomName())
                        || !NumberUtil.equals(cmccRoomMap.getCmccRoomType(), cmccRoomInfoDTO.getRoomType()));

        House house = buildRoomDTO(cmccRoomMap, cmccRoomInfoDTO, needUpdate);
        if (needUpdate) {
            updateRoom(house);
        } else if(ObjectUtil.isEmpty(cmccRoomMap)) {
            cmccRoomMap = createRoom(cmccRoomInfoDTO, cmccSiteMap, house);
        }
        cmccRoomMapMap.put(cmccRoomInfoDTO.getUniqueKey(), cmccRoomMap);
    }

    /**
     * 创建新机房
     */
    private CmccRoomMap createRoom(CmccRoomInfoDTO cmccRoomInfoDTO, CmccSiteMap cmccSiteMap, House house) {
        CmccRoomMap cmccRoomMap = cmccRoomInfoDTO.toCmccRoomMap();
        cmccRoomMap.setStationId(cmccSiteMap.getStationId());
        house.setStationId(cmccSiteMap.getStationId());
        sitewebPersistentService.getConfigAPI().createForHouse(house);
        cmccRoomMap.setHouseId(house.getHouseId());
        
        cmccRoomMapService.save(cmccRoomMap);
        return cmccRoomMap;
    }

    /**
     * 更新机房
     */
    private void updateRoom(House house) {
        sitewebPersistentService.getConfigAPI().updateForHouse(house);
    }

    /**
     * 构建机房DTO
     */
    private House buildRoomDTO(CmccRoomMap cmccRoomMap, CmccRoomInfoDTO cmccRoomInfoDTO, boolean needUpdate) {
        House house = new House();
        if (needUpdate) {
            house.setStationId(cmccRoomMap.getStationId());
            house.setHouseId(cmccRoomMap.getHouseId());
        }
        house.setHouseName(cmccRoomInfoDTO.getRoomName());
        return house;
    }
}
