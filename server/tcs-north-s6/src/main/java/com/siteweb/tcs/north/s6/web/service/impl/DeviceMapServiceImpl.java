package com.siteweb.tcs.north.s6.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import com.siteweb.tcs.north.s6.dal.mapper.DeviceMapMapper;
import com.siteweb.tcs.north.s6.web.service.IDeviceMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 设备映射服务实现类
 */
@Service
public class DeviceMapServiceImpl extends ServiceImpl<DeviceMapMapper, DeviceMap> implements IDeviceMapService {

    @Resource
    private DeviceMapMapper deviceMapMapper;

    @Override
    public List<DeviceMap> getByGatewayId(Long gatewayId) {
        return deviceMapMapper.selectByGatewayId(gatewayId);
    }

    @Override
    public DeviceMap getByDeviceId(Long deviceId) {
        LambdaQueryWrapper<DeviceMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceMap::getDeviceId,deviceId);
        List<DeviceMap> deviceMaps = deviceMapMapper.selectList(queryWrapper);
        return deviceMaps.isEmpty() ? null : deviceMaps.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<DeviceMap> deviceMaps) {
        if (deviceMaps == null || deviceMaps.isEmpty()) {
            return false;
        }
        return deviceMapMapper.insertBatch(deviceMaps) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCompositeKey(Long gatewayId, Long deviceId, Integer northEquipmentId) {
        return deviceMapMapper.deleteByCompositeKey(gatewayId, deviceId, northEquipmentId) > 0;
    }

    @Override
    public List<DeviceMap> getNorthEquipmentIdListByGatewayId(Long gatewayId) {
        LambdaQueryWrapper<DeviceMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceMap::getGatewayId,gatewayId)
                .select(DeviceMap::getNorthEquipmentTemplateId)
                .select(DeviceMap::getDeviceId);
        List<DeviceMap> deviceMaps = deviceMapMapper.selectList(queryWrapper);
        return deviceMaps;
    }

    @Override
    public boolean deleteByGatewayId(Long gatewayId) {
        LambdaQueryWrapper<DeviceMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceMap::getGatewayId,gatewayId);
        int delete = deviceMapMapper.delete(queryWrapper);
        return delete>0;
    }

    @Override
    public Integer getNorthEquipmentIdByDeviceId(Long deviceId) {
        LambdaQueryWrapper<DeviceMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceMap::getDeviceId,deviceId);
        DeviceMap deviceMap = deviceMapMapper.selectOne(queryWrapper);
        Integer equipmentTemplateId = Optional.ofNullable(deviceMap).map(DeviceMap::getNorthEquipmentTemplateId).get();
        return equipmentTemplateId;
    }

    @Override
    public boolean deleteByDeviceId(Long deviceId) {
        LambdaQueryWrapper<DeviceMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceMap::getDeviceId,deviceId);
        return deviceMapMapper.delete(queryWrapper)>0;
    }
}