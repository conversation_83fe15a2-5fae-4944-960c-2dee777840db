package com.siteweb.tcs.north.s6.dal.dto.cmcc;

import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccStationType;
import lombok.Data;

/**
 * @program: tcs2
 * @description: 移动站点信息数据传输类
 * @author: xsx
 * @create: 2025-08-20 10:13
 **/
@Data
public class CmccSiteInfoDTO {
    private String siteId;
    private String siteName;
    private Integer siteType;
    private String siteTypeName;

    public CmccStationType toCmccStationType(){
        CmccStationType cmccStationType = new CmccStationType();
        cmccStationType.setStationTypeId(siteType);
        cmccStationType.setStationTypeName(siteTypeName);
        return cmccStationType;
    }
}
