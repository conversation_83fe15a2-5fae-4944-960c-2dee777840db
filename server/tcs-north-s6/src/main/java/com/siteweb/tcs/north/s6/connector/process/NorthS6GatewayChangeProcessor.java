package com.siteweb.tcs.north.s6.connector.process;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.hub.domain.letter.enums.EnumGatewayConnectState;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.GatewayChange;
import com.siteweb.tcs.hub.util.TCSTopicUtil;
import com.siteweb.tcs.north.s6.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.s6.connector.letter.DelRedisItemAction;
import com.siteweb.tcs.north.s6.connector.letter.SetRedisItemAction;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.dal.entity.MonitorUnitRoute;
import com.siteweb.tcs.north.s6.dal.entity.MonitorUnitState;
import nonapi.io.github.classgraph.json.JSONSerializer;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Cancellable;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import org.apache.pekko.japi.pf.ReceiveBuilder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;

/**
 * @program: tcs2
 * @description: 北向网关自诊断处理器
 * @author: xsx
 * @create: 2025-08-28 09:54
 **/

public class NorthS6GatewayChangeProcessor extends ProbeActor {


    private final ActorRef mediator;

    private ActorRef sitewebRedisSink;

    private Pair<Long,Integer> idPair;

    private StringRedisTemplate redisTemplate;

    private EnumGatewayConnectState gatewayConnectState;

    private final Cancellable scheduler;

    public static Props props(GatewayMap gatewayMap, ActorRef sitewebRedisSink){
        return Props.create(NorthS6GatewayChangeProcessor.class,gatewayMap,sitewebRedisSink);
    }


    private NorthS6GatewayChangeProcessor(GatewayMap gatewayMap,ActorRef sitewebRedisSink){
        this.mediator = DistributedPubSub.get(getContext().system()).mediator();
        this.sitewebRedisSink = sitewebRedisSink;
        handlerSubscribe(gatewayMap.getGatewayId());
        handleInit(gatewayMap);
        scheduler = getContext().getSystem().scheduler().scheduleAtFixedRate(Duration.ofSeconds(30), Duration.ofSeconds(60), () -> reportHeartBeat(), getContext().getDispatcher());
    }

    private void reportHeartBeat() {
        // 获取内容
        String redisKey = GatewayChange.getRedisKey(idPair.getValue());
        String value = redisTemplate.opsForValue().get(redisKey);
        // 更新事件
        if(StringUtils.isNotEmpty(value)){

            MonitorUnitState monitorUnitState = JSONUtil.toBean(value, MonitorUnitState.class);
            monitorUnitState.setCurrentTimeStamp();
            SetRedisItemAction setRedisItemAction = new SetRedisItemAction();
            setRedisItemAction.addItem(redisKey,JSONSerializer.serializeObject(monitorUnitState));

            MonitorUnitRoute monitorUnitRoute = new MonitorUnitRoute(idPair.getValue());
            setRedisItemAction.addItem(monitorUnitRoute.getRedisKey(), JSONSerializer.serializeObject(monitorUnitRoute));
            sitewebRedisSink.tell(setRedisItemAction,getSelf());
        }

    }

    private void handlerSubscribe(Long gatewayGuid) {
        String topicName = TCSTopicUtil.getTopicName(SubscriptionTypeEnum.GATEWAY_STATE_CHANGE, gatewayGuid);
        mediator.tell(new DistributedPubSubMediator.Subscribe(topicName, getSelf()), getSelf());
    }

    private void handleInit(GatewayMap gatewayMap) {
        //初始化数据
         this.idPair = Pair.of(gatewayMap.getGatewayId(),gatewayMap.getNorthMonitorUnitId());
         redisTemplate = ConnectorDataHolder.getInstance().getBean("SitewebRedisTemplate",RedisTemplate.class);
    }


    @Override
    public Receive createReceive() {
        Receive receive = ReceiveBuilder.create().match(GatewayChange.class, this::onGatewayChange).build();
        return super.createReceive().orElse(receive);
    }

    private void onGatewayChange(GatewayChange gatewayChange) {
        this.gatewayConnectState = gatewayChange.getConnectState();
        // 写入路由信息
        handleRoute(gatewayChange);
        // 写入MuState
        handleState(gatewayChange);
    }

    private void handleState(GatewayChange gatewayChange) {
        MonitorUnitRoute monitorUnitRoute = new MonitorUnitRoute(idPair.getValue());
        switch (gatewayChange.getConnectState()){
            case ONLINE :
                //写路由
                SetRedisItemAction setRedisItemAction = new SetRedisItemAction();
                setRedisItemAction.addItem(monitorUnitRoute.getRedisKey(), JSONSerializer.serializeObject(monitorUnitRoute));
                sitewebRedisSink.tell(setRedisItemAction,getSelf());
                break;
            case OFFLINE:
                //删路由
                DelRedisItemAction delRedisItemAction = new DelRedisItemAction();
                delRedisItemAction.addItem(monitorUnitRoute.getRedisKey());
                sitewebRedisSink.tell(delRedisItemAction,getSelf());
                break;
            default:
                break;
        }
    }

    private void handleRoute(GatewayChange gatewayChange) {
        switch (gatewayChange.getConnectState()){
            case ONLINE :
                MonitorUnitState monitorUnitState = MonitorUnitState.fromMonitorUnitChange(gatewayChange,idPair.getValue());
                SetRedisItemAction setRedisItemAction = new SetRedisItemAction();
                setRedisItemAction.addItem(gatewayChange.getRedisKey(idPair.getValue()),JSONSerializer.serializeObject(monitorUnitState));
                sitewebRedisSink.tell(setRedisItemAction,getSelf());
                break;
            default:
                break;
        }
    }

    @Override
    public void postStop() {
        if(ObjectUtil.isNotEmpty(scheduler)){
            scheduler.cancel();
        }
    }
}
