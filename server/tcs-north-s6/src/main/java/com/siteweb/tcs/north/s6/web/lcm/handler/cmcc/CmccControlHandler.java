package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.dal.dto.ControlConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;
import com.siteweb.tcs.north.s6.dal.entity.AlarmMap;
import com.siteweb.tcs.north.s6.dal.entity.ControlMap;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import com.siteweb.tcs.north.s6.web.service.IControlMapService;
import com.siteweb.tcs.siteweb.dto.ControlConfigItem;
import com.siteweb.tcs.siteweb.dto.EventConfigItem;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.Event;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * CMCC控制处理器
 * 负责处理控制相关的创建、更新、删除逻辑
 */
@Component
public class CmccControlHandler extends CmccBaseHandler {

    @Autowired
    private IControlMapService controlMapService;


    /**
     * 处理控制信息
     */
    public void handleControl(DeviceConfigChangeDto deviceConfigChangeDto, DeviceMap deviceMap) {
        List<ControlConfigChangeDto> controls = deviceConfigChangeDto.getControls();
        if(CollectionUtil.isEmpty(controls)) return;
        for (ControlConfigChangeDto control : controls) {
            switch (control.getLifeCycleEvent()) {
                case CREATE:
                    createControl(control, deviceMap);
                    break;
                case DELETE:
                    deleteControl(control, deviceMap);
                    break;
                case UPDATE:
                    updateControl(control, deviceMap);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 创建控制
     */
    private void createControl(ControlConfigChangeDto controlConfigChangeDto, DeviceMap deviceMap) {
        // TODO: 实现创建控制逻辑
        ControlConfigItem controlConfigItem = cmccConfigParser.parseControlConfigItem(controlConfigChangeDto, deviceMap);
        sitewebPersistentService.getConfigAPI().createForControl(controlConfigItem);
        ControlMap controlMap = new ControlMap();
        controlMap.setDeviceId(controlConfigChangeDto.getDeviceId());
        controlMap.setNorthControlId(controlConfigItem.getControlId());
        controlMap.setNorthEquipmentId(deviceMap.getNorthEquipmentId());
        controlMap.setControlId(controlConfigChangeDto.getId());
        controlMapService.save(controlMap);
    }

    /**
     * 删除控制
     */
    private void deleteControl(ControlConfigChangeDto control, DeviceMap deviceMap) {
        ControlMap controlMap = controlMapService.getByControlId(control.getId());
        if(ObjectUtil.isEmpty(controlMap)) return;
        sitewebPersistentService.getConfigAPI().deleteForControl(deviceMap.getNorthEquipmentTemplateId(), controlMap.getNorthControlId());
        boolean flag = controlMapService.deleteByControlId(control.getId());
    }

    /**
     * 更新控制
     */
    private void updateControl(ControlConfigChangeDto control, DeviceMap deviceMap) {
        ControlMap controlMap = controlMapService.getByControlId(control.getId());
        if(ObjectUtil.isEmpty(controlMap)) return;
        ControlConfigItem controlConfigItem = cmccConfigParser.parseControlConfigItem(control, deviceMap);
        controlConfigItem.setControlId(controlMap.getNorthControlId());
        boolean flag = sitewebPersistentService.getConfigAPI().updateForControl(controlConfigItem);
    }

    public boolean handleDeleteByDeviceId(Long deviceId) {
        boolean flag = controlMapService.deleteByDeviceId(deviceId);
        return flag;
    }
}
