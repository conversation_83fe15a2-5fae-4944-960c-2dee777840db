package com.siteweb.tcs.north.s6.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.AlarmMap;
import com.siteweb.tcs.north.s6.dal.entity.SignalMap;
import com.siteweb.tcs.north.s6.dal.mapper.AlarmMapMapper;
import com.siteweb.tcs.north.s6.web.service.IAlarmMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 告警映射服务实现类
 */
@Service
public class AlarmMapServiceImpl extends ServiceImpl<AlarmMapMapper, AlarmMap> implements IAlarmMapService {

    @Resource
    private AlarmMapMapper alarmMapMapper;

    @Override
    public List<AlarmMap> getByDeviceId(Integer deviceId) {
        return alarmMapMapper.selectByDeviceId(deviceId);
    }

    @Override
    public List<AlarmMap> getByNorthEquipmentId(Integer northEquipmentId) {
        return alarmMapMapper.selectByNorthEquipmentId(northEquipmentId);
    }

    @Override
    public AlarmMap getByAlarmId(Long alarmId) {
        LambdaQueryWrapper<AlarmMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmMap::getAlarmId,alarmId);
        return alarmMapMapper.selectOne(queryWrapper);
    }

    @Override
    public List<AlarmMap> getByNorthEventId(Integer northEventId) {
        return alarmMapMapper.selectByNorthEventId(northEventId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCompositeKey(Integer deviceId, Integer northEquipmentId, Long alarmId, 
                                       Integer northEventId, Integer northEventConditionId) {
        return alarmMapMapper.deleteByCompositeKey(deviceId, northEquipmentId, alarmId, 
                                                  northEventId, northEventConditionId) > 0;
    }

    @Override
    public boolean deleteByDeviceId(Long deviceId) {
        LambdaQueryWrapper<AlarmMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmMap::getDeviceId,deviceId);
        return alarmMapMapper.delete(queryWrapper)>0;
    }

    @Override
    public boolean deleteByAlarmId(Long alarmId) {
        LambdaQueryWrapper<AlarmMap> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(AlarmMap::getAlarmId,alarmId);
        return alarmMapMapper.delete(queryWrapper)>0;
    }
} 