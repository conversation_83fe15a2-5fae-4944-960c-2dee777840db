package com.siteweb.tcs.north.s6.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.dal.mapper.GatewayMapMapper;
import com.siteweb.tcs.north.s6.web.service.IGatewayMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网关映射服务实现类
 */
@Service
public class GatewayMapServiceImpl extends ServiceImpl<GatewayMapMapper, GatewayMap> implements IGatewayMapService {

    @Resource
    private GatewayMapMapper gatewayMapMapper;

    @Override
    public GatewayMap getByGatewayId(Long gatewayId) {
        QueryWrapper<GatewayMap> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("GatewayId", gatewayId);
        return gatewayMapMapper.selectOne(queryWrapper);
    }

    @Override
    public List<GatewayMap> getByNorthMonitorUnitId(Integer northMonitorUnitId) {
        return gatewayMapMapper.selectByNorthMonitorUnitId(northMonitorUnitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<GatewayMap> gatewayMaps) {
        if (gatewayMaps == null || gatewayMaps.isEmpty()) {
            return false;
        }
        return gatewayMapMapper.insertBatch(gatewayMaps) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCompositeKey(Long gatewayId, Integer northMonitorUnitId) {
        return gatewayMapMapper.deleteByCompositeKey(gatewayId, northMonitorUnitId) > 0;
    }

    @Override
    public List<GatewayMap> getAllActive() {
        return gatewayMapMapper.selectAllActive();
    }

    @Override
    public boolean deleteByGatewayId(Long gatewayId) {
        QueryWrapper<GatewayMap> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("GatewayId", gatewayId);
        return gatewayMapMapper.delete(queryWrapper) > 0;
    }

    @Override
    public List<GatewayMap> getGatewayMapWithFullRelationsByGatewayId(Long gatewayId) {
        return gatewayMapMapper.selectGatewayMapWithFullRelationsByGatewayId(gatewayId);
    }
} 