package com.siteweb.tcs.north.s6.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import com.siteweb.tcs.north.s6.dal.entity.SignalMap;
import com.siteweb.tcs.north.s6.dal.mapper.SignalMapMapper;
import com.siteweb.tcs.north.s6.web.service.ISignalMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 信号映射服务实现类
 */
@Service
public class SignalMapServiceImpl extends ServiceImpl<SignalMapMapper, SignalMap> implements ISignalMapService {

    @Resource
    private SignalMapMapper signalMapMapper;

    @Override
    public List<SignalMap> getByDeviceId(Long deviceId) {
        return signalMapMapper.selectByDeviceId(deviceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<SignalMap> signalMaps) {
        if (signalMaps == null || signalMaps.isEmpty()) {
            return false;
        }
        return signalMapMapper.insertBatch(signalMaps) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCompositeKey(Long deviceId, Long signalId, Integer northSignalId) {
        return signalMapMapper.deleteByCompositeKey(deviceId, signalId, northSignalId) > 0;
    }

    @Override
    public boolean deleteByDeviceId(Long deviceId) {
        LambdaQueryWrapper<SignalMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SignalMap::getDeviceId,deviceId);
        return signalMapMapper.delete(queryWrapper)>0;
    }

    @Override
    public SignalMap getSignalMapById(Long signalId) {
        LambdaQueryWrapper<SignalMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SignalMap::getSignalId,signalId);
        return signalMapMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean deleteBySignalId(Long signalId) {
        LambdaQueryWrapper<SignalMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SignalMap::getSignalId,signalId);
        return signalMapMapper.delete(queryWrapper)>0;
    }
} 