package com.siteweb.tcs.north.s6.web.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.entity.StationStructure;
import com.siteweb.tcs.siteweb.service.IEquipmentTemplateService;
import com.siteweb.tcs.siteweb.service.ISamplerService;
import com.siteweb.tcs.siteweb.service.IStationStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: tcs2
 * @description: siteweb默认参数提供类
 * @author: xsx
 * @create: 2025-08-21 09:34
 **/
@Service
public class SiteWebDefaultProvider {
    private static final String DEFAULT_STATION_STRUCTURE_NAME = "TCS默认层级";

    private static final String DEFAULT_STATION_STRUCTURE_FLAG = "TCS Create";

    private StationStructure tcsStationStructure;

    private static Integer bInterfaceSamplerId;

    private static final String bInterfaceSamplerProtocolCode = "BInterface-HOST设备6-00";

    private static final String TCS_EQUIPMENT_FLAG = "TCS Create";

    private Map<Integer,Integer> equipmemtTemplatIdMap = new HashMap<>();


    @Autowired
    @Qualifier(value = "s6SitewebPersistentService")
    private SitewebPersistentService sitewebPersistentService;



    public StationStructure getDefaultStationStructure() {
        // 如果已有缓存，直接返回
        if (ObjectUtil.isNotEmpty(tcsStationStructure)) {
            return tcsStationStructure;
        }

        // 从服务中获取默认结构
        StationStructure defaultStructure = sitewebPersistentService.getConfigAPI().getTCSStationStructure();
        if (ObjectUtil.isNotEmpty(defaultStructure)) {
            return tcsStationStructure = defaultStructure;
        }

        // 获取根结构
        StationStructure rootStructure = sitewebPersistentService.getConfigAPI().getRootStationStructure();
        if (ObjectUtil.isEmpty(rootStructure)) {
            return null;
        }

        // 创建新结构
        StationStructure newStructure = new StationStructure();
        newStructure.setParentStructureId(rootStructure.getStructureId());
        newStructure.setStructureName(DEFAULT_STATION_STRUCTURE_NAME);
        newStructure.setDescription(DEFAULT_STATION_STRUCTURE_FLAG);
        newStructure.setEnable(true);
        newStructure.setStructureGroupId(1);
        newStructure.setIsUngroup(false);
        newStructure.setStructureType(1);
        newStructure.setLevelPath(rootStructure.getLevelPath());
        sitewebPersistentService.getConfigAPI().createForStationStructure(newStructure);

        return tcsStationStructure = newStructure;
    }

    public Integer getDefaultSamplerIdByProtocolCode(){
        if(ObjectUtil.isEmpty(bInterfaceSamplerId)){
            Sampler tslSampler = sitewebPersistentService.getConfigAPI().findByProtocolCodeForSampler(bInterfaceSamplerProtocolCode);
            bInterfaceSamplerId = tslSampler.getSamplerId();
            return bInterfaceSamplerId;
        }else {
            return bInterfaceSamplerId;
        }
    }

    public int getDefaultEquipmentTemplateId(Integer equipmentCategoryId,String equipmentTemplateName) {
        Integer tcsDefaultEquipmentTemplateId = sitewebPersistentService.getConfigAPI().getTCSDefaultEquipmentTemplateId(equipmentCategoryId);
        equipmemtTemplatIdMap.put(equipmentCategoryId,tcsDefaultEquipmentTemplateId);
        if(ObjectUtil.isEmpty(tcsDefaultEquipmentTemplateId)){
            EquipmentTemplate equipmentTemplate = new EquipmentTemplate();
            equipmentTemplate.setEquipmentTemplateName(equipmentTemplateName);
            equipmentTemplate.setEquipmentType(1);
            equipmentTemplate.setEquipmentCategory(equipmentCategoryId);
            equipmentTemplate.setDescription(TCS_EQUIPMENT_FLAG);
            equipmentTemplate.setProtocolCode(equipmentTemplateName);
            equipmentTemplate.setMemo(LocalDateTime.now().toString());
            equipmentTemplate.setParentTemplateId(0);
            EquipmentTemplate template = sitewebPersistentService.getConfigAPI().createForEquipmentTemplate(equipmentTemplate);
            equipmemtTemplatIdMap.put(equipmentCategoryId,template.getEquipmentTemplateId());
            return template.getEquipmentTemplateId();
        }else {
            return tcsDefaultEquipmentTemplateId;
        }
    }
}
