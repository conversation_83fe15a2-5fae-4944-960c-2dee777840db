package com.siteweb.tcs.north.s6.web.service.impl.cmcc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceType;
import com.siteweb.tcs.north.s6.dal.mapper.cmcc.CmccDeviceTypeMapper;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccDeviceTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 移动设备种类表 Service 实现类
 */
@Service
public class CmccDeviceTypeServiceImpl extends ServiceImpl<CmccDeviceTypeMapper, CmccDeviceType> implements ICmccDeviceTypeService {

    @Resource
    private CmccDeviceTypeMapper cmccDeviceTypeMapper;

    @Override
    public List<CmccDeviceType> listDistinctDeviceTypes() {
        return cmccDeviceTypeMapper.selectDistinctDeviceTypes();
    }

    @Override
    public List<CmccDeviceType> listSubTypesByDeviceTypeId(Integer deviceTypeId) {
        return cmccDeviceTypeMapper.selectSubTypesByDeviceTypeId(deviceTypeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAndBatchInsert(List<CmccDeviceType> deviceTypeList) {
        // 清空表数据
        cmccDeviceTypeMapper.delete(new QueryWrapper<>());

        // 批量插入新数据
        if (deviceTypeList != null && !deviceTypeList.isEmpty()) {
            return saveBatch(deviceTypeList);
        }
        return true;
    }

    @Override
    public boolean deleteByDeviceTypeAndDeviceSubType(String deviceTypeId, String deviceSubTypeId) {
        LambdaQueryWrapper<CmccDeviceType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmccDeviceType::getDeviceTypeId,deviceTypeId)
                .eq(CmccDeviceType::getDeviceSubTypeId,deviceSubTypeId);
        return baseMapper.delete(queryWrapper)>0;
    }
}

