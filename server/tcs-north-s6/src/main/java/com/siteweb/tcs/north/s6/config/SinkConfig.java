package com.siteweb.tcs.north.s6.config;

import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.north.s6.connector.process.sink.InfluxDBSink;
import com.siteweb.tcs.north.s6.connector.process.sink.RedisSink;
import com.siteweb.tcs.north.s6.dal.dto.SinkDTO;
import org.apache.pekko.actor.ActorRef;
import org.influxdb.InfluxDB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * @program: tcs2
 * @description: 持久化配置类
 * @author: xsx
 * @create: 2025-08-28 10:56
 **/
@Configuration
public class SinkConfig {


    @Autowired
    @Qualifier(value = "realTimeStringRedisTemplate")
    private StringRedisTemplate realTimeStringRedisTemplate;


    @Autowired
    @Qualifier(value = "SitewebRedisTemplate")
    private StringRedisTemplate sitewebRedisTemplate;

    @Autowired
    private InfluxDB influxDB;

    @Bean
    public SinkDTO sinkDTO() {
        SinkDTO sinkDTO = new SinkDTO();
        ActorRef realTimeRedisSink = ClusterContext.system().actorOf(RedisSink.props(realTimeStringRedisTemplate), "realTimeRedisSink");
        ActorRef sitewebRedisSink = ClusterContext.system().actorOf(RedisSink.props(sitewebRedisTemplate), "sitewebRedisSink");
//        ActorRef influxDBSink = ClusterContext.system().actorOf(InfluxDBSink.props(), "historyDataInfluxDBSink");
//        sinkDTO.setInfluxdbSink(influxDBSink);
        sinkDTO.setRealTimeDataRedisSink(realTimeRedisSink);
        sinkDTO.setRedisSink(sitewebRedisSink);
        return sinkDTO;
    }


}
