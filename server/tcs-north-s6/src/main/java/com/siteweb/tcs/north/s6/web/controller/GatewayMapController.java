package com.siteweb.tcs.north.s6.web.controller;

import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.util.ResponseUtil;
import com.siteweb.tcs.north.s6.web.service.IGatewayMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 网关映射控制器
 */
@Slf4j
@RestController
@RequestMapping(value = "/gateway-map")
public class GatewayMapController {

    @Autowired
    private IGatewayMapService gatewayMapService;

    /**
     * 根据网关ID查询网关映射
     * @param gatewayId 网关ID
     * @return 网关映射列表
     */
    @GetMapping(value = "/gateway/{gatewayId}")
    public ResponseEntity<Map<String, Object>> getByGatewayId(@PathVariable Long gatewayId) {
        try {
            GatewayMap gatewayMaps = gatewayMapService.getByGatewayId(gatewayId);
            return ResponseUtil.success(gatewayMaps);
        } catch (Exception e) {
            log.error("根据网关ID查询网关映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 根据北向监控单元ID查询网关映射
     * @param northMonitorUnitId 北向监控单元ID
     * @return 网关映射列表
     */
    @GetMapping(value = "/north-monitor-unit/{northMonitorUnitId}")
    public ResponseEntity<Map<String, Object>> getByNorthMonitorUnitId(@PathVariable Integer northMonitorUnitId) {
        try {
            List<GatewayMap> gatewayMaps = gatewayMapService.getByNorthMonitorUnitId(northMonitorUnitId);
            return ResponseUtil.success(gatewayMaps);
        } catch (Exception e) {
            log.error("根据北向监控单元ID查询网关映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 查询所有活跃的网关映射
     * @return 网关映射列表
     */
    @GetMapping(value = "/active")
    public ResponseEntity<Map<String, Object>> getAllActive() {
        try {
            List<GatewayMap> gatewayMaps = gatewayMapService.getAllActive();
            return ResponseUtil.success(gatewayMaps);
        } catch (Exception e) {
            log.error("查询所有活跃网关映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 创建网关映射
     * @param gatewayMap 网关映射
     * @return 创建结果
     */
    @PostMapping(value = "/create")
    public ResponseEntity<Map<String, Object>> create(@RequestBody GatewayMap gatewayMap) {
        try {
            // 设置默认值
            if (gatewayMap.getDeleted() == null) {
                gatewayMap.setDeleted(false);
            }
            
            boolean success = gatewayMapService.save(gatewayMap);
            if (success) {
                return ResponseUtil.success("创建成功");
            } else {
                return ResponseUtil.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建网关映射失败", e);
            return ResponseUtil.error("创建失败");
        }
    }

    /**
     * 批量创建网关映射
     * @param gatewayMaps 网关映射列表
     * @return 创建结果
     */
    @PostMapping(value = "/batch-create")
    public ResponseEntity<Map<String, Object>> batchCreate(@RequestBody List<GatewayMap> gatewayMaps) {
        try {
            // 设置默认值
            gatewayMaps.forEach(gatewayMap -> {
                if (gatewayMap.getDeleted() == null) {
                    gatewayMap.setDeleted(false);
                }
            });
            
            boolean success = gatewayMapService.saveBatch(gatewayMaps);
            if (success) {
                return ResponseUtil.success("批量创建成功");
            } else {
                return ResponseUtil.error("批量创建失败");
            }
        } catch (Exception e) {
            log.error("批量创建网关映射失败", e);
            return ResponseUtil.error("批量创建失败");
        }
    }

    /**
     * 更新网关映射
     * @param gatewayMap 网关映射
     * @return 更新结果
     */
    @PutMapping(value = "/update")
    public ResponseEntity<Map<String, Object>> update(@RequestBody GatewayMap gatewayMap) {
        try {
            boolean success = gatewayMapService.updateById(gatewayMap);
            if (success) {
                return ResponseUtil.success("更新成功");
            } else {
                return ResponseUtil.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新网关映射失败", e);
            return ResponseUtil.error("更新失败");
        }
    }

    /**
     * 删除网关映射
     * @param gatewayId 网关ID
     * @param northMonitorUnitId 北向监控单元ID
     * @return 删除结果
     */
    @DeleteMapping(value = "/delete")
    public ResponseEntity<Map<String, Object>> delete(
            @RequestParam Long gatewayId,
            @RequestParam Integer northMonitorUnitId) {
        try {
            boolean success = gatewayMapService.deleteByCompositeKey(gatewayId, northMonitorUnitId);
            if (success) {
                return ResponseUtil.success("删除成功");
            } else {
                return ResponseUtil.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除网关映射失败", e);
            return ResponseUtil.error("删除失败");
        }
    }

    /**
     * 查询所有网关映射
     * @return 网关映射列表
     */
    @GetMapping(value = "/list")
    public ResponseEntity<Map<String, Object>> list() {
        try {
            List<GatewayMap> gatewayMaps = gatewayMapService.list();
            return ResponseUtil.success(gatewayMaps);
        } catch (Exception e) {
            log.error("查询所有网关映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 创建或更新网关映射
     * @param gatewayMap 网关映射
     * @return 操作结果
     */
    @PostMapping(value = "/save-or-update")
    public ResponseEntity<Map<String, Object>> saveOrUpdate(@RequestBody GatewayMap gatewayMap) {
        try {
            // 设置默认值
            if (gatewayMap.getDeleted() == null) {
                gatewayMap.setDeleted(false);
            }
            
            boolean success = gatewayMapService.saveOrUpdate(gatewayMap);
            if (success) {
                return ResponseUtil.success("操作成功");
            } else {
                return ResponseUtil.error("操作失败");
            }
        } catch (Exception e) {
            log.error("创建或更新网关映射失败", e);
            return ResponseUtil.error("操作失败");
        }
    }
} 