package com.siteweb.tcs.north.s6.web.service.cmcc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccRoomMap;

import java.util.List;

/**
 * CMCC机房映射表 服务接口
 */
public interface ICmccRoomMapService extends IService<CmccRoomMap> {

    /**
     * 根据CMCC机房ID查询机房映射信息
     * @param cmccRoomId CMCC机房ID
     * @return 机房映射信息
     */
    CmccRoomMap getByCmccRoomId(String cmccRoomId);

    /**
     * 根据CMCC站点ID查询机房映射信息列表
     * @param cmccSiteId CMCC站点ID
     * @return 机房映射信息列表
     */
    List<CmccRoomMap> getByCmccSiteId(String cmccSiteId);

    /**
     * 根据SiteWeb局站ID查询机房映射信息列表
     * @param stationId SiteWeb局站ID
     * @return 机房映射信息列表
     */
    List<CmccRoomMap> getByStationId(Integer stationId);

    /**
     * 批量保存机房映射信息
     * @param roomMapList 机房映射信息列表
     * @return 是否成功
     */
    boolean batchSave(List<CmccRoomMap> roomMapList);

    /**
     * 清空并批量插入机房映射信息
     * @param roomMapList 机房映射信息列表
     * @return 是否成功
     */
    boolean clearAndBatchInsert(List<CmccRoomMap> roomMapList);

    CmccRoomMap getByCmccSiteIdAndRoomId(String siteId,String roomId);
}
