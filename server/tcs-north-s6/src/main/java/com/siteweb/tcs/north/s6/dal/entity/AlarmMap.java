package com.siteweb.tcs.north.s6.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 告警映射表实体类
 */
@Data
@TableName("tcs_alarm_map")
public class AlarmMap implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableField("DeviceId")
    private Long deviceId;

    /**
     * 北向设备ID
     */
    @TableField("NorthEquipmentId")
    private Integer northEquipmentId;

    /**
     * 告警ID
     */
    @TableField("AlarmId")
    private Long alarmId;

    /**
     * 北向事件ID
     */
    @TableField("NorthEventId")
    private Integer northEventId;

    /**
     * 北向事件条件ID
     */
    @TableField("NorthEventConditionId")
    private Integer northEventConditionId;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "false", delval = "true")
    @TableField("Deleted")
    private Boolean deleted;
} 