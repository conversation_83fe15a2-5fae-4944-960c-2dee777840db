package com.siteweb.tcs.north.s6.dal.entity;

import com.siteweb.tcs.hub.domain.v2.letter.GatewayChange;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


/**
 * 监控单元状态
 */
@Data
public class MonitorUnitState {
    private Integer monitorUnitId;

    private Integer state;

    private String heartBeatTime;

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static MonitorUnitState fromMonitorUnitChange(GatewayChange gatewayChange,Integer monitorUnitId){
        MonitorUnitState res = new MonitorUnitState();
        res.setState(0);
        res.setMonitorUnitId(monitorUnitId);
        String dateStr = gatewayChange.getTimeStamp().format(dateTimeFormatter);
        res.setHeartBeatTime(dateStr);
        return res;
    }

    public void setCurrentTimeStamp(){
        this.heartBeatTime = LocalDateTime.now().format(dateTimeFormatter);
    }

    public static String formatDateTime(LocalDateTime localDateTime){
        return localDateTime.format(dateTimeFormatter);
    }
}
