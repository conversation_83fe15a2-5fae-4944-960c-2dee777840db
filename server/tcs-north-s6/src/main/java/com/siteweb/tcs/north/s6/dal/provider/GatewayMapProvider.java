package com.siteweb.tcs.north.s6.dal.provider;

import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.web.service.IGatewayMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * @program: tcs2
 * @description: 网关映射业务提供类
 * @author: xsx
 * @create: 2025-08-28 11:26
 **/
@Component
public class GatewayMapProvider {

    @Autowired
    private IGatewayMapService gatewayMapService;

    public GatewayMap getGatewayMapByGatewayGuid(Long gatewayGuid){
        List<GatewayMap> gatewayMapList = gatewayMapService.getGatewayMapWithFullRelationsByGatewayId(gatewayGuid);
        GatewayMap gatewayMap = Optional.ofNullable(gatewayMapList).get().get(0);
        return gatewayMap;
    }
}
