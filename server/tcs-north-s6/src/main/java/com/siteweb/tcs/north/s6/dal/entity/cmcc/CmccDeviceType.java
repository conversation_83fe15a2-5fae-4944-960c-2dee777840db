package com.siteweb.tcs.north.s6.dal.entity.cmcc;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 移动设备种类表
 */
@Data
@TableName("cmcc_device_type")
public class CmccDeviceType implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("DeviceTypeID")
    private String deviceTypeId;

    @TableField("DeviceTypeName")
    private String deviceTypeName;

    @TableField("DeviceSubTypeID")
    private String deviceSubTypeId;

    @TableField("DeviceSubTypeName")
    private String deviceSubTypeName;
}

