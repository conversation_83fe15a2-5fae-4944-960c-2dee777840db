package com.siteweb.tcs.north.s6.dal.entity;

import com.siteweb.tcs.hub.domain.letter.LiveMonitorUnitState;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MonitorUnitChange {
    private Integer MonitorUnitId;
    private String IP;
    private String port;
    private Integer connectState;
    private String firmWareVersion;
    private String softWareVersion;
    private String vendor;
    private String modal;
    private LocalDateTime timeStamp;
    //是否发生变化
    private boolean isChange;

    private static String redisKeyTemplate = "MuState:%s";

    public LocalDateTime getTimestamp() {
        return timeStamp;
    }


    public LiveMonitorUnitState getLiveMonitorUnitState() {
        LiveMonitorUnitState liveMonitorUnitState = new LiveMonitorUnitState();
        liveMonitorUnitState.setConnectState(connectState);
        liveMonitorUnitState.setFirmWareVersion(firmWareVersion);
        liveMonitorUnitState.setIp(IP);
        liveMonitorUnitState.setModal(modal);
        liveMonitorUnitState.setMonitorUnitID(MonitorUnitId);
        liveMonitorUnitState.setPort(port);
        liveMonitorUnitState.setSoftWareVersion(softWareVersion);
        liveMonitorUnitState.setTimeStamp(timeStamp);
        liveMonitorUnitState.setVendor(vendor);
        return liveMonitorUnitState;
    }

    public String getRedisKey(){
        return String.format(redisKeyTemplate,MonitorUnitId);
    }

    public static String getRedisKey(Integer monitorUnitId){
        return String.format(redisKeyTemplate,monitorUnitId);
    }
}
