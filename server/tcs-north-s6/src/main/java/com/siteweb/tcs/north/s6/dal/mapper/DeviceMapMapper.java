package com.siteweb.tcs.north.s6.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 设备映射表Mapper接口
 */
@Mapper
@Repository
public interface DeviceMapMapper extends BaseMapper<DeviceMap> {

    /**
     * 根据网关ID查询设备映射
     * @param gatewayId 网关ID
     * @return 设备映射列表
     */
    List<DeviceMap> selectByGatewayId(@Param("gatewayId") Long gatewayId);

    /**
     * 根据北向监控单元ID查询设备映射
     * @param northMonitorUnitId 北向监控单元ID
     * @return 设备映射列表
     */
    List<DeviceMap> selectByNorthMonitorUnitId(@Param("northMonitorUnitId") Integer northMonitorUnitId);

    /**
     * 根据北向设备ID查询设备映射
     * @param northEquipmentId 北向设备ID
     * @return 设备映射列表
     */
    List<DeviceMap> selectByNorthEquipmentId(@Param("northEquipmentId") Integer northEquipmentId);

    /**
     * 批量插入设备映射
     * @param deviceMaps 设备映射列表
     * @return 插入成功数量
     */
    int insertBatch(@Param("list") List<DeviceMap> deviceMaps);

    /**
     * 根据复合主键删除设备映射
     * @param gatewayId 网关ID
     * @param deviceId 设备ID
     * @param northEquipmentId 北向设备ID
     * @return 删除成功数量
     */
    int deleteByCompositeKey(@Param("gatewayId") Long gatewayId,
                            @Param("deviceId") Long deviceId,
                            @Param("northEquipmentId") Integer northEquipmentId);
} 