package com.siteweb.tcs.north.s6.web.controller;

import com.siteweb.tcs.north.s6.dal.entity.AlarmMap;
import com.siteweb.tcs.north.s6.util.ResponseUtil;
import com.siteweb.tcs.north.s6.web.service.IAlarmMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 告警映射控制器
 */
@Slf4j
@RestController
@RequestMapping(value = "/alarm-map")
public class AlarmMapController {

    @Autowired
    private IAlarmMapService alarmMapService;

    /**
     * 根据设备ID查询告警映射
     * @param deviceId 设备ID
     * @return 告警映射列表
     */
    @GetMapping(value = "/device/{deviceId}")
    public ResponseEntity<Map<String, Object>> getByDeviceId(@PathVariable Integer deviceId) {
        try {
            List<AlarmMap> alarmMaps = alarmMapService.getByDeviceId(deviceId);
            return ResponseUtil.success(alarmMaps);
        } catch (Exception e) {
            log.error("根据设备ID查询告警映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 根据北向设备ID查询告警映射
     * @param northEquipmentId 北向设备ID
     * @return 告警映射列表
     */
    @GetMapping(value = "/north-equipment/{northEquipmentId}")
    public ResponseEntity<Map<String, Object>> getByNorthEquipmentId(@PathVariable Integer northEquipmentId) {
        try {
            List<AlarmMap> alarmMaps = alarmMapService.getByNorthEquipmentId(northEquipmentId);
            return ResponseUtil.success(alarmMaps);
        } catch (Exception e) {
            log.error("根据北向设备ID查询告警映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 根据告警ID查询告警映射
     * @param alarmId 告警ID
     * @return 告警映射列表
     */
    @GetMapping(value = "/alarm/{alarmId}")
    public ResponseEntity<Map<String, Object>> getByAlarmId(@PathVariable Long alarmId) {
        try {
            AlarmMap alarmMaps = alarmMapService.getByAlarmId(alarmId);
            return ResponseUtil.success(alarmMaps);
        } catch (Exception e) {
            log.error("根据告警ID查询告警映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 根据北向事件ID查询告警映射
     * @param northEventId 北向事件ID
     * @return 告警映射列表
     */
    @GetMapping(value = "/north-event/{northEventId}")
    public ResponseEntity<Map<String, Object>> getByNorthEventId(@PathVariable Integer northEventId) {
        try {
            List<AlarmMap> alarmMaps = alarmMapService.getByNorthEventId(northEventId);
            return ResponseUtil.success(alarmMaps);
        } catch (Exception e) {
            log.error("根据北向事件ID查询告警映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 创建告警映射
     * @param alarmMap 告警映射
     * @return 创建结果
     */
    @PostMapping(value = "/create")
    public ResponseEntity<Map<String, Object>> create(@RequestBody AlarmMap alarmMap) {
        try {
            boolean success = alarmMapService.save(alarmMap);
            if (success) {
                return ResponseUtil.success("创建成功");
            } else {
                return ResponseUtil.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建告警映射失败", e);
            return ResponseUtil.error("创建失败");
        }
    }

    /**
     * 更新告警映射
     * @param alarmMap 告警映射
     * @return 更新结果
     */
    @PutMapping(value = "/update")
    public ResponseEntity<Map<String, Object>> update(@RequestBody AlarmMap alarmMap) {
        try {
            boolean success = alarmMapService.updateById(alarmMap);
            if (success) {
                return ResponseUtil.success("更新成功");
            } else {
                return ResponseUtil.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新告警映射失败", e);
            return ResponseUtil.error("更新失败");
        }
    }

    /**
     * 删除告警映射
     * @param deviceId 设备ID
     * @param northEquipmentId 北向设备ID
     * @param alarmId 告警ID
     * @param northEventId 北向事件ID
     * @param northEventConditionId 北向事件条件ID
     * @return 删除结果
     */
    @DeleteMapping(value = "/delete")
    public ResponseEntity<Map<String, Object>> delete(
            @RequestParam Integer deviceId,
            @RequestParam Integer northEquipmentId,
            @RequestParam Long alarmId,
            @RequestParam Integer northEventId,
            @RequestParam Integer northEventConditionId) {
        try {
            boolean success = alarmMapService.deleteByCompositeKey(deviceId, northEquipmentId, 
                                                                  alarmId, northEventId, northEventConditionId);
            if (success) {
                return ResponseUtil.success("删除成功");
            } else {
                return ResponseUtil.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除告警映射失败", e);
            return ResponseUtil.error("删除失败");
        }
    }

    /**
     * 查询所有告警映射
     * @return 告警映射列表
     */
    @GetMapping(value = "/list")
    public ResponseEntity<Map<String, Object>> list() {
        try {
            List<AlarmMap> alarmMaps = alarmMapService.list();
            return ResponseUtil.success(alarmMaps);
        } catch (Exception e) {
            log.error("查询所有告警映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }
} 