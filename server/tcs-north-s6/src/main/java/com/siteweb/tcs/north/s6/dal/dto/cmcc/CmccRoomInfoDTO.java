package com.siteweb.tcs.north.s6.dal.dto.cmcc;

import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccRoomMap;
import lombok.Data;

/**
 * @program: tcs2
 * @description: 移动机房数据传输类
 * @author: xsx
 * @create: 2025-08-20 10:16
 **/
@Data
public class CmccRoomInfoDTO {
    private String siteId;
    private String roomId;
    private String roomName;
    private Integer roomType;

    private static final String uniqueKey = "%s.%s";

    public String getUniqueKey(){
        return String.format(uniqueKey,siteId,roomId);
    }

    public CmccRoomMap toCmccRoomMap(){
        CmccRoomMap cmccRoomMap = new CmccRoomMap();
        cmccRoomMap.setCmccSiteId(siteId);
        cmccRoomMap.setCmccRoomId(roomId);
        cmccRoomMap.setCmccRoomName(roomName);
        cmccRoomMap.setCmccRoomType(roomType);
        return cmccRoomMap;
    }
}
