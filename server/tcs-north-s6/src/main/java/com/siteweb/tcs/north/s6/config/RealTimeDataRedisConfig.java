package com.siteweb.tcs.north.s6.config;

import cn.hutool.core.text.CharSequenceUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * @ClassName: RealTimeDataRedisConfig
 * @descriptions: 实时数据redis配置类
 * @author: xsx
 * @date: 2024/8/2 9:26
 **/
@Configuration
public class RealTimeDataRedisConfig {
    @Bean(name = "realTimeStringRedisTemplate")
    public StringRedisTemplate stringRedisTemplate(RealTimeDataRedisProperties realTimeDataRedisProperties, RedisConnectionFactory redisConnectionFactory) {
        //设置自定义的redis实列配置
        RedisStandaloneConfiguration standaloneConfiguration = getSiteWebRedisConfiguration(realTimeDataRedisProperties);
        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(standaloneConfiguration);
        connectionFactory.afterPropertiesSet();
        return new StringRedisTemplate(connectionFactory);
    }

    /**
     * 获取S6业务的redis实列配置
     *
     * @param realTimeDataRedisProperties s6业务redis的配置信息
     * @return {@link RedisStandaloneConfiguration}
     */
    private RedisStandaloneConfiguration getSiteWebRedisConfiguration(RealTimeDataRedisProperties realTimeDataRedisProperties) {
        RedisStandaloneConfiguration standaloneConfiguration = new RedisStandaloneConfiguration();
        standaloneConfiguration.setDatabase(realTimeDataRedisProperties.getDatabase());
        standaloneConfiguration.setHostName(realTimeDataRedisProperties.getHost());
        standaloneConfiguration.setPort(realTimeDataRedisProperties.getPort());
        if (CharSequenceUtil.isNotEmpty(realTimeDataRedisProperties.getPassword())) {
            RedisPassword redisPassword = RedisPassword.of(realTimeDataRedisProperties.getPassword());
            standaloneConfiguration.setPassword(redisPassword);
        }
        return standaloneConfiguration;
    }
}
