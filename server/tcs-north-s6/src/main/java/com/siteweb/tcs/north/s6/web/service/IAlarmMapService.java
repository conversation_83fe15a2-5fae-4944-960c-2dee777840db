package com.siteweb.tcs.north.s6.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.AlarmMap;

import java.util.List;

/**
 * 告警映射服务接口
 */
public interface IAlarmMapService extends IService<AlarmMap> {

    /**
     * 根据设备ID查询告警映射
     * @param deviceId 设备ID
     * @return 告警映射列表
     */
    List<AlarmMap> getByDeviceId(Integer deviceId);

    /**
     * 根据北向设备ID查询告警映射
     * @param northEquipmentId 北向设备ID
     * @return 告警映射列表
     */
    List<AlarmMap> getByNorthEquipmentId(Integer northEquipmentId);

    /**
     * 根据告警ID查询告警映射
     * @param alarmId 告警ID
     * @return 告警映射列表
     */
    AlarmMap getByAlarmId(Long alarmId);

    /**
     * 根据北向事件ID查询告警映射
     * @param northEventId 北向事件ID
     * @return 告警映射列表
     */
    List<AlarmMap> getByNorthEventId(Integer northEventId);

    /**
     * 根据复合主键删除告警映射
     * @param deviceId 设备ID
     * @param northEquipmentId 北向设备ID
     * @param alarmId 告警ID
     * @param northEventId 北向事件ID
     * @param northEventConditionId 北向事件条件ID
     * @return 是否删除成功
     */
    boolean deleteByCompositeKey(Integer deviceId, Integer northEquipmentId, Long alarmId, 
                                 Integer northEventId, Integer northEventConditionId);

    boolean deleteByDeviceId(Long deviceId);

    boolean deleteByAlarmId(Long alarmId);
}