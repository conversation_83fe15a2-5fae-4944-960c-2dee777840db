package com.siteweb.tcs.north.s6.connector.letter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class HistoryStatisticsItem {


    private Integer stationId;

    private String equipmentId;
    /**
     * 信号的唯一标识符
     */
    private String signalId;
    /**
     * 最大值
     */
    private Double maxVal;
    /**
     * 最大值的时间
     */
    private LocalDateTime maxValTime;
    /**
     * 最小值
     */
    private Double minVal;
    /**
     * 最小值的时间
     */
    private LocalDateTime minValTime;
    /**
     * 数据统计开始时间
     */
    private LocalDateTime periodStart;
    /**
     * 数据统计结束时间
     */
    private LocalDateTime periodEnd;
}
