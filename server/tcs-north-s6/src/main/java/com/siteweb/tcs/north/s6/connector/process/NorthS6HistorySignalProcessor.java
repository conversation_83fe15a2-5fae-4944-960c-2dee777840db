package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceHistorySignalChange;
import com.siteweb.tcs.hub.util.TCSTopicUtil;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description: 北向S6历史数据处理器
 * @author: xsx
 * @create: 2025-08-28 10:10
 **/

public class NorthS6HistorySignalProcessor extends ProbeActor {

    private final ActorRef mediator;

    private ActorRef influxdbSink;

    public static Props props(GatewayMap gatewayMap, ActorRef realTimeRedisSink){
        return Props.create(NorthS6HistorySignalProcessor.class,gatewayMap,realTimeRedisSink);
    }


    private NorthS6HistorySignalProcessor(GatewayMap gatewayMap,ActorRef influxdbSink){
        this.mediator = DistributedPubSub.get(getContext().system()).mediator();
        this.influxdbSink = influxdbSink;
        handlerSubscribe(gatewayMap.getGatewayId());
        handleInit(gatewayMap);
    }

    private void handlerSubscribe(Long gatewayGuid) {
        String topicName = TCSTopicUtil.getTopicName(SubscriptionTypeEnum.HISTORY_DATA, gatewayGuid);
        mediator.tell(new DistributedPubSubMediator.Subscribe(topicName, getSelf()), getSelf());
    }

    private void handleInit(GatewayMap gatewayMap) {
        //初始化数据
    }

    @Override
    public Receive createReceive() {
        Receive receive = ReceiveBuilder.create().match(DeviceHistorySignalChange.class, this::onDeviceHistorySignalChange).build();
        return super.createReceive().orElse(receive);
    }

    private void onDeviceHistorySignalChange(DeviceHistorySignalChange historySignalChange) {

    }
}