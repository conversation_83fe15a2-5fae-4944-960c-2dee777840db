package com.siteweb.tcs.north.s6.dal.entity.cmcc;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.siteweb.tcs.siteweb.dto.StationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC站点映射表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("cmcc_site_map")
public class CmccSiteMap {

    /**
     * cmcc站点id
     */
    @TableId("CmccSiteID")
    private String cmccSiteId;

    /**
     * cmcc站点名称
     */
    @TableField("CmccSiteName")
    private String cmccSiteName;

    /**
     * cmcc站点类型（对应局站类型）
     */
    @TableField("CmccSiteType")
    private Integer cmccSiteType;

    /**
     * siteweb局站id
     */
    @TableField("StationID")
    private Integer stationId;

    /**
     * siteweb局站类型
     */
    @TableField("StationCategoryID")
    private Integer stationCategoryId;

    public StationDTO toStationDTO() {
        StationDTO stationDTO = new StationDTO();
        stationDTO.setStationId(stationId);
        stationDTO.setStationName(cmccSiteName);
        stationDTO.setStationCategory(stationCategoryId);
        return stationDTO;
    }
}
