package com.siteweb.tcs.north.s6;

import com.siteweb.tcs.common.runtime.NorthPlugin;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.north.s6.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.s6.connector.process.GatewayCreatePipelineSubscriber;
import com.siteweb.tcs.north.s6.dal.dto.SinkDTO;
import com.siteweb.tcs.siteweb.runner.DevelopModeRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.env.Environment;

/**
 * siteweb6北向接口插件主类
 * <p>
 * 插件的入口类，负责插件的生命周期管理
 * </p>
 */
@Slf4j
public class NorthS6Plugin extends NorthPlugin {


    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;
//
//    @Autowired
//    private DevelopModeRunner developModeRunner;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private GatewayLauncher gatewayLauncher;

    @Autowired
    private Environment environment;


    public NorthS6Plugin(PluginContext context) {
        super(context);
    }

    @Override
    public void onStart() {
        try {
            log.info("Starting NorthS6Plugin - siteweb6北向接口");
            ConnectorDataHolder.getInstance().setPluginContext(getApplicationContext());
            // 设置插件ID和创建根Actor
            ConnectorDataHolder.getInstance().setPluginId(this.getPluginId());
            // 从数据库加载网关列表并且tell到分片器
            // 初始化其他组件
            log.info("NorthS6Plugin started successfully - siteweb6北向接口启动成功");
//            developModeRunner.register( e ->{
//                // 创建网关创建管道订阅者
//                log.info("Creating GatewayCreatePipelineSubscriber...");
//                ClusterContext.getActorSystem().actorOf(GatewayCreatePipelineSubscriber.props(), "gatewayCreatePipelineSubscriber");
//                log.info("GatewayCreatePipelineSubscriber created successfully");
//
//            });
            String tcsWsId = getTcsWsId();
            ConnectorDataHolder.getInstance().setTcsWsId(tcsWsId);
            gatewayLauncher.loadGateway();
        } catch (Exception e) {
            log.error("Error starting NorthS6Plugin - siteweb6北向接口启动失败", e);
        }
    }

    public String getTcsWsId() {
        String wsIdKey = "siteweb.tcs-heartbeat.ws-id";
        String wsId = environment.getProperty(wsIdKey);
        if(StringUtils.isBlank(wsId)) {
            log.warn("CONFIG[\"" + wsIdKey + "\"] is blank.");
            return null;
        } else {
            return wsId.trim();
        }
    }

    @Override
    public void onStop() {
        log.info("Stopping NorthS6Plugin - siteweb6北向接口停止");
        // Actor系统会处理停止Actor
    }
}