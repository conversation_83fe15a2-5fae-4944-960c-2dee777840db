package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccRoomMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;
import com.siteweb.tcs.north.s6.web.service.impl.SiteWebDefaultProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Map;

/**
 * CMCC处理器基类
 * 提供公共的依赖注入和工具方法
 */
public abstract class CmccBaseHandler {

    @Autowired
    protected CmccConfigParser cmccConfigParser;

    @Autowired
    protected SiteWebDefaultProvider siteWebDefaultProvider;

    @Autowired
    @Qualifier(value = "s6SitewebPersistentService")
    protected SitewebPersistentService sitewebPersistentService;

    /**
     * 端口名称模板
     */
    protected static final String PORT_NAME_TEMPLATE = "com%s";

    /**
     * 获取站点映射，如果不存在则返回null
     */
    protected CmccSiteMap getSiteMap(String siteId, Map<String, CmccSiteMap> cmccSiteMapMap) {
        return cmccSiteMapMap.get(siteId);
    }

    /**
     * 获取机房映射，如果不存在则返回null
     */
    protected CmccRoomMap getRoomMap(String roomKey, Map<String, CmccRoomMap> cmccRoomMapMap) {
        return cmccRoomMapMap.get(roomKey);
    }
}
