package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.north.s6.dal.dto.SinkDTO;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.dal.provider.GatewayMapProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;

/**
 * 北向S6代理Actor，处理生命周期事件
 * <p>
 * 负责处理北向接口的消息和状态
 * </p>
 */
@Slf4j
public class NorthS6GatewayProxy extends AbstractActor {

    private Long gatewayGuid;

    private SinkDTO sinkDTO;

    private GatewayMap gatewayMap;

    private final GatewayMapProvider gatewayMapProvider;

    private NorthS6GatewayProxy(Long gatewayGuid, SinkDTO sinkDTO){
        log.info("");
        this.gatewayGuid = gatewayGuid;
        this.sinkDTO = sinkDTO;
        //订阅通道
        // 初始化分布式发布/订阅中介器
        gatewayMapProvider = PluginScope.<GatewayMapProvider>getBean(GatewayMapProvider.class);
        init(gatewayGuid);
        log.info("S6 Gateway: {} Entity is Constructor, path={}", getSelf().path().name(), getSelf().path());
    }

    private void init(Long gatewayGuid){
        gatewayMap = gatewayMapProvider.getGatewayMapByGatewayGuid(gatewayGuid);
        getContext().actorOf(NorthS6GatewayChangeProcessor.props(gatewayMap,sinkDTO.getRedisSink()));
        getContext().actorOf(NorthS6DeviceChangeProcessor.props(gatewayMap,sinkDTO.getRedisSink()));
        getContext().actorOf(NorthS6SignalChangeProcessor.props(gatewayMap,sinkDTO.getRealTimeDataRedisSink()));
//        getContext().actorOf(NorthS6HistorySignalProcessor.props(gatewayMap,sinkDTO.getInfluxdbSink()));
        getContext().actorOf(NorthS6AlarmChangeProcessor.props(gatewayMap));
        getContext().actorOf(NorthS6ControlResponseProcessor.props(gatewayMap));
        getContext().actorOf(NorthS6ControlRequestProcessor.props(gatewayMap));
        getContext().actorOf(NorthS6GatewayConfigChangeProcessor.props(gatewayMap));
    }

    /**
     * Returns the props for creating a {@link NorthS6GatewayProxy} Actor.
     *
     * @return a Props for creating a NorthS6GatewayProxy Actor
     */
    public static Props props(Long gatewayGuid,SinkDTO sinkDTO) {
        return Props.create(NorthS6GatewayProxy.class,gatewayGuid,sinkDTO);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .matchAny(message -> {
                    log.debug("NorthS6GatewayProxy received message: {}", message);
                    // 处理各种消息
                })
                .build();
    }
} 