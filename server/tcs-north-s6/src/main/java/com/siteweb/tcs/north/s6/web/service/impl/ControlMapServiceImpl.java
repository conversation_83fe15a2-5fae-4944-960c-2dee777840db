package com.siteweb.tcs.north.s6.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.ControlMap;
import com.siteweb.tcs.north.s6.dal.entity.SignalMap;
import com.siteweb.tcs.north.s6.dal.mapper.ControlMapMapper;
import com.siteweb.tcs.north.s6.web.service.IControlMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 控制映射服务实现类
 */
@Service
public class ControlMapServiceImpl extends ServiceImpl<ControlMapMapper, ControlMap> implements IControlMapService {

    @Resource
    private ControlMapMapper controlMapMapper;

    @Override
    public List<ControlMap> getByDeviceId(Long deviceId) {
        return controlMapMapper.selectByDeviceId(deviceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<ControlMap> controlMaps) {
        if (controlMaps == null || controlMaps.isEmpty()) {
            return false;
        }
        return controlMapMapper.insertBatch(controlMaps) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCompositeKey(Long deviceId, Long controlId, Integer northControlId) {
        return controlMapMapper.deleteByCompositeKey(deviceId, controlId, northControlId) > 0;
    }

    @Override
    public boolean deleteByDeviceId(Long deviceId) {
        LambdaQueryWrapper<ControlMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ControlMap::getDeviceId,deviceId);
        return controlMapMapper.delete(queryWrapper)>0;
    }

    @Override
    public ControlMap getByControlId(Long controlId) {
        LambdaQueryWrapper<ControlMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ControlMap::getControlId,controlId);
        return controlMapMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean deleteByControlId(Long controlId) {
        LambdaQueryWrapper<ControlMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ControlMap::getControlId,controlId);
        return controlMapMapper.delete(queryWrapper)>0;
    }
} 