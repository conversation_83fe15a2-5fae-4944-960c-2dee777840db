package com.siteweb.tcs.north.s6.dal.entity.cmcc;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC机房映射表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("cmcc_room_map")
public class CmccRoomMap {

    /**
     * CMCC站点id
     */
    @TableField("CmccSiteID")
    private String cmccSiteId;

    /**
     * CMCC机房id
     */
    @TableId("CmccRoomID")
    private String cmccRoomId;

    /**
     * CMCC机房名称
     */
    @TableField("CmccRoomName")
    private String cmccRoomName;

    /**
     * CMCC机房类型
     */
    @TableField("CmccRoomType")
    private Integer cmccRoomType;

    /**
     * SiteWeb局站id
     */
    @TableField("StationID")
    private Integer stationId;

    /**
     * SiteWeb机房id
     */
    @TableField("HouseID")
    private Integer houseId;
}
