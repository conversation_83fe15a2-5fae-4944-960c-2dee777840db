package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.hub.dal.dto.*;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccRoomInfoDTO;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccSiteInfoDTO;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceType;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.dto.EventConditionDTO;
import com.siteweb.tcs.siteweb.entity.ControlMeanings;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @program: tcs2
 * @description: 移动配置解析器
 * @author: xsx
 * @create: 2025-08-20 10:08
 **/
@Component
public class CmccConfigParser {

    public CmccSiteInfoDTO parseSiteInfo(GatewayConfigChangeDto configChangeDto) {
        CmccSiteInfoDTO siteInfo = getSiteInfo(configChangeDto.getMetadata());
        return siteInfo;
    }

    public CmccSiteInfoDTO parseSiteInfo(DeviceConfigChangeDto configChangeDto) {
        CmccSiteInfoDTO siteInfo = getSiteInfo(configChangeDto.getMetadata());
        return siteInfo;
    }

    public CmccRoomInfoDTO parseRoomInfo(DeviceConfigChangeDto configChangeDto){
        JsonNode metadata = configChangeDto.getMetadata();
//        String roomId = metadata.get("roomId").asText();
//        String roomName = metadata.get("roomName").asText();
        String roomId = "030100122";
        String roomName = "A03一楼量子实验室动力间";
//        int roomType = metadata.get("roomType").asInt();
        int roomType = 1;
//        String siteId = metadata.get("SiteId").asText();
        String siteId = "3205051000101";
        CmccRoomInfoDTO roomInfo = new CmccRoomInfoDTO();
        roomInfo.setRoomId(roomId);
        roomInfo.setRoomName(roomName);
        roomInfo.setRoomType(roomType);
        roomInfo.setSiteId(siteId);
        return roomInfo;
    }

    public CreateMonitorUnitDTO parseFSUInfo(GatewayConfigChangeDto gatewayConfigChangeDto){
        CreateMonitorUnitDTO createMonitorUnitDTO = new CreateMonitorUnitDTO();
        createMonitorUnitDTO.setFsu(true);
        createMonitorUnitDTO.setMonitorUnitCategory(10);
        createMonitorUnitDTO.setIpAddress(gatewayConfigChangeDto.getSouthAddress());
        createMonitorUnitDTO.setMonitorUnitName(gatewayConfigChangeDto.getSouthGatewayName());
        return createMonitorUnitDTO;
    }

    public CreateEquipmentDto parseDeviceInfo(DeviceConfigChangeDto configChangeDto){
        CreateEquipmentDto createEquipmentDto = new CreateEquipmentDto();
        createEquipmentDto.setEquipmentName(configChangeDto.getSouthDeviceName());
        return createEquipmentDto;
    }
//
    public CmccDeviceType parseDeviceType(DeviceConfigChangeDto configChangeDto){
        JsonNode metadata = configChangeDto.getMetadata();
        String deviceType = metadata.get("deviceType").asText().replaceFirst("^0+(?!$)", "");
        String deviceSubType = metadata.get("deviceSubType").asText().replaceFirst("^0+(?!$)", "");
        CmccDeviceType cmccDeviceType = new CmccDeviceType();
        cmccDeviceType.setDeviceTypeId(deviceType);
        cmccDeviceType.setDeviceSubTypeId(deviceSubType);
        return cmccDeviceType;
    }
//
//    public CmccDeviceExt parseDeviceExt(DeviceConfigChangeDto configChangeDto){
//
//    }
//
    public SignalConfigItem parseSignalConfigItem(SignalConfigChangeDto signalConfigChangeDto, DeviceMap deviceMap){
        SignalConfigItem signalConfigItem = new SignalConfigItem();
        signalConfigItem.setSignalName(signalConfigChangeDto.getSouthSignalName());
        signalConfigItem.setUnit(signalConfigChangeDto.getSouthSignalUnit());
        signalConfigItem.setEquipmentTemplateId(deviceMap.getNorthEquipmentTemplateId());
        List<SignalMeanings> signalMeaningsList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(signalConfigChangeDto.getSouthSignalMeanings())){
            signalConfigChangeDto.getSouthSignalMeanings().forEach((k,v) ->{
                SignalMeanings signalMeanings = new SignalMeanings();
                signalMeanings.setStateValue(k);
                signalMeanings.setMeanings(v);
                signalMeanings.setEquipmentTemplateId(deviceMap.getNorthEquipmentTemplateId());
                signalMeaningsList.add(signalMeanings);
            });
        }
        signalConfigItem.setSignalMeaningsList(signalMeaningsList);
        signalConfigItem.setSignalType(1);
        signalConfigItem.setEnable(true);
        signalConfigItem.setVisible(true);
        signalConfigItem.setChannelNo(-6);
        signalConfigItem.setSignalCategory(signalConfigChangeDto.getSignalType());
        signalConfigItem.setDataType(0);//todo
        signalConfigItem.setChannelType(signalConfigChangeDto.getSignalType());
        signalConfigItem.setDisplayIndex(1);
        return signalConfigItem;
    }

    public EventConfigItem parseAlarmConfigItem(AlarmConfigChangeDto alarmConfigChangeDto, DeviceMap deviceMap){

        JsonNode metadata = alarmConfigChangeDto.getMetadata();
        String alarmLevel = metadata.get("alarmLevel").asText();
        Integer eventLevel;
        switch (alarmLevel){
            case "CRITICAL": //一级
                eventLevel = 3;
                break;
            case "MAJOR": //二级
                eventLevel = 2;
                break;
            case "MINOR": // 三级
                eventLevel = 1;
                break;
            case "HINT": //四级
            default:
                eventLevel = 0;
                break;
        }
        // 告警条件
        EventConditionDTO eventConditionDTO = new EventConditionDTO();
        eventConditionDTO.setEventSeverity(eventLevel);
        eventConditionDTO.setMeanings(alarmConfigChangeDto.getSouthAlarmMeaning());
        eventConditionDTO.setEquipmentTemplateId(deviceMap.getNorthEquipmentTemplateId());
        eventConditionDTO.setStartCompareValue(0.0);
        eventConditionDTO.setStartDelay(0);
        eventConditionDTO.setEventConditionId(-1);
        eventConditionDTO.setStartOperation("=");

        // 告警本身
        EventConfigItem eventConfigItem = new EventConfigItem();
        eventConfigItem.setEquipmentTemplateId(deviceMap.getNorthEquipmentTemplateId());
        eventConfigItem.setEventName(alarmConfigChangeDto.getSouthAlarmName());
        eventConfigItem.setEnable(true);
        eventConfigItem.setEventConditionList(Collections.singletonList(eventConditionDTO));
        // 条件事件
        eventConfigItem.setStartType(1);
        // 持续事件
        eventConfigItem.setEndType(3);
        eventConfigItem.setEnable(true);
        eventConfigItem.setVisible(true);
        // 设备事件
        eventConfigItem.setEventCategory(2);
        eventConfigItem.setDisplayIndex(1);

        return eventConfigItem;
    }

    public ControlConfigItem parseControlConfigItem(ControlConfigChangeDto controlConfigChangeDto, DeviceMap deviceMap){
        ControlConfigItem controlConfigItem = new ControlConfigItem();
        controlConfigItem.setControlName(controlConfigChangeDto.getSouthControlName());
        controlConfigItem.setEquipmentTemplateId(deviceMap.getNorthEquipmentTemplateId());
        controlConfigItem.setVisible(true);
        controlConfigItem.setDisplayIndex(1);
        controlConfigItem.setCmdToken(" ");
        controlConfigItem.setControlSeverity(1);
        controlConfigItem.setEnable(true);
        controlConfigItem.setControlType((short)1);
        controlConfigItem.setModuleNo(0);
        // 字符串
        controlConfigItem.setDataType((short) 1);
        // 普通控制
        controlConfigItem.setCommandType(controlConfigChangeDto.getControlType());
        controlConfigItem.setControlCategory(1);
        if (ObjectUtil.isEmpty(controlConfigChangeDto.getMaxValue())) {
            controlConfigItem.setMaxValue(99.0);
        } else {
            controlConfigItem.setMaxValue(controlConfigChangeDto.getMaxValue());
        }
        if (ObjectUtil.isEmpty(controlConfigChangeDto.getMinValue())) {
            controlConfigItem.setMinValue(0.0);
        } else {
            controlConfigItem.setMinValue(controlConfigChangeDto.getMinValue());
        }

        //控制含义
        if(CollectionUtil.isNotEmpty(controlConfigChangeDto.getControlMeanings())){
            List<ControlMeanings> controlMeaningsList = new ArrayList<>();
            controlConfigChangeDto.getControlMeanings().forEach((k,v)->{
                ControlMeanings controlMeanings = new ControlMeanings();
                controlMeanings.setEquipmentTemplateId(deviceMap.getNorthEquipmentTemplateId());
                controlMeanings.setParameterValue(k);
                controlMeanings.setMeanings(v);
                controlMeaningsList.add(controlMeanings);
            });
            controlConfigItem.setControlMeaningsList(controlMeaningsList);
        }
        return controlConfigItem;
    }

    private CmccSiteInfoDTO getSiteInfo(JsonNode metadata) {
//        String siteId = metadata.get("siteId").asText();
        String siteId = "3205051000101";
//        String siteName = metadata.get("siteName").asText();
        String siteName = "江苏_苏州_虎丘区_苏州虎丘区苏研A03楼一层量子实验室机房";
//        Integer siteType = metadata.get("siteType").asInt();
        Integer siteType = 4;
//        String siteTypeName = metadata.get("siteTypeName").asText();
        String siteTypeName = "数据中心";
        CmccSiteInfoDTO siteInfo = new CmccSiteInfoDTO();
        siteInfo.setSiteId(siteId);
        siteInfo.setSiteName(siteName);
        siteInfo.setSiteType(siteType);
        siteInfo.setSiteTypeName(siteTypeName);
        return siteInfo;
    }

    public EquipmentDetailDTO parseUpdateDeviceInfo(DeviceConfigChangeDto deviceConfigChangeDto) {
        EquipmentDetailDTO equipmentDetailDTO = new EquipmentDetailDTO();
        equipmentDetailDTO.setEquipmentName(deviceConfigChangeDto.getSouthDeviceName());
        return equipmentDetailDTO;
    }
}
