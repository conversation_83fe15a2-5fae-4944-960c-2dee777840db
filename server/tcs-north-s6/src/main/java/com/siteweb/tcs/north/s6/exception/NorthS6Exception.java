package com.siteweb.tcs.north.s6.exception;

/**
 * 北向S6接口自定义异常
 */
public class NorthS6Exception extends RuntimeException {

    private String errorCode;

    public NorthS6Exception() {
        super();
    }

    public NorthS6Exception(String message) {
        super(message);
    }

    public NorthS6Exception(String message, Throwable cause) {
        super(message, cause);
    }

    public NorthS6Exception(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public NorthS6Exception(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
} 