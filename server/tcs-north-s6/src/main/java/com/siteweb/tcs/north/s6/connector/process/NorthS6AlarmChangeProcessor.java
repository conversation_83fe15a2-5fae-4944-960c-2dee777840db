package com.siteweb.tcs.north.s6.connector.process;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.AlarmChange;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceAlarmChange;
import com.siteweb.tcs.hub.util.TCSTopicUtil;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.north.s6.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.s6.dal.entity.EventIdMap;
import com.siteweb.tcs.siteweb.entity.EventResponseItem;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.dal.entity.IdMap;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: tcs2
 * @description: 北向S6告警变化处理器
 * @author: xsx
 * @create: 2025-08-28 10:01
 **/
public class NorthS6AlarmChangeProcessor extends ProbeActor {

    private final ActorRef mediator;

    private IdMap idMap;

    private Integer stationId;

    private SitewebPersistentService sitewebPersistentService;


    public static Props props(GatewayMap gatewayMap){
        return Props.create(NorthS6AlarmChangeProcessor.class,gatewayMap);
    }


    private NorthS6AlarmChangeProcessor(GatewayMap gatewayMap){
        this.mediator = DistributedPubSub.get(getContext().system()).mediator();
        this.sitewebPersistentService = ConnectorDataHolder.getInstance().getBean("s6SitewebPersistentService",SitewebPersistentService.class);
        this.stationId = gatewayMap.getNorthStationId();
        handlerSubscribe(gatewayMap.getGatewayId());
        handleInit(gatewayMap);
    }

    private void handlerSubscribe(Long gatewayGuid) {
        String topicName = TCSTopicUtil.getTopicName(SubscriptionTypeEnum.ALARM, gatewayGuid);
        mediator.tell(new DistributedPubSubMediator.Subscribe(topicName, getSelf()), getSelf());
    }

    private void handleInit(GatewayMap gatewayMap) {
        //初始化数据
        //初始化数据
        idMap = new IdMap();
        idMap.setHubId(gatewayMap.getGatewayId());
        idMap.setNorthId(gatewayMap.getNorthMonitorUnitId());
        if(CollectionUtil.isNotEmpty(gatewayMap.getDeviceMapList())){
            gatewayMap.getDeviceMapList().forEach(e ->{
                IdMap deviceIdMap = new IdMap();
                deviceIdMap.setHubId(e.getDeviceId());
                deviceIdMap.setNorthId(e.getNorthEquipmentId());
                if(CollectionUtil.isNotEmpty(e.getAlarmMapList())){
                    EventIdMap alarmIdMap = new EventIdMap();
                    e.getAlarmMapList().forEach(a -> {
                        alarmIdMap.setHubId(a.getAlarmId());
                        alarmIdMap.setNorthId(a.getNorthEventId());
                        alarmIdMap.setEventConditionId(a.getNorthEventConditionId());
                        deviceIdMap.getSubIdMap().put(a.getAlarmId(),alarmIdMap);
                    });
                }
                idMap.getSubIdMap().put(e.getDeviceId(),deviceIdMap);
            });
        }
    }


    @Override
    public Receive createReceive() {
        Receive receive = ReceiveBuilder.create().match(DeviceAlarmChange.class, this::onDeviceAlarmChange).build();
        return super.createReceive().orElse(receive);
    }

    private void onDeviceAlarmChange(DeviceAlarmChange deviceAlarmChange) {
        Map<Long, IdMap> deviceIdMap = idMap.getSubIdMap();
        if(!deviceIdMap.keySet().contains(deviceAlarmChange.getDeviceId())){
            //不存在
            probe.info("不存在设备："+deviceAlarmChange.getDeviceId());
        }
        if(CollectionUtil.isEmpty(deviceAlarmChange.getAlarmChangeList())){
            probe.info("设备"+deviceAlarmChange.getDeviceId()+"告警包中为空数据");
        }
        Integer equipmentId = deviceIdMap.get(deviceAlarmChange.getDeviceId()).getNorthId();
        List<AlarmChange> alarmChangeList = deviceAlarmChange.getAlarmChangeList();
        Map<Long, EventIdMap> eventIdMapMap = deviceIdMap.get(deviceAlarmChange.getDeviceId()).getSubIdMap();
        List<EventResponseItem> eventResponseItemList = new ArrayList<>();
        alarmChangeList.forEach( a ->{
            if(eventIdMapMap.containsKey(a.getAlarmId())){
                EventIdMap eventIdMap = eventIdMapMap.get(a.getAlarmId());
                EventResponseItem eventResponseItem = null;
                eventResponseItemList.add(eventResponseItem);
            }
        });
        if(CollectionUtil.isNotEmpty(eventResponseItemList)){
            sitewebPersistentService.getConfigAPI().saveForEventResponse(eventResponseItemList);
        }
    }
}
