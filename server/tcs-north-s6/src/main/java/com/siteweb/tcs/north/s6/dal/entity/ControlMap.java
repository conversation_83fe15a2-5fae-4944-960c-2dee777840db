package com.siteweb.tcs.north.s6.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 控制映射表实体类
 */
@Data
@TableName("tcs_control_map")
public class ControlMap implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableField("DeviceId")
    private Long deviceId;

    /**
     * 北向设备ID
     */
    @TableField("NorthEquipmentId")
    private Integer northEquipmentId;

    /**
     * 控制ID
     */
    @TableField("ControlId")
    private Long controlId;

    /**
     * 北向控制ID
     */
    @TableField("NorthControlId")
    private Integer northControlId;

    /**
     * 逻辑删除标志
     */
    @TableLogic(value = "false", delval = "true")
    @TableField("Deleted")
    private Boolean deleted;
} 