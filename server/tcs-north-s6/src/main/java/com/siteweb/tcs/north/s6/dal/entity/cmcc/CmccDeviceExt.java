package com.siteweb.tcs.north.s6.dal.entity.cmcc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC设备拓展表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("cmcc_device_ext")
public class CmccDeviceExt {

    /**
     * hub设备id
     */
    @TableId("DeviceGuid")
    private Long deviceGuid;

    /**
     * CMCC设备大类id
     */
    @TableField("DeviceTypeId")
    private String deviceTypeId;

    /**
     * CMCC设备子类id
     */
    @TableField("DeviceSubTypeId")
    private String deviceSubTypeId;

    /**
     * siteweb设备种类id
     */
    @TableField("EquipmentCategoryId")
    private Integer equipmentCategoryId;
}
