package com.siteweb.tcs.north.s6.connector.letter;

import cn.hutool.core.lang.Pair;
import cn.hutool.json.JSONUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Getter
@Setter
public class SetRedisItemAction {
    private List<Pair<String,String>> items = new LinkedList<>();

    public Map<String, String> getPairMapForMset() {
        Map<String, String> redisMap = new HashMap<>();
        for (Pair<String, String> pair : items) {
            redisMap.put(pair.getKey(), pair.getValue());
        }
        return redisMap;
    }

    public void addItem(String key,String value)
    {
        items.add(new Pair<String,String>(key,value));
    }

    public String getWindowsLogString(){
        String res = JSONUtil.toJsonStr(Collections.synchronizedList(items));
        return res;
    }
}
