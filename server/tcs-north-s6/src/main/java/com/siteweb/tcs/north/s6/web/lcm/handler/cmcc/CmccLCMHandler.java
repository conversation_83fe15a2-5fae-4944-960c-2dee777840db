package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;
import com.siteweb.tcs.north.s6.web.lcm.LCMHandler;
import com.siteweb.tcs.north.s6.web.lcm.LCMHandlerObserver;
import com.siteweb.tcs.north.s6.web.service.IDeviceMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * CMCC生命周期管理处理器
 * 重构后的主协调器，负责协调各个专门的处理器
 *
 * @author: xsx
 * @create: 2025-08-19 15:07
 **/
@Component
public class CmccLCMHandler implements LCMHandler<Boolean> {

    private static final String PLUGIN_ID = "south-cmcc-plugin";

    @Autowired
    private LCMHandlerObserver lcmHandlerObserver;

    @Autowired
    private CmccSiteHandler cmccSiteHandler;

    @Autowired
    private CmccFSUHandler cmccFSUHandler;

    @Autowired
    private CmccDeviceHandler cmccDeviceHandler;

    @Autowired
    private IDeviceMapService deviceMapService;

    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    @PostConstruct
    private void registerHandler() {
        lcmHandlerObserver.registerHandler(PLUGIN_ID, this);
    }

    @Override
    public Boolean handleConfigChange(GatewayConfigChangeDto gatewayConfigChangeDto) {

        /**
         * 处理流程：
         * 1. 站点类型和站点逻辑
         * 2. 机房逻辑
         * 3. FSU CRUD
         * 4. 设备类型和设备CRUD
         * 5. 信号CRUD
         * 6. 告警CRUD
         * 7. 控制CRUD
         */
        try {
            // 站点处理
            Map<String, CmccSiteMap> cmccSiteMapMap = new HashMap<>();
            cmccSiteHandler.handleSiteInfo(gatewayConfigChangeDto, cmccSiteMapMap);

            // FSU处理
            Integer monitorUnitId = cmccFSUHandler.handleFSUInfo(gatewayConfigChangeDto, cmccSiteMapMap);

            // 设备处理
            handleDeviceRelatedInfo(gatewayConfigChangeDto, cmccSiteMapMap, monitorUnitId);
            return true;
        } catch (Exception e) {
            // TODO: 添加日志记录
            e.printStackTrace();
            throw new RuntimeException("处理配置变更失败", e);
        }
    }

    /**
     * 处理设备相关信息（设备、信号、告警、控制）
     * 现在由 CmccDeviceHandler 统一处理设备及其子组件的生命周期
     */
    private void handleDeviceRelatedInfo(GatewayConfigChangeDto gatewayConfigChangeDto,
                                         Map<String, CmccSiteMap> cmccSiteMapMap,
                                         Integer monitorUnitId) {
        // 统一处理设备及其子组件（信号、告警、控制）的生命周期
        cmccDeviceHandler.handleDeviceInfo(gatewayConfigChangeDto, cmccSiteMapMap, monitorUnitId);
    }
}
