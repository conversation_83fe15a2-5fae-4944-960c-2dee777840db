package com.siteweb.tcs.north.s6.dal.dto.cmcc;

import lombok.Data;

import java.util.List;

/**
 * 移动标准化映射 DTO
 */
@Data
public class CmccStandardMappingDTO {

    /**
     * 设备类型映射
     */
    @Data
    public static class DeviceTypeMapping {
        private String deviceTypeId;
        private String deviceTypeName;
        private String deviceSubTypeId;
        private String deviceSubTypeName;
        // 兼容旧字段名
        private Integer equipmentCategoryId;
        private String equipmentCategoryName;
        // 新的字段名（一对一映射）
        private Integer sitewebEquipmentCategoryId;
        private String sitewebEquipmentCategoryName;
        private Boolean isAutoCreated;
        private Integer mappingId;
    }

    /**
     * 局站类型映射
     */
    @Data
    public static class StationTypeMapping {
        private Integer stationTypeId;
        private String stationTypeName;
        private Integer stationCategoryId;
        private String stationCategoryName;
        private Integer mappingId;
    }

    /**
     * SiteWeb设备类型
     */
    @Data
    public static class SiteWebEquipmentCategory {
        private String id;
        private String name;
        
        public void setId(String id) {
            this.id = id;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }

    /**
     * SiteWeb局站类型
     */
    @Data
    public static class SiteWebStationCategory {
        private String id;
        private String name;
        
        public void setId(String id) {
            this.id = id;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }

    /**
     * 设备标准化页面数据
     */
    @Data
    public static class DeviceStandardData {
        private List<DeviceTypeMapping> deviceTypeMappings;
        private List<SiteWebEquipmentCategory> siteWebEquipmentCategories;
        
        public void setDeviceTypeMappings(List<DeviceTypeMapping> deviceTypeMappings) {
            this.deviceTypeMappings = deviceTypeMappings;
        }
        
        public void setSiteWebEquipmentCategories(List<SiteWebEquipmentCategory> siteWebEquipmentCategories) {
            this.siteWebEquipmentCategories = siteWebEquipmentCategories;
        }
    }

    /**
     * 局站标准化页面数据
     */
    @Data
    public static class StationStandardData {
        private List<StationTypeMapping> stationTypeMappings;
        private List<SiteWebStationCategory> siteWebStationCategories;
        
        public void setStationTypeMappings(List<StationTypeMapping> stationTypeMappings) {
            this.stationTypeMappings = stationTypeMappings;
        }
        
        public void setSiteWebStationCategories(List<SiteWebStationCategory> siteWebStationCategories) {
            this.siteWebStationCategories = siteWebStationCategories;
        }
    }
}

