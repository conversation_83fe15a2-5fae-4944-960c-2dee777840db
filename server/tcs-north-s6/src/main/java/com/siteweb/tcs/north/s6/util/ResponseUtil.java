package com.siteweb.tcs.north.s6.util;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 响应工具类
 */
public class ResponseUtil {

    /**
     * 成功响应
     * @param data 数据
     * @return ResponseEntity
     */
    public static <T> ResponseEntity<Map<String, Object>> success(T data) {
        Map<String, Object> response = new HashMap<>();
        response.put("state", true);
        response.put("timestamp", Instant.now().toEpochMilli());
        response.put("data", data);
        response.put("err_msg", null);
        response.put("err_code", null);
        return ResponseEntity.ok(response);
    }

    /**
     * 成功响应（无数据）
     * @return ResponseEntity
     */
    public static ResponseEntity<Map<String, Object>> success() {
        return success(null);
    }

    /**
     * 成功响应（带消息）
     * @param message 消息
     * @return ResponseEntity
     */
    public static ResponseEntity<Map<String, Object>> success(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("state", true);
        response.put("timestamp", Instant.now().toEpochMilli());
        response.put("data", message);
        response.put("err_msg", null);
        response.put("err_code", null);
        return ResponseEntity.ok(response);
    }

    /**
     * 错误响应
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @return ResponseEntity
     */
    public static ResponseEntity<Map<String, Object>> error(String errorCode, String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("state", false);
        response.put("timestamp", Instant.now().toEpochMilli());
        response.put("data", null);
        response.put("err_msg", errorMessage);
        response.put("err_code", errorCode);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 错误响应（仅消息）
     * @param errorMessage 错误消息
     * @return ResponseEntity
     */
    public static ResponseEntity<Map<String, Object>> error(String errorMessage) {
        return error("INTERNAL_ERROR", errorMessage);
    }

    /**
     * 错误响应（自定义状态码）
     * @param status HTTP状态码
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @return ResponseEntity
     */
    public static ResponseEntity<Map<String, Object>> error(HttpStatus status, String errorCode, String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("state", false);
        response.put("timestamp", Instant.now().toEpochMilli());
        response.put("data", null);
        response.put("err_msg", errorMessage);
        response.put("err_code", errorCode);
        return ResponseEntity.status(status).body(response);
    }
} 