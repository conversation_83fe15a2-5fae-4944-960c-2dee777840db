package com.siteweb.tcs.north.s6.connector.letter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class HistoryBatCurveItem {

    private String deviceId;
    private String stationId;
    private String voltageSignalId;
    private String currentSignalId;
    private Double voltageValue;
    private Double currentValue;
    private LocalDateTime recordTime;
    /** 电压的类型：0-普通电压值；1-本次放电记录中的最高电压值；2-本次放电记录中的最低电压值 */
    private int voltageType;
}
