package com.siteweb.tcs.north.s6.dal.entity;

import com.siteweb.tcs.north.s6.connector.process.NorthS6SignalChangeProcessor;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-09-01 11:30
 **/
@Data
public class IdMap<SubIdObject> {
    private Long hubId;
    private Integer northId;
    private Map<Long,SubIdObject> subIdMap = new HashMap<>();
}
