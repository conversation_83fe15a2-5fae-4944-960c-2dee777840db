package com.siteweb.tcs.north.s6.dal.dto;

import lombok.Data;
import org.apache.pekko.actor.ActorRef;

/**
 * @program: tcs2
 * @description: 持久化数据传输类
 * @author: xsx
 * @create: 2025-08-28 10:43
 **/
@Data
public class SinkDTO {
    /**
     * influxdb持久化
     */
    private ActorRef influxdbSink;

    /**
     * redis持久化
     */
    private ActorRef redisSink;

    /**
     * 实时数据落库
     */
    private ActorRef realTimeDataRedisSink;
}
