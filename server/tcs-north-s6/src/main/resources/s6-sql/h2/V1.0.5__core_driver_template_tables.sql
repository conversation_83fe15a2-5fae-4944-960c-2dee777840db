CREATE TABLE tbl_equipmentext (
  equipmentId INT NOT NULL COMMENT '设备主键id',
  driveTemplateId INT DEFAULT NULL COMMENT '引用驱动模板id',
  fileId BIGINT DEFAULT NULL COMMENT '文件id',
  isUpload TINYINT DEFAULT NULL COMMENT '是否已经上传',
  isReset TINYINT DEFAULT NULL COMMENT '是否重新生成',
  fieldHash INT DEFAULT NULL COMMENT '设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值(主要用于判断客户端配置工具是否对上述字段进行过修改)',
  PRIMARY KEY (equipmentId)
);