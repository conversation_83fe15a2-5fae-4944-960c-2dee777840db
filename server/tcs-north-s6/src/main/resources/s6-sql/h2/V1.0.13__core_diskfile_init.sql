-- 储能电站组态页引用图片
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(1, '2D/1', '电池1.png', 0, '2022-09-03 00:15:20');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(2, '2D/1', '控件背景框.png', 0, '2022-09-03 00:15:20');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(3, '2D/1', '导航_首页_2.png', 0, '2022-09-03 00:15:20');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(4, '2D/1', '累计用电量.png', 0, '2022-09-03 00:15:21');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(5, '2D/1', '光伏储电量.png', 0, '2022-09-03 00:15:21');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(6, '2D/1', '背景.png', 0, '2022-09-03 00:15:43');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(7, '2D/1', 'logo浅色2.png', 0, '2022-09-06 13:23:15');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(8, '2D/1', '健康状态.png', 0, '2022-09-08 04:23:14');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(9, '2D/1', '蓄电集装箱.png', 0, '2022-09-08 04:23:16');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(11, '2D/1', '实际容量.png', 0, '2022-09-28 20:53:35');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(12, '2D/1', '额定容量.png', 0, '2022-09-28 20:59:28');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(13, '2D/1', '导航_EMS指令_1.png', 0, '2022-09-29 13:53:34');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(14, '2D/1', '导航_安防_1.png', 0, '2022-09-29 13:53:51');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(15, '2D/1', '导航_储能系统_1.png', 0, '2022-09-29 13:54:11');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(16, '2D/1', '导航_蓄电池_1.png', 0, '2022-09-29 13:55:18');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(17, '2D/1', '配电柜箱3.png', 0, '2022-10-08 00:33:07');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(18, '2D/1', '导航_设施_1.png', 0, '2022-10-08 02:23:48');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(19, '2D/1', '储能电站标头.png', 0, '2022-12-12 14:07:28');
-- 运营商多站点引用图片
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(50, '2D/1', '边框465X330.png', 0, '2023-03-01 16:24:00');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(51, '2D/1', '边框889X277.png', 0, '2023-03-01 16:30:25');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(52, '2D/1', '边框317X130.png', 0, '2023-03-01 16:34:08');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(53, '2D/1', '地图.png', 0, '2023-03-01 16:48:07');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(54, '2D/1', '边框382X334.png', 0, '2023-03-01 16:34:08');
-- 电池安全组态引用图片
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(80, '2D/3', 'titbg1.png', 0, '2023-03-02 13:54:15');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(81, '2D/3', 'duankai (1).png', 0, '2023-03-02 20:50:58');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES(82, '2D/3', 'duankai (2).png', 0, '2023-03-02 20:51:03');
-- batchtool
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES (83,'batchtool','(template)ac.oid',1,'2024-06-11 16:36:31');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES (84,'batchtool','(template)SnmpManager_{address}.cfg',1,'2024-06-11 16:36:31');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES (85,'batchtool','(template)ac.control',1,'2024-06-11 16:36:31');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES (86,'batchtool','(template)SNMP{address}.so',1,'2024-06-11 16:36:31');
INSERT INTO diskfile (FileId, FilePath, FileName, Status, CreateTime) VALUES (87,'batchtool','(template)BANet{address}.so',1,'2024-06-11 16:36:31');