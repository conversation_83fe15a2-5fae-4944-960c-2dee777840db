CREATE TABLE diskfile (
    fileid BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
    filepath VARCHAR(128) NOT NULL,
    filename VARCHAR(128) NOT NULL,
    status INT,
    createtime TIMESTAMP
);

CREATE TABLE resourcestructure (
    resourcestructureid INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
    sceneid INT,
    structuretypeid INT,
    resourcestructurename VARCHAR(128),
    parentresourcestructureid INT,
    photo VARCHAR(256),
    "position" VARCHAR(256),
    levelofpath VARCHAR(128),
    display INT,
    sortvalue INT,
    extendedfield VARCHAR,
    originid INT,
    originparentid INT
);

COMMENT ON TABLE resourcestructure IS 'Resource structure table';
COMMENT ON COLUMN resourcestructure.sceneid IS '场景ID';
COMMENT ON COLUMN resourcestructure.structuretypeid IS '资源组类型';
COMMENT ON COLUMN resourcestructure.resourcestructurename IS '分组名';
COMMENT ON COLUMN resourcestructure.parentresourcestructureid IS '父分组Id';
COMMENT ON COLUMN resourcestructure.photo IS '图片';
COMMENT ON COLUMN resourcestructure."position" IS '位置信息';
COMMENT ON COLUMN resourcestructure.levelofpath IS '连接路径';
COMMENT ON COLUMN resourcestructure.display IS '是否显示';
COMMENT ON COLUMN resourcestructure.sortvalue IS '排序Index';
COMMENT ON COLUMN resourcestructure.extendedfield IS '扩展信息';
COMMENT ON COLUMN resourcestructure.originid IS '源对象ID';
COMMENT ON COLUMN resourcestructure.originparentid IS '源父对象ID';

CREATE INDEX idx_resourcestructure_levelofpath ON resourcestructure (levelofpath);
CREATE INDEX idx_resourcestructure_1 ON resourcestructure (structuretypeid, originparentid, originid);

CREATE TABLE resourcestructuretype (
    resourcestructuretypeid INT NOT NULL PRIMARY KEY,
    sceneid INT,
    resourcestructuretypename VARCHAR(128),
    description VARCHAR(255)
);

COMMENT ON TABLE resourcestructuretype IS 'Resource structure type table';
COMMENT ON COLUMN resourcestructuretype.resourcestructuretypeid IS '分类Id';
COMMENT ON COLUMN resourcestructuretype.sceneid IS '场景ID';
COMMENT ON COLUMN resourcestructuretype.resourcestructuretypename IS '分类名';
COMMENT ON COLUMN resourcestructuretype.description IS '描述信息';

CREATE TABLE rolepermissionmap (
    rolepermissionmapid INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
    roleid INT,
    permissioncategoryid INT,
    permissionid INT
);

COMMENT ON TABLE rolepermissionmap IS 'Role permission mapping table';
COMMENT ON COLUMN rolepermissionmap.roleid IS '角色ID';
COMMENT ON COLUMN rolepermissionmap.permissioncategoryid IS '权限分类ID';
COMMENT ON COLUMN rolepermissionmap.permissionid IS '权限ID';

CREATE INDEX idx_permissioncategoryid ON rolepermissionmap (permissioncategoryid);
CREATE INDEX idx_role_permissionid ON rolepermissionmap (roleid, permissionid);

CREATE TABLE systemconfig (
    systemconfigid INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
    systemconfigkey VARCHAR(255),
    systemconfigvalue VARCHAR(255),
    systemconfigtype INT,
    description VARCHAR(255)
);

COMMENT ON TABLE systemconfig IS 'System configuration table';
COMMENT ON COLUMN systemconfig.systemconfigkey IS '键名';
COMMENT ON COLUMN systemconfig.systemconfigvalue IS '对应键值';
COMMENT ON COLUMN systemconfig.systemconfigtype IS '键值对业务类型';
COMMENT ON COLUMN systemconfig.description IS '描述信息';

CREATE UNIQUE INDEX idx_systemconfigkey ON systemconfig (systemconfigkey);

CREATE TABLE userconfig (
    userconfigid INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
    userid INT,
    configtype INT,
    configkey VARCHAR(255),
    configvalue VARCHAR(255)
);

COMMENT ON TABLE userconfig IS 'User configuration table';
COMMENT ON COLUMN userconfig.userid IS '用户ID';
COMMENT ON COLUMN userconfig.configtype IS '配置类型 1TTS';
COMMENT ON COLUMN userconfig.configkey IS '用户信息配置键';
COMMENT ON COLUMN userconfig.configvalue IS '用户信息配置值';

CREATE INDEX idx_userid_usertype ON userconfig (userid, configtype);