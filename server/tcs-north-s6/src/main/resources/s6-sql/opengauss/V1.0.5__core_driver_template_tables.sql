CREATE TABLE tbl_equipmentext (
                                  equipmentId INT NOT NULL,
                                  driveTemplateId INT DEFAULT NULL,
                                  fileId BIGINT DEFAULT NULL,
                                  isUpload SMALLINT DEFAULT NULL,
                                  isReset SMALLINT DEFAULT NULL,
                                  fieldHash INT DEFAULT NULL,
                                  PRIMARY KEY (equipmentId)
);

COMMENT ON COLUMN tbl_equipmentext.equipmentId IS '设备主键id';
COMMENT ON COLUMN tbl_equipmentext.driveTemplateId IS '引用驱动模板id';
COMMENT ON COLUMN tbl_equipmentext.fileId IS '文件id';
COMMENT ON COLUMN tbl_equipmentext.isUpload IS '是否已经上传';
COMMENT ON COLUMN tbl_equipmentext.isReset IS '是否重新生成';
COMMENT ON COLUMN tbl_equipmentext.fieldHash IS '设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值(主要用于判断客户端配置工具是否对上述字段进行过修改)';
