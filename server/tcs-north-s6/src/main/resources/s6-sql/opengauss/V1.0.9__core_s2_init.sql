INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,11,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,14,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,16,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,25,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,11,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,14,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,16,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,17,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,18,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,25,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,11,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,14,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,16,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,25,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,12,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,15,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,26,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,27,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,28,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,29,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,12,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,15,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,26,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,27,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,28,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,29,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,12,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,15,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,26,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,13,13);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,13,13);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,13,13);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,21,14);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,22,15);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,23,16);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,17,17);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,17,17);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,18,18);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,18,18);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,24,20);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,22,20);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,21,21);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,21,21);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,21,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,22,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,23,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,22,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,22,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,22,23);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,23,23);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,23,23);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,24,24);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,24,24);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,24,24);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,24,26);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,27,27);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,28,28);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,29,29);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,30,30);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,31,31);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,31,31);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,31,31);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,32,32);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,33,33);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,34,34);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,31,35);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,21,35);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,35,35);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,24,36);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,24,36);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,31,37);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,12,37);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,12,38);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,23,39);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,43,40);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,44,40);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,23,40);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,41,41);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,42,41);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,41,41);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,41,42);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,42,42);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,42,42);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,42,42);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,43,43);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,43,43);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,44,44);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,44,44);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,45,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,41,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,45,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,41,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,45,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,51,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,52,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,53,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,54,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,57,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,61,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,62,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,71,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,72,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,81,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,83,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,84,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,87,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,90,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,51,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,52,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,53,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,54,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,55,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,61,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,83,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,87,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,51,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,52,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,53,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,54,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,55,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,57,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,61,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,71,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,81,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,83,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,84,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,87,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,90,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,55,55);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,62,62);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,62,62);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,13,65);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,13,65);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,13,66);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,67);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,67,67);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,68);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,68,68);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,69);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,69,69);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,72,72);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,72,72);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,22,73);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,74);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,24,75);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,24,76);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,82,82);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,82,82);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,82,82);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,85,85);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,85,85);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,85,85);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,86,86);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,86);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,86,86);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,88,88);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,90,90);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,97,97);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,97,97);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,97,97);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,98,98);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,98,98);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,98,98);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,99,99);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,99,99);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,99,99);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,101,101);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,101,101);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,101,101);

INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (0,'CfgStationStructureOP','TBL_StationStructure','StructureId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (1,'CfgStationOP','TBL_Station','StationId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (2,'NA','TBL_StationMask','StationId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (3,'CfgEquipmentOP','TBL_Equipment','StationId.EquipmentId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (4,'NA','TBL_EquipmentMask','StationId.EquipmentId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (5,'CfgHouseOP','TBL_House','StationId.HouseId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (6,'EquipmentTemplateOP','TBL_EquipmentTemplate','EquipmentTemplateId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (7,'EquipmentSignalOP','TBL_Signal','EquipmentTemplateId.SignalId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (8,'SignalMeaningsOP','TBL_SignalMeanings','EquipmentTemplateId.SignalId.StateValue');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (9,'EquipmentSignalPropertyOP','TBL_SignalProperty','EquipmentTemplateId.SignalId.SignalPropertyId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (10,'EquipmentEventOP','TBL_Event','EquipmentTemplateId.EventId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (11,'EventConditionOP','TBL_EventCondition','EquipmentTemplateId.EventId.EventConditionId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (12,'NA','TBL_EventMask','StationId.EquipmentId.EventId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (13,'EquipmentCommandOP','TBL_Control','EquipmentTemplateId.ControlId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (14,'CommandMeaningsOP','TBL_ControlMeanings','EquipmentTemplateId.ControlId.ParameterValue');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (15,'CfgMonitorUnitOP','TBL_MonitorUnit','StationId.MonitorUnitId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (16,'CfgPortOP','TBL_Port','MonitorUnitId.PortId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (17,'CfgSamplerUnitOP','TBL_SamplerUnit','MonitorUnitId.PortId.SamplerUnitId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (18,'NA','TBL_ChannelMap','MonitorUnitId.SamplerUnitId.OriginalChannelNo.StandardChannelNo');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (19,'MonitorUnitSignalOP','TBL_MonitorUnitSignal','StationId.MonitorUnitId.EquipmentId.SignalId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (20,'MonitorUnitEventOP','TBL_MonitorUnitEvent','StationId.MonitorUnitId.EquipmentId.EventId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (21,'MonitorUnitControlOP','TBL_MonitorUnitControl','StationId.MonitorUnitId.EquipmentId.ControlId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (22,'NA','TBL_DataItem','EntryId.ItemId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (23,'NA','TBL_DataEntry','EntryId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (24,'NA','TBL_StandardDic','StandardDicId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (25,'NA','TBL_EventBaseDic','BaseTypeId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (26,'SamplerOP','TBL_Sampler','SamplerId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (27,'ResourceStructureOP','ResourceStructure','ResourceStructureId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (28,'ResourceStructureOP','tbl_equipment','ResourceStructureId.EquipmentId');

INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (0,0,0,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (0,1,0,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (0,2,0,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (0,3,0,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (1,0,1,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (1,1,1,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (1,2,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (1,3,1,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (6,0,6,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (6,1,6,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (6,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (6,3,6,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (15,0,15,0,'1.2');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (15,1,15,1,'1.2');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (15,2,15,2,'1.2');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (15,3,15,3,'1.2');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (23,0,23,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (23,1,23,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (23,2,23,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (23,3,23,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (25,0,25,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (25,1,25,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (25,2,25,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (25,3,25,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (26,0,26,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (26,1,26,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (26,2,26,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (26,3,26,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (3,1,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (3,2,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (3,3,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (5,1,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (5,2,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (5,3,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (7,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (7,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (7,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (8,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (8,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (8,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (9,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (9,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (9,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (10,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (10,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (10,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (11,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (11,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (11,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (13,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (13,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (13,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (14,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (14,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (14,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (16,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (16,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (16,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (17,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (17,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (17,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (18,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (18,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (18,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (19,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (19,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (19,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (20,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (20,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (20,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (21,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (21,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (21,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (22,1,23,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (22,2,23,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (22,3,23,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (27,1,27,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (27,2,27,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (27,3,27,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (28,1,28,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (28,2,28,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (28,3,28,3,'1');


INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (1,'ArchiveLogMiner','ScheduleNumber');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (2,'TBL_Area','AreaId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (3,'BatchReportParam','BatchRptParamId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (4,'TBL_Card','CardId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (7,'TBL_Equipment','EquipmentId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (10,'TBL_House','HouseId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (11,'TSL_MonitorUnit','MonitorUnitId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (12,'TSL_Port','PortId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (14,'TSL_SamplerUnit','SamplerUnitId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (16,'TBL_Station','StationId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (17,'TBL_StationStructure','StructureId,');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (18,'TBL_WorkStation','WorkStationId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (22,'TBL_Department','DepartmentId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (23,'TBL_Door','ManualDoorId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (24,'TBL_DoorController','DoorControlId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (25,'TBL_Employee','EmployeeId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (26,'TBL_Control','ControlId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (27,'TBL_Event','EventId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (28,'TBL_Signal','SignalId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (29,'TBL_EquipmentTemplate','EquipmentTemplateId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (31,'TBL_Experience','ExperienceId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (32,'TBL_ExperienceWord','ExperienceKeyId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (35,'TBL_MenuItems','MenuItemsId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (36,'TBL_Menus','MenusId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (37,'NotifyServer','NotifyServerId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (38,'NotifyReceiver','NotifyReceiverId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (39,'EventFilter','EventFilterId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (40,'EventFilterCondition','EventFilterConditionId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (41,'TBL_Operation','OperationId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (42,'TBL_OperationGroup','GroupId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (43,'ReportTask','TaskId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (44,'ReportInstance','ReportId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (45,'TSL_Sampler','SamplerId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (46,'TBL_SpecialtyGroup','SpecialtyGroupId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (52,'TBL_TimeGroup','TimeGroupId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (53,'TBL_TimeGroupSpan','TimeSpanId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (56,'TBL_UserRole','RoleId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (60,'TBL_HistorySelection','HistorySelectionId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (61,'TBL_DataItem','EntryItemId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (64,'TBL_CustomInfo','CustomInfoId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (65,'NotificationLog','NotificationLogId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (69,'NotifyMode','NotifyModeId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (70,'TBL_EventLogAction','LogActioinId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (81,'TBL_DoorGroup','DoorGroupId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (82,'TBL_Building','BuildingId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (83,'TBL_Floor','FloorId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (84,'TBL_Room','RoomId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (85,'MDC','MDCId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (86,'CorePointSECMap','CorePointSECMap');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (100,'tbl_doorarea','areaid');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (208,'TBL_FingerReaderMap','ReaderId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (216,'TBL_FaceData','FaceId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (217,'TBL_Fingerprint','FingerprintId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (218,'TBL_GraphicPage','Id');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (219,'resourcestructure','ResourceStructureId');


INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('BackupHistoryDataDir',NULL);
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('BackupConfigDir',NULL);
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('EnableConfigSyn','false');
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('MUSyncRecordEnable','true');
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('DSEditServerName',NULL);
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('BDicVersion','1.0.0');

INSERT INTO tbl_userrole (RoleId, RoleName, Description) VALUES (-1,'系统管理员','拥有系统所有权限');

INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-7,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-6,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-5,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-4,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-3,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-2,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-1,-1);


INSERT INTO tsl_monitorunitconfig (Id, AppConfigId, SiteWebTimeOut, RetryTimes, HeartBeat, EquipmentTimeOut, PortInterruptCount, PortInitializeInternal, MaxPortInitializeTimes, PortQueryTimeOut, DataSaveTimes, HistorySignalSavedTimes, HistoryBatterySavedTimes, HistoryEventSavedTimes, CardRecordSavedCount, ControlLog, IpAddressDS) VALUES (1,1,10,3,30,6,3,30,10,5,500,500,500,500,500,TRUE,'udp://127.0.0.1:9000');