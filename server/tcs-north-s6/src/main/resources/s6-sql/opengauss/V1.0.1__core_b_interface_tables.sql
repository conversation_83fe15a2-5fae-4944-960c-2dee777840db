-- 设备子类型表
CREATE TABLE tbl_devicesubtypecmcc (
                                       DeviceTypeID INT NOT NULL,
                                       Devi<PERSON>SubTypeID INT NOT NULL,
                                       DeviceSubTypeName VARCHAR(256) DEFAULT NULL,
                                       Description TEXT,
                                       PRIMARY KEY (DeviceSubTypeID, DeviceTypeID)
);

-- 设备类型表
CREATE TABLE tbl_devicetypecmcc (
                                    DeviceTypeID SERIAL PRIMARY KEY,
                                    DeviceTypeName VARCHAR(256) DEFAULT NULL,
                                    Description TEXT
);

-- 设备表（CMCC）
CREATE TABLE tbl_equipmentcmcc (
                                   StationId INT NOT NULL,
                                   MonitorUnitId INT NOT NULL,
                                   EquipmentId INT NOT NULL,
                                   DeviceID VARCHAR(510) DEFAULT NULL,
                                   DeviceName VARCHAR(510) DEFAULT NULL,
                                   FSUID VARCHAR(510) DEFAULT '',
                                   SiteID VARCHAR(510) DEFAULT '',
                                   SiteName VARCHAR(510) DEFAULT NULL,
                                   RoomID VARCHAR(510) DEFAULT NULL,
                                   RoomName VARCHAR(510) DEFAULT NULL,
                                   DeviceType INT DEFAULT NULL,
                                   DeviceSubType INT DEFAULT NULL,
                                   Model VARCHAR(510) DEFAULT NULL,
                                   Brand VARCHAR(510) DEFAULT NULL,
                                   RatedCapacity DOUBLE PRECISION DEFAULT NULL,
                                   Version VARCHAR(40) DEFAULT NULL,
                                   BeginRunTime TIMESTAMP DEFAULT NULL,
                                   DevDescribe VARCHAR(510) DEFAULT NULL,
                                   ExtendField1 VARCHAR(510) DEFAULT NULL,
                                   ExtendField2 VARCHAR(510) DEFAULT NULL,
                                   PRIMARY KEY (EquipmentId, StationId)
);

CREATE INDEX TBL_EquipmentCMCC_IDX1 ON tbl_equipmentcmcc (StationId, EquipmentId);
CREATE INDEX TBL_EquipmentCMCC_IDX2 ON tbl_equipmentcmcc (FSUID, DeviceID);

-- 设备表（CUCC）
CREATE TABLE tbl_equipmentcucc (
                                   StationId INT NOT NULL,
                                   MonitorUnitId INT NOT NULL,
                                   EquipmentId INT NOT NULL,
                                   DeviceID VARCHAR(510) DEFAULT NULL,
                                   DeviceName VARCHAR(510) DEFAULT NULL,
                                   DeviceRID VARCHAR(510) DEFAULT NULL,
                                   SUID VARCHAR(510) DEFAULT NULL,
                                   DeviceVender VARCHAR(510) DEFAULT NULL,
                                   DeviceType VARCHAR(510) DEFAULT NULL,
                                   MFD VARCHAR(510) DEFAULT NULL,
                                   ControllerType VARCHAR(510) DEFAULT NULL,
                                   SoftwareVersion VARCHAR(510) DEFAULT NULL,
                                   BatchNo VARCHAR(510) DEFAULT NULL,
                                   Password VARCHAR(1024) DEFAULT NULL,
                                   ExtendField1 VARCHAR(510) DEFAULT NULL,
                                   ExtendField2 VARCHAR(510) DEFAULT NULL,
                                   PRIMARY KEY (EquipmentId, StationId)
);

CREATE INDEX TBL_EquipmentCUCC_IDX1 ON tbl_equipmentcucc (StationId, EquipmentId);
CREATE INDEX IDX_EquipmentCUCC_SU_DeviceId ON tbl_equipmentcucc (SUID, DeviceID);

-- 房间表（CMCC）
CREATE TABLE tbl_roomcmcc (
                              StationId INT NOT NULL,
                              HouseId INT NOT NULL,
                              RoomID VARCHAR(250) DEFAULT NULL,
                              RoomName VARCHAR(510) DEFAULT NULL,
                              SiteID VARCHAR(510) DEFAULT NULL,
                              Description VARCHAR(510) DEFAULT NULL,
                              PRIMARY KEY (HouseId, StationId)
);

CREATE INDEX TBL_RoomCMCC_IDX1 ON tbl_roomcmcc (StationId, HouseId);

-- 站点表（CMCC）
CREATE TABLE tbl_stationcmcc (
                                 StationId INT NOT NULL,
                                 SiteID VARCHAR(510) DEFAULT NULL,
                                 SiteName VARCHAR(510) DEFAULT NULL,
                                 Description VARCHAR(510) DEFAULT NULL,
                                 PRIMARY KEY (StationId)
);

CREATE INDEX TBL_StationCMCC_IDX1 ON tbl_stationcmcc (StationId);

-- 监控单元表（CMCC）
CREATE TABLE tsl_monitorunitcmcc (
                                     StationId INT NOT NULL,
                                     MonitorUnitId INT NOT NULL,
                                     FSUID VARCHAR(510) DEFAULT '',
                                     FSUName VARCHAR(510) DEFAULT NULL,
                                     SiteID VARCHAR(510) DEFAULT '',
                                     SiteName VARCHAR(510) DEFAULT NULL,
                                     RoomID VARCHAR(510) DEFAULT NULL,
                                     RoomName VARCHAR(510) DEFAULT NULL,
                                     UserName VARCHAR(80) DEFAULT NULL,
                                     PassWord VARCHAR(80) DEFAULT NULL,
                                     FSUIP VARCHAR(510) DEFAULT NULL,
                                     FSUMAC VARCHAR(40) DEFAULT NULL,
                                     FSUVER VARCHAR(40) DEFAULT NULL,
                                     Result INT DEFAULT NULL,
                                     FailureCause VARCHAR(510) DEFAULT NULL,
                                     CPUUsage DOUBLE PRECISION DEFAULT NULL,
                                     MEMUsage DOUBLE PRECISION DEFAULT NULL,
                                     HardDiskUsage DOUBLE PRECISION DEFAULT NULL,
                                     GetFSUInfoResult INT DEFAULT NULL,
                                     GetFSUFaliureCause VARCHAR(510) DEFAULT NULL,
                                     GetFSUTime TIMESTAMP DEFAULT NULL,
                                     FTPUserName VARCHAR(80) DEFAULT NULL,
                                     FTPPassWord VARCHAR(80) DEFAULT NULL,
                                     ExtendField1 VARCHAR(510) DEFAULT NULL,
                                     ExtendField2 VARCHAR(510) DEFAULT NULL,
                                     GetConfigFlag INT DEFAULT 0,
                                     PRIMARY KEY (MonitorUnitId, StationId)
);

CREATE INDEX TSL_MonitorUnitCMCC_IDX1 ON tsl_monitorunitcmcc (StationId, MonitorUnitId);
CREATE INDEX IDX_MonitorUnitCMCC_1 ON tsl_monitorunitcmcc (FSUID);

-- 监控单元表（CUCC）
CREATE TABLE tsl_monitorunitcucc (
                                     StationId INT NOT NULL,
                                     MonitorUnitId INT NOT NULL,
                                     SUID VARCHAR(510) DEFAULT NULL,
                                     SUName VARCHAR(510) DEFAULT NULL,
                                     SURID VARCHAR(510) DEFAULT NULL,
                                     UserName VARCHAR(80) DEFAULT NULL,
                                     PassWord VARCHAR(80) DEFAULT NULL,
                                     SUIP VARCHAR(510) DEFAULT NULL,
                                     SUVER VARCHAR(40) DEFAULT NULL,
                                     SUPort VARCHAR(40) DEFAULT NULL,
                                     SUVendor VARCHAR(40) DEFAULT NULL,
                                     SUModel VARCHAR(40) DEFAULT NULL,
                                     SUHardVER VARCHAR(40) DEFAULT NULL,
                                     Longitude DOUBLE PRECISION DEFAULT NULL,
                                     Latitude DOUBLE PRECISION DEFAULT NULL,
                                     Result INT DEFAULT NULL,
                                     FailureCause VARCHAR(510) DEFAULT NULL,
                                     CPUUsage DOUBLE PRECISION DEFAULT NULL,
                                     MEMUsage DOUBLE PRECISION DEFAULT NULL,
                                     GetSUInfoResult INT DEFAULT NULL,
                                     GetSUTime TIMESTAMP DEFAULT NULL,
                                     FTPUserName VARCHAR(80) DEFAULT NULL,
                                     FTPPassWord VARCHAR(80) DEFAULT NULL,
                                     ConfigState INT DEFAULT NULL,
                                     RegisterTime TIMESTAMP DEFAULT NULL,
                                     SUConfigTime VARCHAR(510) DEFAULT NULL,
                                     CenterConfigTime VARCHAR(510) DEFAULT NULL,
                                     Devices TEXT,
                                     ExtendField1 VARCHAR(510) DEFAULT NULL,
                                     ExtendField2 VARCHAR(510) DEFAULT NULL,
                                     PRIMARY KEY (MonitorUnitId, StationId)
);

CREATE INDEX TSL_MonitorUnitCUCC_IDX1 ON tsl_monitorunitcucc (StationId, MonitorUnitId);
