INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1,1,'局站树区划种类',NULL,'StationGroupType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2,1,'局站等级',NULL,'StationGrade',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3,1,'局站分类',NULL,'StationType',FALSE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (4,1,'局站结构定义',NULL,'StructureCategory ',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (5,1,'局站状态',NULL,'StationState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (6,1,'地图种类',NULL,'MapCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (7,1,'设备种类',NULL,'EquipmentCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (8,1,'设备分类',NULL,'EquipmentType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (9,1,'设备属性',NULL,'EquipmentProperty',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (10,1,'设备资产状态',NULL,'EquipmentAssetState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (11,1,'电池类型',NULL,'EquipmentCase',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (12,1,'电池工作状态',NULL,'EquipmentState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (13,1,'油机状态',NULL,'GeneratorState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (14,1,'设备厂商',NULL,'Vendor',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (15,1,'代维厂商',NULL,'Agent',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (16,1,'设备单位',NULL,'EquipmentUnit',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (17,1,'信号种类',NULL,'SignalCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (18,1,'信号分类',NULL,'SignalType ',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (19,1,'存盘方式',NULL,'SignalThresholdType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (20,1,'信号单位',NULL,'SignalUnit',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (21,1,'信号属性',NULL,'SignalProperty',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (22,1,'通道类型',NULL,'ChannelType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (23,1,'事件等级',NULL,'EventSeverity',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (24,1,'事件种类',NULL,'EventCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (25,1,'事件开始类型',NULL,'StartType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (26,1,'事件结束类型',NULL,'EndType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (27,1,'事件阶段',NULL,'EventPhase',FALSE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (28,1,'控制命令权限级',NULL,'CommandSeverity',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (29,1,'控制命令执行阶段',NULL,'CommandPhase',FALSE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (30,1,'控制命令结果类型',NULL,'CommandResultType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (31,1,'控制命令种类',NULL,'CommandCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (32,1,'控制命令分类',NULL,'CommandType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (33,1,'监控单元运行模式',NULL,'RunMode',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (34,1,'监控单元种类',NULL,'MonitorUnitCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (35,1,'监控单元分类',NULL,'MonitorUnitType',FALSE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (36,1,'监控单元工作状态',NULL,'MonitorUnitWorkState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (37,1,'采集器种类',NULL,'SamplerType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (38,1,'采集单元种类',NULL,'SamplerUnitType',FALSE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (39,1,'端口种类',NULL,'PortType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (40,1,'通知方式',NULL,'NotifyCategory',FALSE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (41,1,'员工分类',NULL,'EmployeeType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (42,1,'员工职称',NULL,'EmployeeTitle',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (43,1,'角色类型',NULL,'RoleType',FALSE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (44,1,'片区类型',NULL,'AreaType',FALSE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (45,1,'操作日志类型',NULL,'LogType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (46,1,'门禁卡状态',NULL,'CardStutas',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (47,1,'门禁卡种类',NULL,'CardCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (48,1,'门禁进出密码模式',NULL,'PasswordMode',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (49,1,'门禁刷卡标识说明',NULL,'ValidType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (50,1,'设备预警种类',NULL,'EquipmentPredictionCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (51,1,'后台服务种类',NULL,'DbTaskCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (52,1,'后台服务执行方式 ',NULL,'DbTaskExecMode',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (53,1,'后台服务间隔时间种类',NULL,'DbTaskIntervalType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (54,1,'后台服务处理方式',NULL,'DbTaskProcessType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (55,1,'报表种类',NULL,'ReportCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (56,1,'报表时间种类',NULL,'ReportTimeCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (57,1,'报表图表种类',NULL,'ChartCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (58,1,'工作台种类',NULL,'WorkStationCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (59,1,'工作台状态',NULL,'WorkStationState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (60,1,'工作台协议种类',NULL,'ProtocolCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (61,1,'工作台服务种类',NULL,'ServiceCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (62,1,'监控中心(NO.)',NULL,'CenterCode',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (63,1,'报表输出格式定义',NULL,'ReportOutputFormat',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (64,1,'定时任务执行频率定义',NULL,'TaskExecuteFrequency',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (65,1,'触发器共享模式定义',NULL,'TriggerShareMode',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (66,1,'定时任务执行模式',NULL,'TaskExecuteMode',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (67,1,'事件屏蔽种类',NULL,'TimeGroupCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (68,1,'控件种类',NULL,'ControlType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (69,1,'事件状态',NULL,'EventState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (70,1,'数据类型',NULL,'DataType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (71,1,'局站种类',NULL,'StationCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (72,1,'设备分组类型',NULL,'EquipmentGroupType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (73,1,'门禁种类',NULL,'DoorCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (74,1,'门禁红外设置类型',NULL,'DoorInfrared',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (75,1,'卡分组',NULL,'CardGroup',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (76,1,'查询时段种类',NULL,'SelectTimeCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (77,1,'自诊断设备种类',NULL,'SelfDiagnostEquipmentCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (78,1,'用户设备种类',NULL,'NonMonitorEquipmentCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (79,1,'设备监控状态',NULL,'EquipmentMonitoringState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (80,1,'登录分类',NULL,'LoginType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (81,1,'执行类型',NULL,'ExcuteType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (82,1,'部门级别',NULL,'DepartmentLevel',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (83,1,'派单状态',NULL,'EomsStatus',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (84,1,'人员性别',NULL,'Gender',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (85,1,'自诊断状态',NULL,'DiagnosisState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (86,1,'配置验证规则',NULL,'ExamineRule',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (87,1,'操作选项',NULL,'OperationCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (88,1,'网管告警级别',NULL,'NetManagement EventServerity',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (89,1,'告警类别',NULL,'EventClass',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (90,1,'告警逻辑分类',NULL,'Event Logic Category',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (91,1,'告警逻辑子类',NULL,'Event Logic Sub Category',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (92,1,'VIP级别',NULL,'VIP Level',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (93,1,'VIP类型',NULL,'VIP Category',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (94,1,'告警对象类型',NULL,'EventObjectCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (95,1,'网络类型',NULL,'Net Category',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (96,1,'网元版本',NULL,'Cell Version',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (97,1,'告警标准名',NULL,'Event Standard Name',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (98,1,'设备子类',NULL,'Equipment Sub Category',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (99,1,'专业',NULL,'Professional',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (100,1,'映射方式',NULL,'Map Category',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (101,1,'告警联动触发类型',NULL,'LogAction Trigger Type',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (102,1,'告警联动通知方式',NULL,'LogAction Inform Type',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (103,1,'控制执行类别',NULL,'ControlExcute Type',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (104,1,'网络连接类型',NULL,'Connection Type',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (105,1,'告警来源',NULL,'Alarm By',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (106,1,'统计方式',NULL,'Statistics',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (107,1,'传输协议类型',NULL,'Protocol',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (108,1,'MIB',NULL,'MIB',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (109,1,'客户',NULL,'Customer',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (110,1,'拓扑连接类型',NULL,'Topology Type',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (111,1,'级连方式',NULL,'Link Type',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (112,1,'基类信号',NULL,'Base Signal',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (113,1,'基类事件',NULL,'Base Event',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (114,1,'基类控制',NULL,'Base Control',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (115,1,'操作权限类别',NULL,'Operation Category',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (116,1,'控制类型',NULL,'Control Type',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (117,1,'落后电池发现方式',NULL,'BadBatteryIdentifyType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (118,1,'落后电池处理方式',NULL,'BadBatteryDealType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (119,1,'设备型号',NULL,'Equipment Model',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (120,1,'屏蔽类型',NULL,'TimeGroupType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (121,1,'工作模式',NULL,'Work Model',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (122,1,'事件状态',NULL,'EventState',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (123,1,'放电类型',NULL,'Discharge Category',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (124,1,'合同基站数',NULL,'Contract Station Count',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (150,1,'操作对象类型',NULL,'Operation Object Type',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (151,1,'配置检查问题类型',NULL,'Config Check Error Type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (152,1,'是否标准化',NULL,'It is standardized',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (153,1,'字典项类型',NULL,'Dictionary entry type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (156,1,'告警变化表通知类型',NULL,'AlarmChange entry type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (157,1,'门属性',NULL,'Door Property',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1001,1,'基站建筑类型',NULL,'BuildingType',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1002,1,'电表采集类别',NULL,'ScaleObject',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1003,1,'用电量异常类型',NULL,'PowerConsumptionEvenCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1004,1,'基站载频数',NULL,'Station BordNumber',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1005,1,'基站房屋面积',NULL,'Station Acreage',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1006,1,'基站主设备厂商',NULL,'Station Vendors',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1007,1,'基站空调品牌',NULL,'Station Air Condition Bands',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1008,1,'基站开关电源',NULL,'Station Rectifier Bands',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1009,1,'电表类型',NULL,'AmmeterCategory',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1010,1,'电源模块休眠功能',NULL,'Sleep Function Power Module',TRUE,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1011,1,'报表参数控件类型','','ParameterType',TRUE,'Report Parameter Control Type');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2003,1,'卡片种类',NULL,'Door Card Type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2004,1,'指纹读头多种开门方式',NULL,'FingerReaderMultiOpenDoorType',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2005,1,'指纹读头进出门标志',NULL,'FingerReader Door In Or Out Flag',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2006,1,'指纹采集串口服务器类型',NULL,'Serial Device Type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2007,1,'艾默生ISU串口服务器串口号',NULL,'Serial Device Type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2008,1,'艾默生IDU串口服务器串口号',NULL,'Serial Device Type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2009,1,'艾默生eStone串口服务器串口号',NULL,'Serial Device Type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2010,1,'纽贝尔806D4M3/D2M3串口服务器串口号',NULL,'Serial Device Type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2012,1,'中控门禁人员权限',NULL,'ZK door access role',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2013,1,'海康门禁门开方式',NULL,'Hik door open mode',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2021,1,'门禁用户属性',NULL,'Employee Feature',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2022,1,'资产类型',NULL,'AssetType',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2023,1,'驱动模板类型',NULL,'Drive template Type',TRUE,'批量工具驱动模板类型');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3001,1,'审计级别',NULL,'Audit level',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3002,1,'类别',NULL,'Type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3003,1,'IT设备模型类型',NULL,'Category',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3004,1,'卡号类型',NULL,'Card number type',TRUE,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3005,1,'组态模板组件类型',NULL,'Configure the template graphic type',TRUE,'');

INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1,0,0,1,1,'按行政区分组','Group by Regions',TRUE,TRUE,FALSE,'','StationGroupType1.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (3,0,0,1,2,'按局站等级分组','Group by Station Grade',TRUE,FALSE,FALSE,'','StationGroupType2.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (5,0,0,1,3,'典型局站分组','Typical Station',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (7,0,0,2,1,'VIP基站','VIP Station',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (9,0,0,2,2,'普通基站','General Station',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (11,0,0,2,3,'重要基站','Important Station ',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (13,0,0,2,4,'边远基站','Boundary Station',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (15,0,0,3,1,'真实局站','Physical Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (17,0,0,3,2,'虚拟局站','Virtual Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (19,0,0,4,4,'四级中心(SSC)','Super Supervision Center',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (21,0,0,4,3,'三级中心(SC)','Supervision Center',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (23,0,0,4,2,'二级中心(SS)','Supervision Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (25,0,0,4,1,'二级中心区划(SG)','Supervision Group',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (27,0,0,5,1,'联网运行 ','Online',TRUE,TRUE,FALSE,'','Ok.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (29,0,0,5,2,'测试状态 ','Testing',TRUE,TRUE,FALSE,'','Testing.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (31,0,0,5,3,'工程状态 ','Building',TRUE,TRUE,FALSE,'','Building.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (33,0,0,5,4,'屏蔽状态','Masking',TRUE,TRUE,FALSE,'','Masking.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (35,0,0,6,1,'静态地图','Static Map',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (37,0,0,6,2,'矢量地图','Vector Map',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (39,0,0,7,11,'高压配电','High Voltage Distribution',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (41,0,0,7,12,'低压配电','Low Voltage Distribution',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (43,0,0,7,13,'油机发电机组','Diesel Generator',TRUE,TRUE,FALSE,'','Generator.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (45,0,0,7,14,'高压进线柜','High-voltage Line Cabinet',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (47,0,0,7,15,'低压出线开关','Low-voltage circuit switcher',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (49,0,0,7,16,'高压出线柜','High-voltage out Cabinet',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (51,0,0,7,17,'高压操作电源','High-voltage power supply',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (53,0,0,7,18,'变压器','Transformer',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (55,0,0,7,21,'整流室交流配电屏','Rectifier Room AC Distribution Board',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (57,0,0,7,22,'整流器','Rectifier',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (59,0,0,7,23,'直流配电屏','DC Distribution Board',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (61,0,0,7,24,'蓄电池','Battery',TRUE,TRUE,FALSE,'','Battery.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (63,0,0,7,25,'高压母联柜','High-voltage Connect Cabinet',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (65,0,0,7,26,'低压进线柜','Low-voltage Line Cabinet',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (67,0,0,7,27,'低压电容补偿柜','Low Voltage Capacitor Cabinet',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (69,0,0,7,28,'低压谐波补偿柜','Low Harmonic Compensation Cabinet',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (71,0,0,7,29,'低压母联柜','Low-voltage Connect Cabinet',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (73,0,0,7,31,'UPS设备','UPS',TRUE,TRUE,FALSE,'','Ups.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (75,0,0,7,41,'中央空调(风冷)','Central Air-Condition (Wind Cooling)',TRUE,TRUE,FALSE,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (77,0,0,7,42,'中央空调(水冷)','Central Air-Condition (Water Cooling)',TRUE,TRUE,FALSE,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (79,0,0,7,43,'专用空调(风冷)','Special Air-Condition (Wind Cooling)',TRUE,TRUE,FALSE,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (81,0,0,7,44,'专用空调(水冷)','Special Air-Condition (Water Cooling)',TRUE,TRUE,FALSE,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (83,0,0,7,45,'分体空调','Split Air-Condition',TRUE,TRUE,FALSE,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (85,0,0,7,51,'动力设备局房','Power Equipment House',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (87,0,0,7,52,'空调设备局房','Air-Condition Equipment House',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (89,0,0,7,53,'通信设备局房','Communication Equipment House',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (91,0,0,7,54,'OMC局房','OMC House',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (93,0,0,7,55,'极早期烟感设备','Early-warning Smoke Equipment',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (95,0,0,7,57,'MDF设备','MDF Equipment',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (97,0,0,7,61,'重要信号设备','Important Signal Equipment',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (99,0,0,7,62,'风能设备','Wind Equipment',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (101,0,0,7,71,'地线设备','Grounding Equipment',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (103,0,0,7,72,'防辟雷设备','Lightning Protection Equipment',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (105,0,0,7,81,'通信设备','Communication Equipment',TRUE,TRUE,FALSE,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (107,0,0,7,82,'门禁','Door Access Control',TRUE,TRUE,FALSE,'','Door.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (109,0,0,7,83,'温度计','Thermometer',TRUE,TRUE,FALSE,'','thermometer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (111,0,0,7,84,'ATM设备','ATM',TRUE,TRUE,FALSE,'','ATMKey.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (113,0,0,7,85,'智能电表','Ammeter',TRUE,TRUE,FALSE,'','ATMKey.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (115,0,0,7,86,'智能通风系统','Aeration System',TRUE,TRUE,FALSE,'','ATMKey.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (117,0,0,7,87,'1-wire设备','1-wire Equipment',TRUE,TRUE,FALSE,'','ATMKey.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (119,0,0,7,90,'图像设备','Image Equipment',TRUE,TRUE,FALSE,'','webcam.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (121,0,0,7,97,'人脸读头','Face Reader',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (123,0,0,7,98,'指纹录入仪','Finger Reader',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (125,0,0,7,99,'自诊断设备','SelfDiagnostics',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (127,0,0,7,101,'240V组合电源','240V Power Supply',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (129,0,0,8,0,'未知','Unkown',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (131,0,0,8,1,'监控设备','Monitoring Equipment',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (133,0,0,8,2,'自诊断设备','Self-Diagnostic Equipment',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (135,0,0,8,3,'非监控设备','Non-Monitoring Equipment',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (137,0,0,9,1,'智能设备','Intelligent Equipment',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (139,0,0,9,2,'告警可屏蔽','Suppressable Equipment',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (141,0,0,9,3,'可控设备','Controlable Equipment',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (143,0,0,9,5,'重要设备','Important Equipment',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (145,0,0,9,4,'未用','Unused',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (147,0,0,9,6,'24伏蓄电池','24 Volt Battery',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (149,0,0,9,7,'48伏蓄电池','48 Volt Battery',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (151,0,0,9,8,'虚拟设备','Virtual Equipment',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (153,0,0,10,1,'未知','Unknown',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (155,0,0,10,2,'故障','Fault',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (157,0,0,10,3,'闲置','Free',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (159,0,0,10,4,'报废','Abandon',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (161,0,0,10,5,'正常','Normal',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (163,0,0,11,0,'未知','Unkown',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (165,0,0,11,1,'2V','2V',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (167,0,0,11,2,'6V','6V',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (169,0,0,11,3,'12V','12V',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (171,0,0,11,4,'24V','24V',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (173,0,0,11,5,'48V','48V',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (175,0,0,12,1,'浮充','Floating Charge',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (177,0,0,12,2,'放电','Discharge',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (179,0,0,12,3,'均充','Average Charge',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (181,0,0,13,1,'待命','Stand by',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (183,0,0,13,2,'派单','Send Order',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (185,0,0,13,3,'在途','On the way',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (187,0,0,13,4,'发电','Generating',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (189,0,0,14,0,'未知','Unknown',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (191,0,0,14,1,'维谛','Vertiv',TRUE,FALSE,FALSE,'包括维谛、力博特、华为电气、安圣、艾默生、克劳瑞德等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (193,0,0,14,2,'中达','Delta',TRUE,FALSE,FALSE,'包括台达、中达电通等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (195,0,0,14,3,'南都','Nandu',TRUE,FALSE,FALSE,'浙江南都',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (197,0,0,14,4,'双登','ShuangDeng',TRUE,FALSE,FALSE,'江苏双登',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (199,0,0,14,5,'光宇','GuangYu',TRUE,FALSE,FALSE,'哈尔滨光宇',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (201,0,0,14,6,'雅达','Jata',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (203,0,0,14,7,'祥正','XiangZheng',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (205,0,0,14,8,'大金','Daikin',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (207,0,0,14,9,'三菱','Mitsubishi',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (209,0,0,14,10,'三洋','Sanyou',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (211,0,0,14,11,'海尔','Hair',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (213,0,0,14,12,'松下','Panasonic',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (215,0,0,14,13,'海尔三菱','Hair&Mitsubishi',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (217,0,0,14,14,'格力','Gree',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (219,0,0,14,15,'华为','Huawei',TRUE,FALSE,FALSE,'华为技术',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (221,0,0,14,16,'中兴','ZTE',TRUE,FALSE,FALSE,'中兴通讯',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (223,0,0,14,17,'罗兰','Rolan',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (225,0,0,14,18,'西恩迪(C&D)','C&D',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (227,0,0,14,19,'明光','Mingguang',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (229,0,0,14,20,'华达','Huada',TRUE,FALSE,FALSE,'包括深圳华达、艾诺斯华达等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (231,0,0,14,21,'奇恩比(GNB)','GNB',TRUE,FALSE,FALSE,'美国GNB',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (233,0,0,14,22,'胜利','Shengli',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (235,0,0,14,23,'佳力图','Galitu',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (237,0,0,14,24,'春兰','Chunnan',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (239,0,0,14,25,'卡特彼勒','CaterPillar',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (241,0,0,14,26,'德锋','Xianfeng',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (243,0,0,14,27,'中恒','ZhongHeng',TRUE,FALSE,FALSE,'包括施威特克、侨兴、中恒等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (245,0,0,14,28,'海洛斯','Hiross',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (247,0,0,14,29,'立博特','Libert',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (249,0,0,14,30,'东芝','Toshiba',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (251,0,0,14,31,'美的','Midea',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (253,0,0,14,32,'科勒','Kohler',TRUE,FALSE,FALSE,'含常州科勒',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (255,0,0,14,33,'星信','Xingxin',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (257,0,0,14,34,'新宏博','Xinhongbo',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (259,0,0,14,35,'江苏移动','JiangShu mobile',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (261,0,0,14,36,'先控','Sicon',TRUE,FALSE,FALSE,'河北先控',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (263,0,0,14,37,'锐高','Ruigao',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (265,0,0,14,38,'爱立信','Ericsson',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (267,0,0,14,39,'APC','APC',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (269,0,0,14,40,'福发','Fufa',TRUE,FALSE,FALSE,'福州柴油机',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (271,0,0,14,41,'PowerWare','PowerWare',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (273,0,0,14,42,'爱美威','Fanday',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (275,0,0,14,43,'登高','Genesis',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (277,0,0,14,44,'ASCO','ASCO',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (279,0,0,14,45,'普赛','PS',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (281,0,0,14,46,'霍克','Huoke',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (283,0,0,14,47,'珠江','Zhujiang',TRUE,FALSE,FALSE,'珠江电源',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (285,0,0,14,48,'环宇','Huyu',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (287,0,0,14,49,'海信','Hisense',TRUE,FALSE,FALSE,'山东海信',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (289,0,0,14,50,'双宇','Shuangyu',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (291,0,0,14,51,'艾思得','Astrid',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (293,0,0,14,52,'爱克赛','Eksi',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (295,0,0,14,53,'波利','Boli',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (297,0,0,14,54,'梅兰日兰','MerlinGerin',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (299,0,0,14,55,'中创瑞普','Zcpurui',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (301,0,0,14,56,'新西兰','NewZealand',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (303,0,0,14,57,'易达','Eltek',TRUE,FALSE,FALSE,'东莞易达',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (305,0,0,14,58,'康明斯','Cummins',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (307,0,0,14,59,'朗讯','Lucent',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (309,0,0,14,60,'DIRIS','DIRIS',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (311,0,0,14,61,'阿特拉斯','Atlas',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (313,0,0,14,62,'京丰','Jingfeng',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (315,0,0,14,63,'泰兴','Taixing',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (317,0,0,14,64,'汤浅','Yuasa',TRUE,FALSE,FALSE,'包括广东汤浅、日本汤浅等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (319,0,0,14,65,'华菱','Valin',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (321,0,0,14,66,'劲达','Kingtec',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (323,0,0,14,67,'兴安','Xingan',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (325,0,0,14,68,'华宝','Huabao',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (327,0,0,14,69,'银波达','Yinboda',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (329,0,0,14,70,'卧龙','Wolong',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (331,0,0,14,71,'灯塔','Dengta',TRUE,FALSE,FALSE,'浙江卧龙灯塔',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (333,0,0,14,72,'TNB','TNB',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (335,0,0,14,73,'博尔','Boer',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (337,0,0,14,74,'艾维达','Avda',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (339,0,0,14,75,'理士','Leoch',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (341,0,0,14,76,'通力环','Tolihi',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (343,0,0,14,77,'威尔逊','Wilson',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (345,0,0,14,78,'动力源','Dynamic',TRUE,FALSE,FALSE,'北京动力源',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (347,0,0,14,79,'科龙','Kelon',TRUE,FALSE,FALSE,'广东科龙',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (349,0,0,14,80,'银泰','Intesolar',TRUE,FALSE,FALSE,'武汉银泰',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (351,0,0,14,81,'陶都','Taodu',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (353,0,0,14,82,'银河','Yinghe',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (355,0,0,14,83,'阿尔西','Airsys',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (357,0,0,14,84,'伟博','Wabertec',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (359,0,0,14,85,'昊诚','Haocheng',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (361,0,0,14,86,'能威','Newave',TRUE,FALSE,FALSE,'瑞士能威',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (363,0,0,14,87,'华富','Huafu',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (365,0,0,14,88,'JDC','JDC',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (367,0,0,14,89,'雷乐士','Riello',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (369,0,0,14,90,'HDR','HDR',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (371,0,0,14,91,'GE','GE',TRUE,FALSE,FALSE,'美国通用',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (373,0,0,14,92,'AGM','AGM',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (375,0,0,14,93,'安奈特','Allied Telesis',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (377,0,0,14,94,'优力','Unitech',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (379,0,0,14,95,'科创','Kechuang',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (381,0,0,14,96,'创科','TTI',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (383,0,0,14,97,'京丰明光','JFMG',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (385,0,0,14,98,'星恒','Xingheng',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (387,0,0,14,99,'硅能','Ledteen',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (389,0,0,14,100,'阳光','Sungrow',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (391,0,0,14,101,'凯旋','Kashinys',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (393,0,0,14,102,'特灵','Trane',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (395,0,0,14,103,'江苏通信','JSCA',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (397,0,0,14,104,'三菱电机','Mitsubishielectric',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (399,0,0,14,105,'尚灵','Sunlines',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (401,0,0,14,106,'科泰','Cooltech',TRUE,FALSE,FALSE,'上海科泰',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (403,0,0,14,107,'锦天乐','Jintianle',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (405,0,0,14,108,'金威远','Jinweiyuan',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (407,0,0,14,109,'亚奥','Asia Olympic',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (409,0,0,14,110,'永为','Yongwei',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (411,0,0,14,111,'搏力','Boli',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (413,0,0,14,112,'易事特','East',TRUE,FALSE,FALSE,'广东易事特',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (415,0,0,14,113,'依米康','Sunrise',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (417,0,0,14,114,'派诺','Polot',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (419,0,0,14,115,'恒通','Hengtong',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (421,0,0,14,116,'万里','Wanli',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (423,0,0,14,117,'威泰迅','Wontex',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (425,0,0,14,118,'海通','Haitong',TRUE,FALSE,FALSE,'江苏海通',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (427,0,0,14,119,'盾安','Dunan',TRUE,FALSE,FALSE,'浙江盾安',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (429,0,0,14,120,'新海宜','New Seaunion',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (431,0,0,14,121,'普兴','Puxing',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (433,0,0,14,122,'山特','Santak',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (435,0,0,14,123,'辛普森','Xinpusheng',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (437,0,0,14,124,'中高','Zhonggao',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (439,0,0,14,125,'科士达','Kstar',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (441,0,0,14,126,'兆宇','Zhaoyu',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (443,0,0,14,127,'派能','Pylontech',TRUE,FALSE,FALSE,'中兴派能',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (445,0,0,14,128,'天能','Tianneng',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (447,0,0,14,129,'宏通','Hongtong',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (449,0,0,14,130,'科华','Kehua',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (451,0,0,14,131,'圣阳','Sacredsun',TRUE,FALSE,FALSE,'山东圣阳',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (453,0,0,14,132,'力创','Lichuang',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (455,0,0,14,133,'武汉普天','Putian',TRUE,FALSE,FALSE,'包括洲际电源、电池等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (457,0,0,14,134,'金威源','Goldpower',TRUE,FALSE,FALSE,'深圳金威源',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (459,0,0,14,135,'通力盛达','Tonlier',TRUE,FALSE,FALSE,'北京通力盛达',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (461,0,0,14,136,'安耐特','Enatel',TRUE,FALSE,FALSE,'深圳安耐特',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (463,0,0,14,137,'新电元','Shindengenelctric',TRUE,FALSE,FALSE,'上海新电元通信设备有限公司',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (465,0,0,14,138,'长光','Yotc',TRUE,FALSE,FALSE,'武汉长光',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (467,0,0,14,139,'火炬','Torch',TRUE,FALSE,FALSE,'淄博火炬',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (469,0,0,14,140,'海霸','Haiba',TRUE,FALSE,FALSE,'山东海霸',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (471,0,0,14,141,'大力神','Technologies',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (473,0,0,14,142,'华日','Huari',TRUE,FALSE,FALSE,'山东华日',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (475,0,0,14,143,'丰日','Fengri',TRUE,FALSE,FALSE,'湖南丰日',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (477,0,0,14,144,'文隆','Wenlong',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (479,0,0,14,145,'北京汉铭','Aceway',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (481,0,0,14,146,'联动天翼','Linkdata',TRUE,FALSE,FALSE,'北京联动天翼',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (483,0,0,14,147,'比亚迪','BYD',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (485,0,0,14,148,'汇龙','Huilong',TRUE,FALSE,FALSE,'云南汇龙',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (487,0,0,14,149,'施耐德','Schneider',TRUE,FALSE,FALSE,'包括施耐德、梅兰日兰、APC等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (489,0,0,14,150,'索科曼','Socemen',TRUE,FALSE,FALSE,'包括索科曼、溯高美等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (491,0,0,14,151,'史图斯','STULZ',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (493,0,0,14,152,'西门子','Siemens',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (495,0,0,14,153,'爱维达','Evada',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (497,0,0,14,154,'伊顿','Eaton',TRUE,FALSE,FALSE,'包括Powerware、山特、伊顿等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (499,0,0,14,155,'志成冠军','zhicheng-champion',TRUE,FALSE,FALSE,'广东志成冠军',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (501,0,0,14,156,'斯泰科','CTDG',TRUE,FALSE,FALSE,'北京斯泰科',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (503,0,0,14,157,'志高','Chigo',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (505,0,0,14,158,'吉荣','Jirong',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (507,0,0,14,159,'华凌','Hualing',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (509,0,0,14,160,'约顿','Joton',TRUE,FALSE,FALSE,'上海约顿',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (511,0,0,14,161,'铨高','Renovo',TRUE,FALSE,FALSE,'珠海铨高',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (513,0,0,14,162,'融和','Rohe',TRUE,FALSE,FALSE,'北京融和',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (515,0,0,14,163,'艾苏威尔','Isuwel',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (517,0,0,14,164,'开利','Carrier',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (519,0,0,14,165,'十字军','SZJ',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (521,0,0,14,166,'道依兹','DEUTZ',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (523,0,0,14,167,'劳斯莱斯','Rolls-Royce',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (525,0,0,14,168,'威尔信','Weierxin',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (527,0,0,14,169,'泰豪','Tellhow',TRUE,FALSE,FALSE,'泰豪科技',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (529,0,0,14,170,'威能','Saonon',TRUE,FALSE,FALSE,'番禺威能',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (531,0,0,14,171,'济柴','Jicai',TRUE,FALSE,FALSE,'济南柴油机',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (533,0,0,14,172,'怡昌','Yichang',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (535,0,0,14,173,'开普','Gmeey',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (537,0,0,14,174,'南柴','Nancai',TRUE,FALSE,FALSE,'南昌柴油机',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (539,0,0,14,175,'BEST','BEST',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (541,0,0,14,176,'IMV','IMV',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (543,0,0,14,177,'丹科','Dako',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (545,0,0,14,178,'上柴','Shangcai',TRUE,FALSE,FALSE,'上海柴油机',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (547,0,0,14,179,'正和','Zhenghe',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (549,0,0,14,180,'海康','HIK',TRUE,FALSE,FALSE,'HIK',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (551,0,0,14,181,'纽贝尔','NewaBel',TRUE,FALSE,FALSE,'NewaBel',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (553,0,0,15,0,'未知','Unknown',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (555,0,0,16,0,'未知','Unknown',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (557,0,0,17,1,'模拟信号','Analog Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (559,0,0,17,2,'开关信号','Switch Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (561,0,0,17,3,'图像信号','Image Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (563,0,0,18,1,'采集信号','Sample Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (565,0,0,18,2,'虚拟信号','Virtual Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (567,0,0,18,3,'常量信号','Constant Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (569,0,0,19,0,'统计最大值','Statistic Max',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (571,0,0,19,1,'统计最小值','Statistic Min',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (573,0,0,19,2,'统计平均值','Statistic Average',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (575,0,0,19,3,'由事件产生','Triggered by Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (577,0,0,19,4,'超过阀值产生','Triggered By Threshold',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (579,0,0,19,5,'周期存储','Save Periodically',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (581,0,0,19,6,'抄表信号','Record Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (583,0,0,19,7,'定点采集','Sampled by Timing',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (585,0,0,20,1,'V','V',TRUE,TRUE,FALSE,'电位、电压、电动势',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (587,0,0,20,2,'kV','kV',TRUE,TRUE,FALSE,'高压',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (589,0,0,20,3,'A','A',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (591,0,0,20,4,'Hz','Hz',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (593,0,0,20,5,'PF','PF',TRUE,TRUE,FALSE,'功率因数PF',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (595,0,0,20,6,'W','W',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (597,0,0,20,7,'kW','kW',TRUE,TRUE,FALSE,'有功功率、辐射通量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (599,0,0,20,8,'MW','MW',TRUE,TRUE,FALSE,'光伏',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (601,0,0,20,9,'kWh','kWh',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (603,0,0,20,10,'Var','Var',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (605,0,0,20,11,'kVar','kVar',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (607,0,0,20,12,'Varh','Varh',TRUE,TRUE,FALSE,'无功电量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (609,0,0,20,13,'kVarh','kVarh',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (611,0,0,20,14,'VA','VA',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (613,0,0,20,15,'kVA','kVA',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (615,0,0,20,16,'℃','℃',TRUE,TRUE,FALSE,'如果资料为华氏度，需协议转换为摄氏度',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (617,0,0,20,17,'%RH','%RH',TRUE,TRUE,FALSE,'相对湿度(Relative Humidity)',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (619,0,0,20,18,'r/min','r/min',TRUE,TRUE,FALSE,'风扇转速',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (621,0,0,20,19,'kPa','kPa',TRUE,TRUE,FALSE,'压缩机X压力、油压、压强、应力',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (623,0,0,20,20,'MPa','MPa',TRUE,TRUE,FALSE,'冷凝器进水压力，由协议统一，同类设备、信号使用一种',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (625,0,0,20,21,'N','N',TRUE,TRUE,FALSE,'力、重力',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (627,0,0,20,22,'L','L',TRUE,TRUE,FALSE,'燃油剩余容量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (629,0,0,20,23,'Ah','Ah',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (631,0,0,20,24,'%','%',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (633,0,0,20,25,'%','%',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (635,0,0,20,26,'Y','Y',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (637,0,0,20,27,'M','M',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (639,0,0,20,28,'D','D',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (641,0,0,20,29,'h','h',TRUE,TRUE,FALSE,'累计运行时间',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (643,0,0,20,30,'min','min',TRUE,TRUE,FALSE,'电池后备时间',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (645,0,0,20,31,'sec','sec',TRUE,TRUE,FALSE,'压缩机重开保护时间',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (647,0,0,20,32,'kΩ','kΩ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (649,0,0,20,33,'mΩ','mΩ',TRUE,TRUE,FALSE,'电池内阻',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (651,0,0,20,34,'μF','μF',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (653,0,0,20,35,'H','H',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (655,0,0,20,36,'m','m',TRUE,TRUE,FALSE,'长度、高度',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (657,0,0,20,37,'kg','kg',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (659,0,0,20,38,'lx','lx',TRUE,TRUE,FALSE,'衡量单位面积的光通量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (661,0,0,20,39,'lm','lm',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (663,0,0,20,40,'pcs','pcs',TRUE,TRUE,FALSE,'个',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (665,0,0,20,41,'W/m2','W/m2',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (667,0,0,20,42,'m/s','m/s',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (669,0,0,20,43,'Nm','Nm',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (671,0,0,20,44,'m3','m3',TRUE,TRUE,FALSE,'容积、风量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (673,0,0,20,45,'S','S',TRUE,TRUE,FALSE,'电池电导',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (675,0,0,20,46,'MWh','MWh',TRUE,TRUE,FALSE,'累计电能',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (677,0,0,21,1,'状态信号','State Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (679,0,0,21,2,'抄表信号','Record Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (681,0,0,21,3,'门禁信号','Door Access Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (683,0,0,21,4,'电池后备时间','Battery Stand by Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (685,0,0,21,5,'电池总电压信号','Battery Total Voltage Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (687,0,0,21,6,'模块输出电压','Module Output Voltage',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (689,0,0,21,7,'模块输出电流','Module Input Current',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (691,0,0,21,8,'电表读表信号','Ammeter Record Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (693,0,0,21,9,'CPU使用率','CPU Usage Rate',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (695,0,0,21,10,'内存使用率','Memory Usage Rage',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (697,0,0,21,11,'通讯状态','Communication State',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (699,0,0,21,12,'主机接入','Host Connected',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (701,0,0,21,13,'门开关状态信号','Door Access Switch State Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (703,0,0,21,14,'市电状态信号','Power State Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (705,0,0,21,15,'图像明亮度信号','Image Brightness Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (707,0,0,21,16,'图像对比度信号','Image Contrast Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (709,0,0,21,17,'图像流信号','Image Stream Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (711,0,0,21,18,'图像帧率信号','Image FPS Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (713,0,0,21,19,'落后电池信号','Bad Cell Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (715,0,0,21,20,'电池电流信号','Battery Current Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (717,0,0,21,22,'电池油机工作状态信号','Battery Diesel Work State Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (719,0,0,21,23,'告警可屏蔽','Suppressable Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (721,0,0,21,24,'告警屏蔽','Event Suppression',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (723,0,0,21,25,'重要信号','Important Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (725,0,0,21,27,'可视信号','Visible Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (727,0,0,21,28,'诊断信号','Diagnostic Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (729,0,0,21,30,'单体电池电压信号','Cell Voltage Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (731,0,0,21,31,'电池组剩余容量','Battery Pack Remaining Capacity',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (733,0,0,21,32,'来电提示','Incoming Call Alert',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (735,0,0,21,33,'数据库连接信号','Database Connection Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (737,0,0,21,34,'用电量','Energy',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (739,0,0,21,35,'放电测试状态','Discharge Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (741,0,0,21,36,'设备安装状态','Equipment Fixed Status',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (743,0,0,21,37,'温度信号','Temperature Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (745,0,0,21,38,'DI_DOOR','DI_DOOR',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (747,0,0,21,39,'DI_SMOKE','DI_SMOKE',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (749,0,0,21,40,'DI_LEAK','DI_LEAK',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (751,0,0,21,41,'DI_MOTION','DI_MOTION',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (753,0,0,21,42,'DI_VIBRA','DI_VIBRA',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (755,0,0,21,43,'DI_OTHER','DI_OTHER',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (757,0,0,21,44,'DO_SOUND','DO_SOUND',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (759,0,0,21,45,'DO_LIGHT','DO_LIGHT',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (761,0,0,21,46,'DO_FAN','DO_FAN',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (763,0,0,21,47,'整流器有电状态','Power State Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (765,0,0,21,48,'湿度','Humidity',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (767,0,0,21,49,'DI_MicroWave','DI_MicroWave',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (769,0,0,21,50,'功耗信号','Power Consumption',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (771,0,0,21,51,'负载电流','LoadCurrent',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (773,0,0,21,52,'三相输入Uab','Three-phase input Uab',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (775,0,0,21,53,'三相输入Ubc','Three-phase inputUbc',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (777,0,0,21,54,'三相输入Uac','Three-phase input Uac',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (779,0,0,21,55,'三相输出Uab','Three-phase output Uab',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (781,0,0,21,56,'三相输出Ubc','Three-phase output Ubc',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (783,0,0,21,57,'三相输出Uac','Three-phase output Uac',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (785,0,0,21,58,'输出线电流Ia','Ouput Line Current Ia',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (787,0,0,21,59,'输出线电流Ib','Ouput Line Current Ib',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (789,0,0,21,60,'输出线电流Ic','Ouput Line Current  Ic',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (791,0,0,21,61,'开关电源系统电压','Rectifier System Voltage',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (793,0,0,21,62,'节能状态','Energy State',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (795,0,0,21,63,'整点抄表信号','Dot Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (797,0,0,21,64,'相电流Ia','Phase Current Ia',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (799,0,0,21,65,'相电流Ib','Phase Current Ib',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (801,0,0,21,66,'相电流Ic','Phase Current Ic',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (803,0,0,21,67,'A相保障负荷电流','A-Phase Protection Of current',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (805,0,0,21,68,'B相保障负荷电流','B-Phase Protection Of current',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (807,0,0,21,69,'C相保障负荷电流','C-Phase Protection Of current',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (809,0,0,21,70,'开关位置','Switch position',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (811,0,0,21,71,'有功功率','Active Power',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (813,0,0,21,72,'电池保障时长','Battery  Protected Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (815,0,0,21,73,'落后电池总节数','Bad Cell Total Count',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (817,0,0,21,74,'剩余容量','Remaining Capacity',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (819,0,0,21,75,'已经放电时间','Discharge Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (821,0,0,21,76,'休眠状态','Dormancy Status',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (823,0,0,21,77,'虚拟电表用电量','Virtual Ammeter  Consumption',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (825,0,0,21,78,'停止放电原因','Stop  Discharge Reason',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (827,0,0,21,79,'上次电池测试容量','Last Battery Test Compacity',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (829,0,0,22,1,'模拟量','Analog',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (831,0,0,22,2,'数字量','State',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (833,0,0,23,0,'四级告警','Level 4',TRUE,TRUE,FALSE,'','EventSeverity0.png','EventSeverity0.mp3','#77BDF8','4',NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (835,0,0,23,1,'三级告警','Level 3',TRUE,TRUE,FALSE,'','EventSeverity1.png','EventSeverity1.mp3','#EDD951','3',NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (837,0,0,23,2,'二级告警','Level 2',TRUE,TRUE,FALSE,'','EventSeverity2.png','EventSeverity2.mp3','#F39924','2',NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (839,0,0,23,3,'一级告警','Level 1',TRUE,TRUE,FALSE,'','EventSeverity3.png','EventSeverity3.mp3','#ff2626','1',NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (841,0,0,24,1,'系统事件','System Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (843,0,0,24,2,'设备事件','Equipment Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (845,0,0,24,3,'状态事件','State Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (847,0,0,24,4,'刷卡事件','Swipe Card Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (849,0,0,24,5,'烟感事件','Fire Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (851,0,0,24,6,'自诊断事件','Self-Diagnostic Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (853,0,0,24,7,'通讯状态事件','Communication State Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (855,0,0,24,8,'主机接入事件','Host Connected Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (857,0,0,24,9,'撤防','Disarming',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (859,0,0,24,10,'停电','Power Cut',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (861,0,0,24,11,'一次下电事件','Main Loop Power Cut',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (863,0,0,24,12,'监控中断','Monitoring Interruption',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (865,0,0,24,13,'门事件','Door Access Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (867,0,0,24,14,'空调温度预警','Air-Condition Temperature Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (869,0,0,24,15,'空调压缩机预警','Air-Condition Compressor Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (871,0,0,24,16,'开关电源模块电流预警','Switch Power Module Current Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (873,0,0,24,17,'开关电源负载电流预警','Switch Power Load Current Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (875,0,0,24,18,'单体电池放电预警','Cell Discharge Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (877,0,0,24,19,'单体电池充电预警','Cell Charge Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (879,0,0,24,20,'电缆负载电流预警','Power Cable Load Current Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (881,0,0,24,21,'UPS单机容量预警','UPS Stand-alone Capacity Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (883,0,0,24,22,'UPS单机电流预警','UPS Stand-alone Current Early-warning',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (885,0,0,24,23,'局站撤防','Station Disarming',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (887,0,0,24,24,'端局断站事件','Station Interruption Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (889,0,0,24,25,'License无效事件','License Invalid Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (891,0,0,24,26,'大规模停电事件','Large Scale Power Cut Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (893,0,0,24,27,'数据库连接中断事件','Database Connection Interruption Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (895,0,0,24,28,'设备有效状态事件 ','Equipment Valid State Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (897,0,0,24,29,'单体落后电池事件','Bad Cell Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (899,0,0,24,30,'采集器通讯状态事件','Sampler Communication State Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (901,0,0,24,31,'直流电源告警','DC Power Event',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (903,0,0,24,32,'防盗告警','Anti-theft Event',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (905,0,0,24,33,'总电压告警','Total voltage Event',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (907,0,0,24,34,'温度告警','Temperature   Event',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (909,0,0,24,35,'门碰告警','Door Access Event',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (911,0,0,24,36,'红外告警','Infrared Event',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (913,0,0,24,37,'水浸告警','Flood Event',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (915,0,0,24,38,'非Vertiv采集器接入事件','Non-Vertiv Sampler Connected Event',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (917,0,0,24,39,'接入监控单元数超过License限制','Access Monitoring Unit Over License Limit',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (919,0,0,24,40,'设备未安装事件','Equipment Non-Fixed Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (921,0,0,24,41,'设备安装事件','Equipment Fixed Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (923,0,0,24,42,'油机供电','Machine Generating',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (925,0,0,24,43,'放电结束事件','DisCharge Finish Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (927,0,0,24,44,'湿度告警','Humidity  Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (929,0,0,24,45,'空调设备事件','AirCondition Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (931,0,0,24,46,'单体电压事件','Cell Voltage Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (933,0,0,24,47,'电池温度事件','Battery Temperature Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (935,0,0,24,48,'电表电压事件','Ammeter Voltage Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (937,0,0,24,49,'市电开关事件','Power Status Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (939,0,0,24,50,'开关电源事件','Rectifier Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (941,0,0,24,51,'非法开门事件','Illegal Door Open Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (943,0,0,24,52,'超长门开事件','Door Opening long Time Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (945,0,0,24,53,'专用空调事件','Special Air-Condition Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (947,0,0,24,54,'自动重合闸设备事件','Autoreclose circuit breaker',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (949,0,0,24,55,'油机事件','Generator Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (951,0,0,24,56,'UPS事件','UPS Event ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (953,0,0,24,57,'直流电源输出电压告警','DC Voltage Output  Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (955,0,0,24,58,'直流电源交流输出事件','AC Voltage Output  Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (957,0,0,24,59,'开关电源休眠','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (959,0,0,24,60,'模块休眠','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (961,0,0,24,61,'电池组单体状态','Battery Cell Voltage Status',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (963,0,0,24,62,'数据库空间不足告警','Database FreeSize not Enough',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (965,0,0,24,63,'设备通讯状态事件','Equipment Communication State Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (967,0,0,25,1,'条件事件','Conditional Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (969,0,0,25,2,'非条件事件','Non-conditional Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (971,0,0,26,1,'脉冲事件','Pulse Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (973,0,0,26,2,'间隔事件','Interval Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (975,0,0,26,3,'持续事件','Continual Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (977,0,0,27,0,'事件开始','Event Start',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (979,0,0,27,1,'事件结束','Event End',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (981,0,0,27,4,'事件升级','Event Upgrade',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (983,0,0,28,1,'一般','General',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (985,0,0,28,2,'重要','Important',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (987,0,0,28,3,'紧急','Critical',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (989,0,0,29,1,'待命','Stand by',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (991,0,0,29,2,'正执行中','Executing',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (993,0,0,29,3,'异常','Exception',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (995,0,0,29,4,'完成','Success',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (997,0,0,30,1,'控制成功','Control Success',TRUE,TRUE,FALSE,'','commandstatus_execute.gif',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (999,0,0,30,2,'控制失败','Control Failure',TRUE,TRUE,FALSE,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1001,0,0,30,3,'处理超时','Process Timeout',TRUE,TRUE,FALSE,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1003,0,0,30,4,'未返回','Return Failure',TRUE,TRUE,FALSE,'','commandstatus_execute.gif',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1005,0,0,30,5,'控制单元地址错误','Control Unit Address Error',TRUE,TRUE,FALSE,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1007,0,0,30,6,'参数错误','Parameter Error',TRUE,TRUE,FALSE,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1009,0,0,30,7,'在控制队列中超时','Timeout in the Control Queue',TRUE,TRUE,FALSE,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1011,0,0,30,8,'录像结束','Record Over',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1013,0,0,31,0,'未设定','Unknown',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1015,0,0,31,1,'普通控制','General Control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1017,0,0,31,2,'开关门控制','Switch Door Access',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1019,0,0,31,3,'图像上移','Up',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1021,0,0,31,4,'图像下移','Down',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1023,0,0,31,5,'图像左移','Left',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1025,0,0,31,6,'图像右移','Right',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1027,0,0,31,7,'图像前移','Forward',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1029,0,0,31,8,'图像后移','Backward',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1031,0,0,31,9,'图像亮度控制','Brightness',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1033,0,0,31,10,'图像对比度控制','Contrast',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1035,0,0,31,11,'图像帧率控制','Fps',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1037,0,0,31,12,'增加门禁卡','Add Door Access Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1039,0,0,31,13,'删除门禁卡','Delete Door Access Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1041,0,0,31,14,'修改门禁卡设置','Modify Door Access Card Setting',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1043,0,0,31,15,'设置星期准进时间段 ','Set Week Ingress Time Period',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1045,0,0,31,16,'修改验证控制密码','Modify Control Privilege Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1047,0,0,31,17,'删除所有门禁卡','Delete All Door Access Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1049,0,0,31,18,'布防红外','Deploy Infrared',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1051,0,0,31,19,'撤防红外','Disarm Infrared',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1053,0,0,31,20,'开门超时时间','Door Open Timeout',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1055,0,0,31,21,'刷卡进门密码工作方式','Swipe Card Entry Password Work Mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1057,0,0,31,22,'设置时间','Set Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1059,0,0,31,23,'非法开门告警结束确定命令','Illegal Door Open Event End Confirm Command',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1061,0,0,31,24,'放电测试','Discharge Test ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1063,0,0,31,25,'停止放电测试','Stop  Discharge Test ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1065,0,0,31,26,'告警录像控制命令','Event Record Control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1067,0,0,31,27,'放电终止电压设置','Set Discharge Terminate Voltage',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1069,0,0,31,28,'放电终止时长设置','Set Discharge Terminate Period',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1071,0,0,32,1,'遥调','Remote Regulating',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1073,0,0,32,2,'遥控','Remote Control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1075,0,0,33,1,'联网模式','Network Mode',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1077,0,0,33,2,'单机模式','Stand-alone Mode',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1079,0,0,33,3,'测试模式','Test Mode',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1081,0,0,34,1,'RMU下MU','MU of RMU',TRUE,TRUE,FALSE,'','mu.png','4',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1083,0,0,34,2,'IDU','IDU',TRUE,TRUE,FALSE,'','idu.png','11',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1085,0,0,34,4,'IPLU','IPLU',TRUE,TRUE,FALSE,'','iplu.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1087,0,0,34,5,'IMU','IMU',TRUE,TRUE,FALSE,'','IMU.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1089,0,0,34,6,'eStone','eStone',TRUE,TRUE,FALSE,'','eStone.png','14',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1091,0,0,34,7,'IDU-X','IDU-X',TRUE,TRUE,FALSE,'','IDUX.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1093,0,0,34,8,'ISU','ISU',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1095,0,0,34,9,'FSU','FSU',TRUE,TRUE,FALSE,'',NULL,'13',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1097,0,0,34,10,'BInterface','BInterface',TRUE,TRUE,FALSE,'',NULL,'15',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1099,0,0,34,11,'Catcher','Catcher',TRUE,TRUE,FALSE,'',NULL,'16',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1101,0,0,34,12,'GFSU','GFSU',TRUE,TRUE,FALSE,'',NULL,'1',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1103,0,0,34,14,'ISUV2','ISUV2',TRUE,TRUE,FALSE,'',NULL,'3',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1105,0,0,34,16,'WorkStation','WorkStation',TRUE,TRUE,FALSE,'',NULL,'5',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1107,0,0,34,17,'ECG','ECG',TRUE,TRUE,FALSE,'',NULL,'6',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1109,0,0,35,1,'真实MU','Pysical MU',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1111,0,0,35,2,'虚拟MU','Virtual MU',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1113,0,0,36,1,'自启动','Start Automatically',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1115,0,0,36,2,'停机','Halt',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1117,0,0,36,3,'故障','Fault',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1119,0,0,36,4,'远程控制','Remote Control',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1121,0,0,37,0,'自诊断采集器','Self-Diagnostic Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1123,0,0,37,1,'AMS采集器','AMS Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1125,0,0,37,2,'OCE采集器','OCE Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1127,0,0,37,3,'IDA-AI','IDA-AI',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1129,0,0,37,4,'IDA-DI','IDA-DI',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1131,0,0,37,5,'IDA-DO','IDA-DO',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1133,0,0,37,6,'IDA-BAT','IDA-BAT',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1135,0,0,37,7,'IDA-I0','IDA-I0',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1137,0,0,37,8,'SDA-OCE','SDA-OCE',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1139,0,0,37,9,'SDA-IO','SDA-IO',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1141,0,0,37,10,'IDA-SIO','IDA-SIO',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1143,0,0,37,13,'IDU_IO','IDU_IO',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1145,0,0,37,14,'IDU_BAT','IDU_BAT',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1147,0,0,37,11,'IDU_DOOR(IDU一体化门禁)','IDU_DOOR(IDU Integrative Door Access)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1149,0,0,37,12,'DOOR(普通门禁)','DOOR(General Door Access)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1151,0,0,37,15,'简单逻辑控制采集器','Simple Logic Control Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1153,0,0,37,16,'图像采集器','Image Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1155,0,0,37,17,'IOLanplus','IO-Lan plus',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1157,0,0,37,18,'普通采集器','General Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1159,0,0,37,19,'大规模停电采集器','Large Scale Power Cut Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1161,0,0,37,20,'USB Key丢失采集器','USB Key Unfound Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1163,0,0,37,21,'端局断站生成采集器','Station Interruption Generation Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1165,0,0,37,22,'IDU自诊断采集器','IDU Self-diagnostic Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1167,0,0,37,23,'IPLU自诊断采集器','IPLU Self-diagnostic Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1169,0,0,37,24,'eStone自诊断采集器','eStone Self-diagnostic Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1171,0,0,37,25,'EEM-Sampler ','EEM-Sampler ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1173,0,0,37,26,'EEM-RI-Sampler','EEM-RI-Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1175,0,0,37,27,'EEM-Diagnose-Sampler','EEM-Diagnose-Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1177,0,0,37,28,'IMU采集器','IMU Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1179,0,0,37,29,'微站采集器','Micro Station Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1181,0,0,37,30,'ISU自诊断采集器','ISU Self-diagnostic Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1183,0,0,38,0,'自诊断(RMS)','Self-diagnostic(RMS)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1185,0,0,38,1,'采集单元(AMS)','Sampler Unit(AMS)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1187,0,0,38,2,'采集单元(OCE)','Sampler Unit(OCE)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1189,0,0,38,3,'采集单元(普通)','Sampler Unit(General)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1191,0,0,39,1,'标准串口','Standard Serial Port',TRUE,TRUE,FALSE,'','1',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1193,0,0,39,2,'SNU口','SNU Port',TRUE,TRUE,FALSE,'','52',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1195,0,0,39,3,'SNMP口','SNMP Port',TRUE,TRUE,FALSE,'','3',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1197,0,0,39,4,'PSTN巡检口','PSTN Patrol Port',TRUE,TRUE,FALSE,'','53',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1199,0,0,39,5,'虚拟端口','Virtual Port',TRUE,TRUE,FALSE,'','2',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1201,0,0,39,6,'终端服务器口','Terminal Server Port',TRUE,TRUE,FALSE,'','20',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1203,0,0,39,7,'PSTN告警回叫口','PSTN Event Callback Port',TRUE,TRUE,FALSE,'','54',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1205,0,0,39,8,'PSTN手动维护口','PSTN Maintenance Port',TRUE,TRUE,FALSE,'','55',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1207,0,0,39,9,'OMC口','OMC Port',TRUE,TRUE,FALSE,'','56',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1209,0,0,39,10,'系统接入口','System Access Port',TRUE,TRUE,FALSE,'','57',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1211,0,0,39,11,'ISDN拨号备份口','ISDN Dial-up Standby Port',TRUE,TRUE,FALSE,'','58',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1213,0,0,39,12,'专线备份口','Special Line Standby Port',TRUE,TRUE,FALSE,'','59',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1215,0,0,39,13,'IDU-IP口','IDU-IP port',TRUE,TRUE,FALSE,'','60',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1217,0,0,39,14,'IDU-SMS口','IDU-SMS Port',TRUE,TRUE,FALSE,'','61',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1219,0,0,39,15,'IDU-IP-SMS口','IDU-IP-SMS Port',TRUE,TRUE,FALSE,'','62',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1221,0,0,39,16,'IDU-Serial口','IDU-Serial Port',TRUE,TRUE,FALSE,'','63',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1223,0,0,39,17,'DTU口','DTU Port',TRUE,TRUE,FALSE,'','64',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1225,0,0,39,18,'Host口','Host Port',TRUE,TRUE,FALSE,'','65',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1227,0,0,39,19,'简单逻辑控制口','Simple Logic Control Port',TRUE,TRUE,FALSE,'','5',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1229,0,0,39,20,'IP巡检口     ','IP Patrol Port',TRUE,TRUE,FALSE,'','66',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1231,0,0,39,21,'IP告警回叫口 ','IP Event Callback Port',TRUE,TRUE,FALSE,'','67',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1233,0,0,39,22,'IP手动维护口 ','IP Maintenance Port',TRUE,TRUE,FALSE,'','68',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1235,0,0,39,23,'GPRS巡检口     ','GPRS Patrol Port',TRUE,TRUE,FALSE,'','69',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1237,0,0,39,24,'GPRS告警回叫口','GPRS Event Callback Port',TRUE,TRUE,FALSE,'','70',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1239,0,0,39,25,'GPRS手动维护口 ','GPRS Maintenance Port',TRUE,TRUE,FALSE,'','71',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1241,0,0,39,26,'GSM巡检口     ','GSM Patrol Port',TRUE,TRUE,FALSE,'','72',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1243,0,0,39,27,'GSM告警回叫口 ','GSM Event Callback Port',TRUE,TRUE,FALSE,'','73',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1245,0,0,39,28,'GSM手动维护口','GSM Maintenance Port',TRUE,TRUE,FALSE,'','75',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1247,0,0,39,29,'I2C端口','I2C Port',TRUE,TRUE,FALSE,'','76',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1249,0,0,39,30,'MDU端口','MDU Port',TRUE,TRUE,FALSE,'','7',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1251,0,0,39,31,'移动B接口门禁透传端口','CMCC BInterface Access Control Port',TRUE,TRUE,FALSE,'','6',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1253,0,0,40,1,'邮件（Notes）','Email(Notes)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1255,0,0,40,2,'短信（GSM Modem）','ShortMessage(GSM Modem)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1257,0,0,40,3,'语音','Text to Speech',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1259,0,0,40,4,'打印机','Printer',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1261,0,0,40,5,'日志','Log',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1263,0,0,40,6,'传真机','Fax',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1265,0,0,40,7,'屏幕','Screen',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1267,0,0,40,8,'短信（infoX-MAS-SOAP）','ShortMessage(infoX-MAS-SOAP)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1269,0,0,40,9,'短信（infoX-MAS-DLL）','ShortMessage(infoX-MAS-DLL)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1271,0,0,41,1,'普通员工','General Employee',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1273,0,0,41,2,'系统操作员','System Operator',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1275,0,0,41,3,'系统维护员','System Maintenance Operator',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1277,0,0,41,4,'系统管理员','System Administrator',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1279,0,0,42,1,'工程师','Engineer',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1281,0,0,42,2,'业务员','Business Person',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1283,0,0,42,3,'操作员','Operator',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1285,0,0,43,1,'管理','Management',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1287,0,0,43,2,'操作','Operation',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1289,0,0,43,3,'业务','Business',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1291,0,0,44,1,'VIP片区','VIP Area',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1293,0,0,44,2,'普通片区','General Area',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1295,0,0,44,3,'边远片区','Boundary Area',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1297,0,0,44,4,'SVIP片区','SVIP Area',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1299,0,0,45,1,'配置日志','Configuration Log',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1301,0,0,45,2,'操作日志','Operation Log',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1303,0,0,45,3,'业务日志','Business Log',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1305,0,0,46,1,'使用','Using',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1307,0,0,46,2,'挂失','Lost',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1309,0,0,46,3,'作废','Cancel',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1311,0,0,47,1,'管理卡','Management Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1313,0,0,47,2,'维护卡','Maintenance Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1315,0,0,47,3,'操作卡','Operation Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1317,0,0,47,4,'普通卡','General Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1319,0,0,48,1,'进出不要密码','Enter or Exit without Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1321,0,0,48,2,'进出都要密码','Enter or Exit with Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1323,0,0,48,3,'进要密码','Enter with a Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1325,0,0,48,4,'出要密码','Exit  with a Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1327,0,0,49,0,'按钮出门','Exit by Pressing Button',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1329,0,0,49,1,'合法卡','Valid Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1331,0,0,49,2,'过期卡','Unauthorized',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1333,0,0,49,3,'非法时区卡','Invalid Time Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1335,0,0,49,4,'密码错误卡','Password Wrong Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1337,0,0,49,5,'非法门区卡','Invalid Door Region Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1339,0,0,49,6,'非法卡','Invalid Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1341,0,0,49,11,'用户号加密码开门','By ID and password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1343,0,0,50,1,'空调','Air-Condition',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1345,0,0,50,2,'开关电源','Switch Power',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1347,0,0,50,3,'电池','Battery',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1349,0,0,50,4,'电缆','Cable',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1351,0,0,50,5,'UPS','UPS',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1353,0,0,51,1,'分布式服务','Distributed Service',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1355,0,0,51,2,'数据复制服务','Data Replicate Service',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1357,0,0,51,3,'数据备份服务','Data Backup Service',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1359,0,0,51,4,'数据恢复服务','Data Restore Service',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1361,0,0,51,5,'数据分析服务','Data Analysis Service',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1363,0,0,52,1,'自动执行','Execute Automatically',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1365,0,0,52,2,'手动执行','Execute Manually',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1367,0,0,52,3,'自动手动均可','Execute Automatically or Manually',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1369,0,0,53,1,'按秒','In Seconds',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1371,0,0,53,2,'按分','In Minutes',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1373,0,0,53,3,'按小时','Hourly',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1375,0,0,53,4,'按天','Daily',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1377,0,0,53,5,'按月','Monthly',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1379,0,0,54,1,'整表处理','Whole Table Process',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1381,0,0,54,2,'分时间处理','Period Process',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1383,0,0,55,-12,'定制的蓄电池管理报表','Customize Battery Management',TRUE,TRUE,FALSE,'','noimage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1385,0,0,55,-11,'四川定制报表','SiChuan Customize',TRUE,TRUE,FALSE,'','noimage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1387,0,0,55,-10,'高低压配电管理','High And Low Voltage Distribution',TRUE,TRUE,FALSE,'','PowerDistributeReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1389,0,0,55,-9,'告警管理','Event Management',TRUE,TRUE,FALSE,'','AlarmReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1391,0,0,55,-8,'安全管理','Security Management',TRUE,TRUE,FALSE,'','SecurityReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1393,0,0,55,-7,'考核报表','Assessment Report',TRUE,TRUE,FALSE,'','CheckWorkReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1395,0,0,55,-6,'遥测数据分析','Telemetry Data Analysis',TRUE,TRUE,FALSE,'','RemoteControlReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1397,0,0,55,0,'无分类报表','Ungrouped Report',TRUE,TRUE,FALSE,'','NoTypeReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1399,0,0,55,1,'人员管理','User Management',TRUE,FALSE,FALSE,'','usermanage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1401,0,0,55,2,'设备管理','Equipment Management',TRUE,FALSE,FALSE,'','equipmentmanage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1403,0,0,55,3,'门禁管理','Door Access Management',TRUE,FALSE,FALSE,'','janitormanage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1405,0,0,55,4,'运行维护','Running Maintenance',TRUE,FALSE,FALSE,'','runmaintenance.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1407,0,0,55,6,'配置信息','Configuration Asset',TRUE,FALSE,FALSE,'','configurecapital.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1409,0,0,55,7,'系统报表','System Report',TRUE,FALSE,FALSE,'','SystemReportCategory.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1411,0,0,55,5,'电池管理','Battery Management',TRUE,FALSE,FALSE,'','BatteryManage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1413,0,0,55,8,'能源管理','PowerConsumption ManageMent',TRUE,FALSE,FALSE,'','noimage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1415,0,0,55,9,'告警标准化报表','Event Standardization',TRUE,TRUE,FALSE,'','StdAlarmReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1417,0,0,56,1,'年报表','Yearly Report',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1419,0,0,56,2,'月报表','Monthly Report',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1421,0,0,56,3,'日报表','Daily Report',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1423,0,0,56,4,'时报表','Hourly Report',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1425,0,0,56,5,'分报表','Minutely Report',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1427,0,0,56,6,'秒报表','Secondly Report',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1429,0,0,56,7,'时间段报表','Period Report',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1431,0,0,56,8,'无时间分类报表','Time-independent Report',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1433,0,0,57,1,'数据表','Record Table',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1435,0,0,57,2,'饼状图','Pie Chart',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1437,0,0,57,3,'柱状图','Column Chart',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1439,0,0,57,4,'折线图','Curve Chart',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1441,0,0,57,5,'混合表','Mix Table',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1443,0,0,57,6,'矩阵表','Matrix Table',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1445,0,0,58,1,'应用服务器','Application Server',TRUE,TRUE,FALSE,'','ApplicationServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1447,0,0,58,2,'数据服务器','Data Server',TRUE,TRUE,FALSE,'','DataServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1449,0,0,58,3,'数据服务控制器','Data Server Controller',TRUE,TRUE,FALSE,'','DataServerController.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1451,0,0,58,4,'地图服务器','Map Server',TRUE,TRUE,FALSE,'','MapServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1453,0,0,58,5,'报表服务器','Report Server',TRUE,TRUE,FALSE,'','ReportServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1455,0,0,58,6,'数据库服务器','Database Server',TRUE,TRUE,FALSE,'','DatabaseServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1457,0,0,58,7,'远程应用服务器','Remote Application Server',TRUE,TRUE,FALSE,'','ApplicationServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1459,0,0,58,8,'RMU','Real-time Monitoring Unit',TRUE,TRUE,FALSE,'','FrontDataServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1461,0,0,58,9,'Web服务器','Web Server',TRUE,TRUE,FALSE,'','WebServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1463,0,0,58,11,'组态服务器','Configurable Graphic Server',TRUE,TRUE,FALSE,'','ConfigGraphicServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1465,0,0,58,12,'图像服务器','Video Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1467,0,0,58,13,'C接口服务器','C-Interface Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1469,0,0,58,14,'通知主机服务器','Notification Host Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1471,0,0,58,15,'综合资源同步服务器','Resource synchronization Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1473,0,0,58,16,'通知服务器','Notification Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1475,0,0,58,20,'业务服务器','BS Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1477,0,0,58,21,'告警复制服务器','Alarm Copy Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1479,0,0,58,22,'后台服务器','BackGround Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1481,0,0,58,23,'实时数据服务器','RealTime Data Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1483,0,0,58,24,'手机接口服务器','Mobile Interface Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1484,0,0,58,25,'定时任务服务器','Scheduled Task Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1485,0,0,58,31,'应用服务器II','Application ServerII',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1487,0,0,58,32,'业务服务器II','BS ServerII',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1489,0,0,58,101,'B接口服务器','B-Interface Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1491,0,0,59,1,'运行','Running',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1493,0,0,59,2,'停机','Halt',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1495,0,0,59,3,'故障','Fault',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1497,0,0,60,1,'TCP','TCP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1499,0,0,60,2,'UDP','UDP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1501,0,0,60,3,'HTTP','HTTP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1503,0,0,60,4,'SMS','SMS',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1505,0,0,60,5,'MSMQ','MSMQ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1507,0,0,61,1,'心跳','Heartbeat',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1509,0,0,61,2,'事件','Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1511,0,0,61,3,'实时','Real-time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1513,0,0,61,4,'历史','History',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1515,0,0,61,5,'请求','Request',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1517,0,0,61,6,'响应','Reponse',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1519,0,0,63,1,'PDF','PDF',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1521,0,0,63,2,'EXCEL','EXCEL',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1523,0,0,63,3,'HTML','HTML',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1525,0,0,63,4,'CSV','CSV',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1527,0,0,63,5,'RTF','RTF',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1529,0,0,64,1,'从不执行','Execute Never',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1531,0,0,64,2,'仅执行一次','Execute Once',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1533,0,0,64,3,'总是执行','Execute Forever',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1535,0,0,65,1,'共享模式','Concurrent Mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1537,0,0,65,2,'独占模式','Exclusive Mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1539,0,0,66,1,'串行模式','Serial Mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1541,0,0,66,2,'并行模式','Parallel Mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1543,0,0,67,1,'全时段屏蔽','Deactivate Daily ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1545,0,0,67,2,'分时段屏蔽','Deactivate Weekly',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1547,0,0,68,1,'文本框','Textbox',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1549,0,0,68,2,'数字框','Numerical Box',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1551,0,0,68,3,'滚动条','Scroll Bar',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1553,0,0,68,4,'下拉框','ComboBox',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1555,0,0,68,5,'日期','Date',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1557,0,0,69,1,'事件开始','Event Start',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1559,0,0,69,2,'事件结束','Event End',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1561,0,0,69,3,'开始确认','Start Confirm',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1563,0,0,69,4,'结束确认','End Confirm',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1565,0,0,69,5,'事件升级','Event Upgrade',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1567,0,0,70,0,'浮点型(FLOAT)','Float',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1569,0,0,70,1,'字符串(STRING)','String',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1571,0,0,70,2,'密码类型','Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1573,0,0,71,1,'基站','Site',TRUE,TRUE,FALSE,'','BigStation.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1575,0,0,71,2,'机房','Station',TRUE,TRUE,FALSE,'','SmallStation.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1577,0,0,71,3,'虚拟局站','Virtual Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1579,0,0,71,4,'服务厅','Service Hall',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1581,0,0,71,5,'传输汇接层','Transfer Aggregation Layer',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1583,0,0,71,6,'工程站','Engineering Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1585,0,0,71,7,'备用站','Standby Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1587,0,0,71,8,'微蜂窝','Microcell',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1589,0,0,71,9,'村通局站','Village Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1591,71,1,71,10,'VIP基站','VIP Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1593,71,1,71,11,'VVIP','VVIP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1595,71,1,71,12,'一级VIP','一级VIP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1597,71,1,71,13,'二级VIP','二级VIP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1599,71,1,71,14,'三级VIP','三级VIP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1601,0,0,71,15,'其他类型','其他类型',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1603,0,0,71,16,'干结点','干结点',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1605,71,5,71,17,'一类节点','区域性骨干节点',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1607,71,5,71,18,'二类节点','汇聚节点',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1609,71,5,71,19,'三类节点','综合业务接入节点',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1611,71,2,71,20,'一类机房','一类机房',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1613,71,2,71,21,'二类机房','二类机房',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1615,71,2,71,22,'三类机房','三类机房',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1617,0,0,71,23,'微站','Micro Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1619,0,0,71,24,'高铁专网','High Railway',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1621,0,0,71,25,'超级VIP基站','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1623,0,0,71,26,'数据中心','Data Center',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1625,0,0,72,1,'机房','Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1627,0,0,72,2,'设备类','Equipment Category',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1629,0,0,73,1,'DC505门禁','DC505 Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1631,0,0,73,2,'2750门禁','2750 Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1633,0,0,73,3,'ES2000-1门禁','ES2000-1 Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1635,0,0,73,4,'ES2000-2门禁','ES2000-2 Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1637,0,0,73,5,'一体化门禁','Integrative Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1639,0,0,73,6,'EDM30E门禁','EDM30E Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1641,0,0,74,1,'布防','Deploy',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1643,0,0,74,2,'撤防','Disarm',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1645,0,0,75,1,'未分组','UnGrouped',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1647,0,0,76,1,'最近12小时','The most recent 12 hours',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1649,0,0,76,2,'最近24小时','The most recent 24 hours',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1651,0,0,76,3,'最近48小时','The most recent 48 hours',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1653,0,0,79,1,'已监控有效','Monitoring and Valid',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1655,0,0,79,2,'已监控无效','Monitoring and Invalid',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1657,0,0,79,3,'未监控','Not Monitoring',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1659,0,0,80,1,'应用服务器登录','Login from Application Server',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1661,0,0,80,2,'配置工具登录','Login from Configuration Tool',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1663,0,0,81,1,'手动执行','Manual Execution',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1665,0,0,81,2,'定时执行','Periodic Execution',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1667,0,0,81,3,'告警联动','Event InterAction',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1669,0,0,81,4,'群控','Control in batch',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1671,0,0,82,1,'代维商','Maintenance Proxy',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1673,0,0,82,2,'其他','Other',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1675,0,0,83,0,'不通知 ','UnNotified',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1677,0,0,83,1,'等待投递 ','Waiting for Delivery',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1679,0,0,83,2,'正在投递 ','Deliverying',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1681,0,0,83,3,'投递成功  ','Deliveried Succeed',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1683,0,0,83,4,'投递失败 ','Deliveried Failure',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1685,0,0,83,5,'通知成功 ','Notified Succeed',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1687,0,0,83,6,'通知失败 ','Notified Failure',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1689,0,0,83,7,'通知超时 ','Notified Time 0ut',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1691,0,0,83,8,'派单处理中','Notification Dealing',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1693,0,0,83,9,'派单处理完毕','Notification Closed',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1695,0,0,83,10,'延迟时间内结束','Closed During Delay',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1697,0,0,83,11,'过期的告警','Over Due Alarm',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1699,0,0,83,12,'通知主机异常的失效告警','Invalidation Notify',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1701,0,0,84,1,'男','Male',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1703,0,0,84,2,'女','Female',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1705,0,0,85,0,'离线','Offline',TRUE,TRUE,FALSE,'','Offline.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1707,0,0,85,1,'在线','Online',TRUE,TRUE,FALSE,'','Online.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1709,0,0,85,2,'异常','Exception',TRUE,TRUE,FALSE,'','Fault.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1711,0,0,85,3,'断开','Interruption',TRUE,TRUE,FALSE,'','Break.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1713,0,0,86,1,'检查设备条件，信号，事件表达式是否存在非法字符','Check for the invalid characters in equipment, signal and event expression.',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1715,0,0,86,2,'检查设备条件，信号，事件表达式长度是否越界','Check that the Expression Length of Equipment, Signal and Event is within the allowed range.',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1717,0,0,86,3,'检查事件条件中的事件等级是否为空','Check that the Event severity condition is null.',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1719,0,0,87,1,'登录','Login',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1721,0,0,87,2,'退出','Logout',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1723,0,0,87,3,'踢出','KickOut',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1725,0,0,87,4,'确认','ConfirmEvent',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1727,0,0,87,5,'强制结束','EndEvent',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1729,0,0,87,6,'录入备注信息','AddEventNote',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1731,0,0,87,7,'EOMS手动派单','AsignEOMS',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1733,0,0,87,8,'动态配置','DynamicConfig',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1735,0,0,87,9,'局站屏蔽设置','MaskStation',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1737,0,0,87,10,'设备屏蔽设置','MaskEquipment',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1739,0,0,87,11,'事件屏蔽设置','MaskEvent',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1741,0,0,87,12,'发送控制命令','SendControlCommand',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1743,0,0,87,13,'取消控制命令','CancelControlCommand',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1745,0,0,87,14,'确认控制命令','ConfirmControlCommand',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1747,0,0,87,15,'开关门命令','OpenOrCloseDoorCommand',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1749,0,0,87,16,'专家建议新增','AddEExperience',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1751,0,0,87,17,'专家建议修改','EditExperience',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1753,0,0,87,18,'专家建议删除','DeleteExperience',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1755,0,0,87,19,'过滤条件新增','AddFilter',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1757,0,0,87,20,'过滤条件删除','DeleteFilter',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1759,0,0,87,21,'过滤条件修改','EditFilter',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1761,0,0,87,22,'界面列调整','AdjustUserInterface',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1763,0,0,87,50,'配置更改','Change Configuration ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1765,0,0,88,0,'四级告警','四级告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1767,0,0,88,1,'三级告警','三级告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1769,0,0,88,2,'二级告警','二级告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1771,0,0,88,3,'一级告警','一级告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1773,0,0,89,1,'设备告警','设备告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1775,0,0,89,2,'环境告警','环境告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1777,0,0,89,3,'电网异常','电网异常',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1779,0,0,91,0,'未定义','未定义',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1781,0,0,92,0,'未定义','未定义',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1783,0,0,93,0,'未定义','未定义',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1785,0,0,94,0,'未定义','未定义',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1787,0,0,95,0,'未定义','未定义',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1789,0,0,96,0,'未定义','未定义',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1791,0,0,99,1,'动环网','动环网',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1793,0,0,99,2,'话务网','话务网',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1795,0,0,100,1,'标准','标准',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1797,0,0,101,1,'表达式触发','Expression  Trigger',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1799,0,0,101,2,'时间触发','Time Trigger',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1801,0,0,102,1,'邮件通知','Mail Inform',TRUE,TRUE,FALSE,'','Email.so',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1803,0,0,102,2,'手机通知','Mobile Inform',TRUE,TRUE,FALSE,'','Mobile.so',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1805,0,0,103,1,'用户手动执行','Manual Excute',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1807,0,0,103,2,'告警联动自动执行','EventAction Auto Excute',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1809,0,0,103,3,'定时触发执行','Timing Trigger Excute',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1811,0,0,104,1,'Telnet(TCP/IP)','Telnet(TCP/IP)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1813,0,0,104,2,'Direct','Direct',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1815,0,0,104,3,'PSTN Modem','PSTN Modem',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1817,0,0,104,4,'GPRS Modem','GPRS Modem',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1819,0,0,104,5,'GSM','GSM  Modem',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1821,0,0,105,1,'M.C','M.C',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1823,0,0,105,2,'C.S.U.','C.S.U.',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1825,0,0,106,1,'M.C','M.C',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1827,0,0,106,2,'Deactivated','Deactivated',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1829,0,0,106,3,'C.S.U.','C.S.U.',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1831,0,0,107,1,'EEM','EEM',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1833,0,0,107,2,'SOC','SOC',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1835,0,0,107,3,'SOC-TPE','SOC-TPE',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1837,0,0,107,4,'SNMP','SNMP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1839,0,0,108,1,'MIB LXP','MIB LXP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1841,0,0,108,2,'MIB LMS','MIB LMS',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1843,0,0,108,3,'MIB VORTEX','MIB VORTEX',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1845,0,0,109,1,'ENP','ENP',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1847,0,0,109,2,'ENP Dubai','ENP Dubai',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1849,0,0,109,3,'JAVI Customer','JAVI Customer',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1851,0,0,109,4,'Other Company','Other Company',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1853,0,0,110,1,'点对点','P2P',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1855,0,0,110,2,'链式','LinkedList',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1857,0,0,110,3,'双向环','Bi-Directional Ring',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1859,0,0,110,4,'抽时隙','Pumping Slot',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1861,0,0,110,5,'用户DCN','DCN',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1863,0,0,110,6,'PSTN','PSTN',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1865,0,0,110,7,'无线猫','Wireless Modem',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1867,0,0,111,1,'RJ23','RJ23',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1869,0,0,111,2,'RJ45','RJ45',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1871,7,51,112,1,'温度','Temperature',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1873,7,51,112,2,'湿度','Humidity',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1875,7,51,112,3,'水浸','Flood ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1877,7,51,112,4,'烟雾','Fog',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1879,7,51,112,5,'红外','Infrared',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1881,7,51,112,6,'综合防盗','Anti-theft',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1883,7,51,112,7,'门磁','Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1885,7,52,112,8,'温度','Temperature',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1887,7,52,112,9,'湿度','Humidity',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1889,7,52,112,10,'水浸','Flood ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1891,7,52,112,11,'烟雾','Fog',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1893,7,52,112,12,'红外','Infrared',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1895,7,52,112,13,'综合防盗','Anti-theft',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1897,7,52,112,14,'门磁','Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1899,7,53,112,15,'温度','Temperature',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1901,7,53,112,16,'湿度','Humidity',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1903,7,53,112,17,'水浸','Flood ',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1905,7,53,112,18,'烟雾','Fog',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1907,7,53,112,19,'红外','Infrared',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1909,7,53,112,20,'综合防盗','Anti-theft',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1911,7,53,112,21,'门磁','Door Access',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1913,7,22,112,22,'交流输入AB线电压/相电压','交流输入AB线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1915,7,22,112,23,'交流屏输入BC线电压','交流屏输入BC线电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1917,7,22,112,24,'交流屏输入CA线电压','交流屏输入CA线电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1919,7,22,112,25,'交流输出A相电流','交流输出A相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1921,7,22,112,26,'交流输出B相电流','交流输出B相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1923,7,22,112,27,'交流输出C相电流','交流输出C相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1925,7,22,112,28,'直流输出电压','直流输出电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1927,7,22,112,29,'负载总电流','负载总电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1929,7,22,112,30,'电池总电压','电池总电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1931,7,22,112,31,'电池温度','电池温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1933,7,22,112,32,'系统均浮充状态','系统均浮充状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1935,7,41,112,33,'回风温度','回风温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1937,7,41,112,34,'回风湿度','回风湿度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1939,7,41,112,35,'空调状态','空调状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1941,7,41,112,36,'开关机状态','开关机状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1943,7,42,112,37,'回风温度','回风温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1945,7,42,112,38,'回风湿度','回风湿度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1947,7,42,112,39,'空调状态','空调状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1949,7,42,112,40,'开关机状态','开关机状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1951,7,43,112,41,'回风温度','回风温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1953,7,43,112,42,'回风湿度','回风湿度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1955,7,43,112,43,'空调状态','空调状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1957,7,43,112,44,'开关机状态','开关机状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1959,7,44,112,45,'回风温度','回风温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1961,7,44,112,46,'回风湿度','回风湿度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1963,7,44,112,47,'空调状态','空调状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1965,7,44,112,48,'开关机状态','开关机状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1967,7,45,112,49,'回风温度','回风温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1969,7,45,112,50,'回风湿度','回风湿度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1971,7,45,112,51,'空调状态','空调状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1973,7,45,112,52,'开关机状态','开关机状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1975,7,31,112,53,'交流输入AB线电压/相电压','交流输入AB线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1977,7,31,112,54,'交流输入BC线电压/相电压','交流输入BC线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1979,7,31,112,55,'交流输入CA线电压/相电压','交流输入CA线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1981,7,31,112,56,'交流输出AB线电压/相电压','交流输出AB线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1983,7,31,112,57,'交流输出BC线电压/相电压','交流输出BC线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1985,7,31,112,58,'交流输出CA线电压/相电压','交流输出CA线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1987,7,31,112,59,'电池组总电压','电池组总电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1989,7,31,112,60,'单体电池温度','单体电池温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1991,7,31,112,61,'供电方式','供电方式',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1993,7,13,112,62,'AB线电压/相电压','AB线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1995,7,13,112,63,'BC线电压/相电压','BC线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1997,7,13,112,64,'CA线电压/相电压','CA线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1999,7,13,112,65,'A相电流','A相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2001,7,13,112,66,'B相电流','B相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2003,7,13,112,67,'C相电流','C相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2005,7,13,112,68,'输出频率/转速','输出频率/转速',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2007,7,13,112,69,'水温','水温',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2009,7,13,112,70,'油温','油温',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2011,7,13,112,71,'油位','油位',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2013,7,13,112,72,'油压','油压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2015,7,13,112,73,'启动电池电压','启动电池电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2017,7,13,112,74,'自动手动状态','自动手动状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2019,7,13,112,75,'停机/运行状态','停机/运行状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2021,7,82,112,76,'卡号','卡号',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2023,7,82,112,77,'刷卡时间','刷卡时间',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2025,7,82,112,78,'非法开门状态','非法开门状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2027,7,82,112,79,'门开关状态','门开关状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2029,7,85,112,80,'AB线电压/相电压','AB线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2031,7,85,112,81,'BC线电压/相电压','BC线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2033,7,85,112,82,'CA线电压/相电压','CA线电压/相电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2035,7,85,112,83,'A相电流','A相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2037,7,85,112,84,'B相电流','B相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2039,7,85,112,85,'C相电流','C相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2041,7,85,112,86,'交流频率','交流频率',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2043,7,85,112,87,'A相功率因数','A相功率因数',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2045,7,85,112,88,'B相功率因数','B相功率因数',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2047,7,85,112,89,'C相功率因数','C相功率因数',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2049,7,85,112,90,'正向有功电能累加值（总用电量）','正向有功电能累加值（总用电量）',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2051,7,24,112,91,'电池组总电压','电池组总电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2053,7,24,112,92,'电池组充放电电流','电池组充放电电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2055,7,24,112,93,'电池后备时间','电池后备时间',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2057,7,24,112,94,'电池组温度','电池组温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2059,7,24,112,95,'电池状态','电池状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2061,7,24,112,96,'单体电池落后状态','单体电池落后状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2063,7,86,112,97,'室内温度','室内温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2065,7,86,112,98,'室外温度','室外温度',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2067,7,86,112,99,'进风风机状态','进风风机状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2069,7,86,112,100,'出风风机状态','出风风机状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2071,7,86,112,101,'进风风机故障','进风风机故障',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2073,7,86,112,102,'出风风机故障','出风风机故障',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2075,7,11,112,103,'AB线电压','AB线电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2077,7,11,112,104,'BC线电压','BC线电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2079,7,11,112,105,'CA线电压','CA线电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2081,7,11,112,106,'A相电流','A相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2083,7,11,112,107,'B相电流','B相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2085,7,11,112,108,'C相电流','C相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2087,7,11,112,109,'出线线柜状态','出线线柜状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2089,7,11,112,110,'进线柜状态','进线柜状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2091,7,11,112,111,'变压器系统状态','变压器系统状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2093,7,11,112,112,'高压操作电池故障状态','高压操作电池故障状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2095,7,11,112,113,'高压操作电池温度过高告警状态','高压操作电池温度过高告警状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2097,7,12,112,114,'AB线电压','AB线电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2099,7,12,112,115,'BC线电压','BC线电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2101,7,12,112,116,'CA线电压','CA线电压',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2103,7,12,112,117,'A相电流','A相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2105,7,12,112,118,'B相电流','B相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2107,7,12,112,119,'C相电流','C相电流',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2109,7,12,112,120,'进线柜开关状态','进线柜开关状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2111,7,12,112,121,'配电柜开关状态','配电柜开关状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2113,7,12,112,122,'过载跳闸状态','过载跳闸状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2115,7,12,112,123,'失压跳闸状态','失压跳闸状态',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2117,7,51,113,1,'水浸告警','水浸告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2119,7,51,113,2,'烟雾告警','烟雾告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2121,7,51,113,3,'红外告警','红外告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2123,7,51,113,4,'综合防盗','综合防盗',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2125,7,51,113,5,'门磁告警','门磁告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2127,7,52,113,6,'水浸告警','水浸告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2129,7,52,113,7,'烟雾告警','烟雾告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2131,7,52,113,8,'红外告警','红外告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2133,7,52,113,9,'综合防盗','综合防盗',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2135,7,52,113,10,'门磁告警','门磁告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2137,7,53,113,11,'水浸告警','水浸告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2139,7,53,113,12,'烟雾告警','烟雾告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2141,7,53,113,13,'红外告警','红外告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2143,7,53,113,14,'综合防盗','综合防盗',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2145,7,53,113,15,'门磁告警','门磁告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2147,7,22,113,16,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2149,7,41,113,17,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2151,7,42,113,18,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2153,7,43,113,19,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2155,7,44,113,20,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2157,7,45,113,21,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2159,7,31,113,22,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2161,7,13,113,23,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2163,7,82,113,24,'非法开门告警','非法开门告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2165,7,82,113,25,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2167,7,24,113,26,'单体电池落后告警','单体电池落后告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2169,7,86,113,27,'进风风机故障','进风风机故障',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2171,7,86,113,28,'出风风机故障','出风风机故障',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2173,7,86,113,29,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2175,7,11,113,30,'变压器系统告警','变压器系统告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2177,7,11,113,31,'高压操作电池故障告警','高压操作电池故障告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2179,7,11,113,32,'高压操作电池温度过高告警','高压操作电池温度过高告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2181,7,11,113,33,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2183,7,12,113,34,'过载跳闸告警','过载跳闸告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2185,7,12,113,35,'失压跳闸告警','失压跳闸告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2187,7,12,113,36,'其他告警','其他告警',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2189,7,22,114,1,'系统均充命令','系统均充命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2191,7,22,114,2,'系统浮充命令','系统浮充命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2193,7,41,114,3,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2195,7,41,114,4,'开机/关机命令','开机/关机命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2197,7,42,114,5,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2199,7,42,114,6,'开机/关机命令','开机/关机命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2201,7,43,114,7,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2203,7,43,114,8,'开机/关机命令','开机/关机命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2205,7,44,114,9,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2207,7,44,114,10,'开机/关机命令','开机/关机命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2209,7,44,114,11,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2211,7,44,114,12,'开机/关机命令','开机/关机命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2213,7,13,114,13,'开机/关机','开机/关机',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2215,7,13,114,14,'紧急停机','紧急停机',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2217,7,82,114,15,'远程开门命令','远程开门命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2219,7,86,114,16,'开/关风机命令','开/关风机命令',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2221,0,0,115,1,'SiteWeb权限','SiteWeb Previlege',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2223,0,0,115,2,'油机调度权限','Generator Previlege',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2225,0,0,116,1,'轮流开机','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2227,0,0,116,2,'同时开机','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2229,0,0,1001,0,'其它','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2231,0,0,1001,1,'平顶砖混','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2233,0,0,1001,2,'尖顶砖混','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2235,0,0,1001,3,'彩钢板','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2237,0,0,1001,4,'一体化机房','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2239,0,0,1001,5,'住宅楼','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2241,0,0,1001,6,'办公楼','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2243,0,0,1002,0,'不设定','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2245,0,0,1002,1,'设备','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2247,0,0,1002,2,'空调','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2249,0,0,1003,1,'电表日用电量异常','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2251,0,0,1003,2,'月用电量异常','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2253,0,0,1003,3,'电费折算月用电量异常','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2255,0,0,1003,4,'智能电表工作状态异常','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2257,0,0,1004,1,'6','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2259,0,0,1004,2,'12','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2261,0,0,1004,3,'18','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2263,0,0,1004,4,'24','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2265,0,0,1004,5,'32','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2267,0,0,1005,1,'小面积','',TRUE,FALSE,FALSE,'',' Acreage > 0 AND Acreage <=15 ','0-15',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2269,0,0,1005,2,'中面积','',TRUE,FALSE,FALSE,'',' Acreage > 15 AND Acreage <=30 ','15-30',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2271,0,0,1005,3,'大面积','',TRUE,FALSE,FALSE,'',' Acreage > 30 AND Acreage <=45 ','30-45',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2273,0,0,1005,4,'超大面积','',TRUE,FALSE,FALSE,'',' Acreage > 45  ','45',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2275,0,0,1006,0,'未知','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2277,0,0,1006,1,'爱立信','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2279,0,0,1006,2,'阿尔卡特','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2281,0,0,1006,3,'华为','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2283,0,0,1006,4,'诺基亚','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2285,0,0,1006,5,'西门子','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2287,0,0,1006,6,'中兴','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2289,0,0,1007,0,'未知','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2291,0,0,1007,1,'大金','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2293,0,0,1007,2,'三菱','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2295,0,0,1007,3,'三洋','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2297,0,0,1007,4,'海尔','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2299,0,0,1007,5,'松下','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2301,0,0,1007,7,'海尔三菱','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2303,0,0,1007,8,'格力','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2305,0,0,1007,9,'美的','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2307,0,0,1008,0,'未知','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2309,0,0,1008,1,'维谛','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2311,0,0,1008,2,'中达','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2313,0,0,1008,3,'中恒','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2315,0,0,1008,4,'易达','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2317,0,0,1008,5,'珠江','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2319,0,0,1008,6,'罗兰','',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2321,0,0,1009,0,'未知','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2323,0,0,1009,1,'交流电表','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2325,0,0,1009,2,'直流电表','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2327,0,0,1010,0,'无','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2329,0,0,1010,1,'有','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2331,0,0,117,0,'其他','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2333,0,0,117,1,'放电测试','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2335,0,0,117,2,'停电','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2337,0,0,118,0,'不处理','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2339,0,0,118,1,'更换电池','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2341,0,0,120,0,'其它','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2343,0,0,120,1,'屏蔽时间段','Mask Duration',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2345,0,0,120,2,'工程预约屏蔽','Project Precontract',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2347,0,0,120,10,'准进时间组1','Allowed to enter the time group 1',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2349,0,0,120,11,'准进时间组2','Allowed to enter the time group 2',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2351,0,0,120,12,'准进时间组3','Allowed to enter the time group 3',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2353,0,0,120,13,'准进时间组4','Allowed to enter the time group 4',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2355,0,0,120,14,'准进时间组5','Allowed to enter the time group 5',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2357,0,0,121,0,'其它','Other',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2359,0,0,121,1,'主设备电表直采模式','Master Equipment Ammeter Direct Sourcing Model',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2361,0,0,121,2,'主设备计算模式','Master Equipment Computing Model',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2363,0,0,121,3,'电源电表直采模式','Power Supply Ammeter Direct Sourcing Model',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2365,0,0,121,4,'电源计算模式','Power Supply Computing Model',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2367,0,0,122,0,'未知','Unknown',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2369,0,0,122,1,'局站工程预约状态下的事件','Events at station building',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2371,0,0,122,2,'系统强制结束的超长时间事件','The long time events of system terminated',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2373,0,0,123,0,'未知','Unknown',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2375,0,0,123,1,'停电放电','Discharge because of power off',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2377,0,0,123,2,'手动放电','Manual Discharge',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2379,0,0,124,0,'0','Contract Site Count',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2381,0,0,150,1,'监控中心','MonitorCenter',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2383,0,0,150,2,'局站分组','StationStructure',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2385,0,0,150,3,'局房','House',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2387,0,0,150,4,'工作站','WorkStation',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2389,0,0,150,5,'局站','Station',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2391,0,0,150,6,'监控单元','MonitorUnit',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2393,0,0,150,7,'端口','Port',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2395,0,0,150,8,'采集单元','SamplerUnit',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2397,0,0,150,9,'采集器','Sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2399,0,0,150,10,'设备模板','EquipmentTemplate',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2401,0,0,150,11,'设备','Equipment',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2403,0,0,150,12,'信号','Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2405,0,0,150,13,'信号含义','SignalMeanings',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2407,0,0,150,14,'信号属性','SignalProperty',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2409,0,0,150,15,'事件','Event',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2411,0,0,150,16,'事件条件','EventCondition',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2413,0,0,150,17,'控制','Control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2415,0,0,150,18,'控制含义','ControlMeanings',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2417,0,0,150,19,'样板站','SwatchStation',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2419,0,0,150,20,'人员管理','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2421,0,0,150,21,'部门','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2423,0,0,150,22,'人员','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2425,0,0,150,23,'帐号','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2427,0,0,150,24,'片区','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2429,0,0,150,25,'模块','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2431,0,0,150,26,'操作','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2433,0,0,150,27,'专业','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2435,0,0,150,28,'角色','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2437,0,0,150,29,'权限组授权','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2439,0,0,150,30,'角色帐号授权','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2441,0,0,150,31,'角色权限组授权','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2443,0,0,150,32,'门禁卡','',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2445,0,0,151,1,'缺失实例信号','MonitorUnitSignal Error',TRUE,TRUE,FALSE,'缺失实例信号',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2447,0,0,151,2,'信号类型错误','SignalType Error',TRUE,TRUE,FALSE,'信号类型错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2449,0,0,151,3,'告警过滤表达式错误','EventExpression Error',TRUE,TRUE,FALSE,'告警过滤表达式错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2451,0,0,151,4,'事件开始表达式语法错误','EventExpression Error',TRUE,TRUE,FALSE,'事件开始表达式语法错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2453,0,0,151,5,'事件结束表达式语法错误','EventExpression Error',TRUE,TRUE,FALSE,'事件结束表达式语法错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2455,0,0,151,6,'设备告警过滤表达式语法错误','EquipmentExpression Error',TRUE,TRUE,FALSE,'设备告警过滤表达式语法错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2457,0,0,152,0,'否','No',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2459,0,0,152,1,'是','Yes',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2461,0,0,153,0,'系统项','System',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2463,0,0,153,1,'新增项','Add',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2465,0,0,153,2,'修改项','Modify',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2467,0,0,156,1,'解除告警屏蔽','Clear Mask',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2469,0,0,156,2,'结束告警','End Alarm',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2471,0,0,156,3,'确认告警','Confirm Alarm',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2473,0,0,156,5,'增加事件注释','Add Note',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2475,0,0,156,6,'保存派单状态','Save Notification Status',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2477,0,0,156,7,'保存派单号','Save Notification ID',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2479,0,0,156,21,'发送工单','Send Work Order',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2481,0,0,2022,1,'电脑','computer',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2483,0,0,2022,2,'采集器','sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2485,0,0,2022,3,'智能设备','sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2487,0,0,2022,4,'非智能设备','sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2489,0,0,2022,5,'网络资产','sampler',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2491,0,0,2003,1,'ID卡','RFID',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2493,0,0,2003,2,'IC卡','RFIC',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2495,0,0,2004,0,'刷卡 或 指纹 ','Key Or Card Or Finger',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2497,0,0,2004,1,'刷卡 + 指纹','Card+Finger',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2499,0,0,2004,2,'ID号 + 指纹','ID+Finger',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2501,0,0,2004,3,'ID号 + 密码','ID+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2503,0,0,2004,4,'刷卡 + 密码','Card+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2505,0,0,2004,5,'指纹 + 密码','Finger+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2507,0,0,2004,7,'刷卡 + 指纹 + 密码','Card+Finger+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2509,0,0,2005,1,'出门','Out',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2511,0,0,2005,0,'进门','In',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2513,0,0,2006,0,'纽贝尔806D4M3/D2M3','806D4M3/D2M3',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2515,0,0,2006,1,'艾默生ISU','ISU',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2517,0,0,2006,2,'艾默生IDU','IDU',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2519,0,0,2006,3,'艾默生eStone','eStone',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2521,0,0,2006,4,'其他','Others',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2523,0,0,2007,8001,'串口1','COM1',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2525,0,0,2007,8002,'串口2','COM2',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2527,0,0,2007,8003,'串口3','COM3',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2529,0,0,2007,8004,'串口4','COM4',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2531,0,0,2007,8005,'串口5','COM5',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2533,0,0,2007,8006,'串口6','COM6',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2535,0,0,2007,8007,'串口7','COM7',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2537,0,0,2007,8008,'串口8','COM8',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2539,0,0,2007,8009,'串口9','COM9',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2541,0,0,2007,8010,'串口10','COM10',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2543,0,0,2007,8011,'串口11','COM11',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2545,0,0,2007,8012,'串口12','COM12',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2547,0,0,2007,8013,'串口13','COM13',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2549,0,0,2007,8014,'串口14','COM14',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2551,0,0,2007,8015,'串口15','COM15',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2553,0,0,2007,8016,'串口16','COM16',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2555,0,0,2008,5001,'串口1','COM1',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2557,0,0,2008,5002,'串口2','COM2',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2559,0,0,2008,5003,'串口3','COM3',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2561,0,0,2008,5004,'串口4','COM4',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2563,0,0,2008,5005,'串口5','COM5',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2565,0,0,2008,5006,'串口6','COM6',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2567,0,0,2008,5007,'串口7','COM7',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2569,0,0,2008,5008,'串口8','COM8',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2571,0,0,2008,5009,'串口9','COM9',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2573,0,0,2008,5010,'串口10','COM10',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2575,0,0,2008,5011,'串口11','COM11',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2577,0,0,2008,5012,'串口12','COM12',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2579,0,0,2008,5013,'串口13','COM13',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2581,0,0,2008,5014,'串口14','COM14',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2583,0,0,2008,5015,'串口15','COM15',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2585,0,0,2008,5016,'串口16','COM16',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2587,0,0,2008,5017,'串口17','COM17',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2589,0,0,2008,5018,'串口18','COM18',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2591,0,0,2008,5019,'串口19','COM19',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2593,0,0,2008,5020,'串口20','COM20',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2595,0,0,2008,5021,'串口21','COM21',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2597,0,0,2008,5022,'串口22','COM22',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2599,0,0,2008,5023,'串口23','COM23',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2601,0,0,2008,5024,'串口24','COM24',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2603,0,0,2009,5001,'串口1','COM1',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2605,0,0,2009,5002,'串口2','COM2',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2607,0,0,2009,5003,'串口3','COM3',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2609,0,0,2009,5004,'串口4','COM4',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2611,0,0,2009,5005,'串口5','COM5',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2613,0,0,2009,5006,'串口6','COM6',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2615,0,0,2009,5007,'串口7','COM7',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2617,0,0,2009,5008,'串口8','COM8',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2619,0,0,2009,5009,'串口9','COM9',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2621,0,0,2009,5010,'串口10','COM10',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2623,0,0,2009,5011,'串口11','COM11',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2625,0,0,2009,5012,'串口12','COM12',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2627,0,0,2009,5013,'串口13','COM13',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2629,0,0,2009,5014,'串口14','COM14',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2631,0,0,2009,5015,'串口15','COM15',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2633,0,0,2009,5016,'串口16','COM16',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2635,0,0,2009,5017,'串口17','COM17',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2637,0,0,2009,5018,'串口18','COM18',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2639,0,0,2009,5019,'串口19','COM19',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2641,0,0,2009,5020,'串口20','COM20',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2643,0,0,2009,5021,'串口21','COM21',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2645,0,0,2009,5022,'串口22','COM22',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2647,0,0,2009,5023,'串口23','COM23',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2649,0,0,2009,5024,'串口24','COM24',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2651,0,0,2009,5025,'串口25','COM25',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2653,0,0,2009,5026,'串口26','COM26',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2655,0,0,2009,5027,'串口27','COM27',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2657,0,0,2009,5028,'串口28','COM28',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2659,0,0,2009,5029,'串口29','COM29',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2661,0,0,2009,5030,'串口30','COM30',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2663,0,0,2009,5031,'串口31','COM31',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2665,0,0,2009,5032,'串口32','COM32',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2667,0,0,2009,5033,'串口33','COM33',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2669,0,0,2009,5034,'串口34','COM34',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2671,0,0,2009,5035,'串口35','COM35',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2673,0,0,2009,5036,'串口36','COM36',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2675,0,0,2009,5037,'串口37','COM37',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2677,0,0,2010,4002,'串口1','COM1',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2679,0,0,2021,1,'门禁卡','Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2681,0,0,2021,2,'指纹','Fingerprint',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2683,0,0,2021,3,'人脸','Face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2685,0,0,31,29,'添加一人多卡','add multiple cards',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2687,0,0,31,30,'删除用户名下一人多卡','delete multiple cards',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2689,0,0,31,31,'添加或修改指纹','add or update finger print',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2691,0,0,31,32,'删除指纹','delete finger print',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2693,0,0,31,33,'添加或修改人脸','add or update face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2695,0,0,31,34,'删除人脸','delete face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2697,0,0,31,35,'添加或修改用户','add or update user',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2699,0,0,31,36,'删除用户','delete user',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2701,0,0,31,37,'采集人脸信息','sample face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2703,0,0,31,38,'采集指纹信息','sample finger print',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2705,0,0,31,39,'设置读卡器默认验证方式（门开方式）','door open mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2707,0,0,31,40,'读头设置指纹','door open mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2709,0,0,31,41,'读头删除指纹','door open mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2711,0,0,2012,0,'普通用户','Ordinary user',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2713,0,0,2012,2,'登记员','Registrar',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2715,0,0,2012,6,'管理员','Administrator',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2717,0,0,2012,10,'用户自定义','User defined',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2719,0,0,2012,14,'超级管理员','Super admin',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2721,0,0,2013,2,'刷卡+密码','Card+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2723,0,0,2013,3,'刷卡','Card',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2725,0,0,2013,4,'刷卡/（工号+密码）','Card/(JobNO+Password)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2727,0,0,2013,5,'指纹','FingerPrint',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2729,0,0,2013,6,'指纹+密码','FingerPrint+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2731,0,0,2013,7,'刷卡/指纹','Card/FingerPrint',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2733,0,0,2013,8,'刷卡+指纹','Card+FingerPrint',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2735,0,0,2013,9,'刷卡+指纹+密码','Card+FingerPrint+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2737,0,0,2013,10,'刷卡/指纹/人脸/(工号+密码)','Card/FingerPrint/Face/(JobNO+Password)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2739,0,0,2013,11,'指纹+人脸','FingerPrint+Face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2741,0,0,2013,13,'刷卡+人脸','Card+Face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2743,0,0,2013,14,'人脸','Face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2745,0,0,2013,15,'工号+密码','JobNO+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2747,0,0,2013,16,'(工号+密码)/指纹','(JobNO+Password)/FingerPrint',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2749,0,0,2013,17,'工号+指纹','JobNO+FingerPrint',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2751,0,0,2013,18,'工号+密码+指纹','JobNO+Password+FingerPrint',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2753,0,0,2013,19,'刷卡+指纹+人脸','Card+FingerPrint+Face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2755,0,0,2013,20,'指纹+人脸+密码','FingerPrint+Face+Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2757,0,0,2013,21,'工号+人脸','JobNO+Face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2759,0,0,2013,23,'指纹/人脸','FingerPrint/Face',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2761,0,0,2013,24,'刷卡/人脸/(工号+密码)','Card/Face/(JobNO+Password)',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2763,0,0,1011,1,'year','datetime control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2765,0,0,1011,2,'month','datetime control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2767,0,0,1011,3,'date','datetime control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2769,0,0,1011,4,'datetime','datetime control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2771,0,0,1011,5,'transfer','combox control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2773,0,0,1011,6,'tree','tree control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2775,0,0,1011,7,'input','textbox control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2777,0,0,1011,8,'check','checkbox control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2779,0,0,1011,9,'radio','radio button control',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2781,0,0,31,42,'设置门常开','Set Door Normally Open',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2783,0,0,31,43,'设置门常闭','Set Door Normally Close',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2785,0,0,31,44,'远程关门','Remote Close',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2787,0,0,31,45,'火警信号有效方式','Effective Way Of Fire Alarm Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2789,0,0,31,46,'卡封锁错误次数','Number Of Card Block Errors',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2791,0,0,31,47,'卡封锁时间','Card Lock Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2793,0,0,31,48,'非法卡刷卡间隔','Illegal Card Swipe Interval',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2795,0,0,31,49,'门开保持时间','Door Open Holding Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2797,0,0,31,50,'门开方式（纽贝尔）','Door Open Mode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2799,0,0,157,1,'门开方式','Door OpenMode',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2801,0,0,157,2,'门禁火警信号有效方式','Door Effective Way Of Fire Alarm Signal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2803,0,0,157,3,'刷卡或开门状态','Card Swiping Or Door Open State',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2805,0,0,157,4,'进出门标志','Entry Or Exit Sign',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2807,0,0,157,5,'门开保持时间','Door Open Holding Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2809,0,0,157,6,'门开超时时间','Door Open Delay Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2811,0,0,157,7,'卡封锁错误次数','Number Of Card Block Errors',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2813,0,0,157,8,'卡封锁时间','Card Lock Time',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2815,0,0,157,9,'非法卡刷卡间隔','Illegal Card Swipe Interval',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2817,0,0,157,10,'门密码','Door Password',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2819,0,0,2023,1,'SNMP','SNMP',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2821,0,0,2023,2,'BACNet','BACNet',TRUE,FALSE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2823,0,0,39,32,'BACNet端口(Linux RMU)','BACNetPort',TRUE,FALSE,FALSE,'','74',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2825,0,0,39,33,'SNMP端口(Linux RMU)','SNMPPort',TRUE,FALSE,FALSE,'','4',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2827,0,0,3001,1,'最小级别','min level',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2829,0,0,3001,2,'基本级别','basic level',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2831,0,0,3001,3,'详细级别','detail level',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2833,0,0,3001,4,'未定义级别','undefined level',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2835,0,0,3002,1,'身份用户鉴别','identity authentication',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2837,0,0,3002,2,'攻击检测','attack detection',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2839,0,0,3002,3,'暴力破解','brute force',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2841,0,0,3002,4,'完整性检测','integrity test',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2843,0,0,3004,10,'十进制','decimal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2845,0,0,3004,16,'十六进制','hexadecimal',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2847,0,0,3005,1,'全部','all',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2849,0,0,3005,2,'动环','power & environment supervision',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2851,0,0,3005,3,'通用','general',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2853,0,0,3005,4,'指标类','complex index',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2855,0,0,3005,5,'能耗','energy',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2857,0,0,3005,6,'定制','customize',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2859,0,0,3005,7,'图表','chart',TRUE,TRUE,FALSE,'',NULL,NULL,NULL,NULL,NULL);