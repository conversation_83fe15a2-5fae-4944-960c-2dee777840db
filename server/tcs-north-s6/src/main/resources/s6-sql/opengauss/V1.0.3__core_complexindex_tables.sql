-- 复杂索引表
CREATE TABLE complexindex (
                              complexindexid SERIAL PRIMARY KEY,  -- 自动增长的主键
                              complexindexname VARCHAR(256),
                              complexindexdefinitionid INT,
                              objectid INT,
                              calccron VARCHAR(256),
                              calctype INT,
                              aftercalc VARCHAR(256),
                              savecron VARCHAR(256),
                              expression TEXT,
                              unit VARCHAR(256),
                              accuracy VARCHAR(256),
                              objecttypeid INT,
                              remark VARCHAR(256),
                              label VARCHAR(256),
                              businesstypeid INT,
                              checkexpression VARCHAR(2048)
);
