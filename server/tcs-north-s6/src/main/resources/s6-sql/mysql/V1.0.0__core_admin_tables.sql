CREATE TABLE diskfile (
    fileid BIGINT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    filepath VARCHAR(128) NOT NULL,
    filename VARCHAR(128) NOT NULL,
    status INT,
    createtime TIMESTAMP
);

CREATE TABLE resourcestructure (
    resourcestructureid INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    sceneid INT COMMENT '场景ID',
    structuretypeid INT COMMENT '资源组类型',
    resourcestructurename VARCHAR(128) COMMENT '分组名',
    parentresourcestructureid INT COMMENT '父分组Id',
    photo VARCHAR(256) COMMENT '图片',
    `position` VARCHAR(256) COMMENT '位置信息',
    levelofpath VARCHAR(128) COMMENT '连接路径',
    display INT COMMENT '是否显示',
    sortvalue INT COMMENT '排序Index',
    extendedfield TEXT COMMENT '扩展信息',
    originid INT COMMENT '源对象ID',
    originparentid INT COMMENT '源父对象ID'
) COMMENT='Resource structure table';

CREATE INDEX idx_resourcestructure_levelofpath ON resourcestructure (levelofpath);
CREATE INDEX idx_resourcestructure_1 ON resourcestructure (structuretypeid, originparentid, originid);

CREATE TABLE resourcestructuretype (
    resourcestructuretypeid INT NOT NULL PRIMARY KEY,
    sceneid INT COMMENT '场景ID',
    resourcestructuretypename VARCHAR(128) COMMENT '分类名',
    description VARCHAR(255) COMMENT '描述信息'
) COMMENT='Resource structure type table';

CREATE TABLE rolepermissionmap (
    rolepermissionmapid INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    roleid INT COMMENT '角色ID',
    permissioncategoryid INT COMMENT '权限分类ID',
    permissionid INT COMMENT '权限ID'
) COMMENT='Role permission mapping table';

CREATE INDEX idx_permissioncategoryid ON rolepermissionmap (permissioncategoryid);
CREATE INDEX idx_role_permissionid ON rolepermissionmap (roleid, permissionid);

CREATE TABLE systemconfig (
    systemconfigid INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    systemconfigkey VARCHAR(255) COMMENT '键名',
    systemconfigvalue VARCHAR(255) COMMENT '对应键值',
    systemconfigtype INT COMMENT '键值对业务类型',
    description VARCHAR(255) COMMENT '描述信息'
) COMMENT='System configuration table';

CREATE UNIQUE INDEX idx_systemconfigkey ON systemconfig (systemconfigkey);

CREATE TABLE userconfig (
    userconfigid INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    userid INT COMMENT '用户ID',
    configtype INT COMMENT '配置类型 1TTS',
    configkey VARCHAR(255) COMMENT '用户信息配置键',
    configvalue VARCHAR(255) COMMENT '用户信息配置值'
) COMMENT='User configuration table';

CREATE INDEX idx_userid_usertype ON userconfig (userid, configtype);
