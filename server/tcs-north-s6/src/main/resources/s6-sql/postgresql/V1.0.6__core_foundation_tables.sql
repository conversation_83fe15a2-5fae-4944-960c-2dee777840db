-- Account table
CREATE TABLE tbl_account (
                             UserId SERIAL PRIMARY KEY,
                             UserName VARCHAR(128) NOT NULL,
                             LogonId VARCHAR(20) NOT NULL,
                             Password VARCHAR(128) DEFAULT NULL,
                             Enable BOOLEAN NOT NULL DEFAULT TRUE,
                             MaxError INT DEFAULT NULL,
                             Locked BOOLEAN NOT NULL DEFAULT FALSE,
                             ValidTime TIMESTAMP DEFAULT NULL,
                             Description VARCHAR(255) DEFAULT NULL,
                             IsRemote BOOLEAN NOT NULL DEFAULT FALSE,
                             CenterId INT DEFAULT NULL,
                             PasswordValidTime TIMESTAMP DEFAULT NULL,
                             ThemeName VARCHAR(128) DEFAULT NULL,
                             NeedResetPwd BOOLEAN NOT NULL DEFAULT FALSE,
                             Avatar VARCHAR(256) DEFAULT NULL
);

-- Config change tables
CREATE TABLE tbl_configchangedefine (
                                        ConfigId INT PRIMARY KEY,
                                        EntityName VARCHAR(255) NOT NULL,
                                        TableName VARCHAR(255) NOT NULL,
                                        IdDefine VARCHAR(255) DEFAULT NULL
);

CREATE TABLE tbl_configchangemacrolog (
                                          ObjectId VARCHAR(255) NOT NULL,
                                          ConfigId INT NOT NULL,
                                          EditType INT NOT NULL,
                                          UpdateTime TIMESTAMP NOT NULL
);

CREATE INDEX IDX_configchangemacrolog_1 ON tbl_configchangemacrolog(UpdateTime DESC, ConfigId);
CREATE INDEX IDX_configchangemacrolog_2 ON tbl_configchangemacrolog(ObjectId, ConfigId, EditType);

CREATE TABLE tbl_configchangemap (
                                     MicroConfigId INT NOT NULL,
                                     MicroEditType INT NOT NULL,
                                     MacroConfigId INT NOT NULL,
                                     MacroEditType INT NOT NULL,
                                     IdConvertRule VARCHAR(255) DEFAULT NULL
);

CREATE TABLE tbl_configchangemicrolog (
                                          ObjectId VARCHAR(255) NOT NULL,
                                          ConfigId INT NOT NULL,
                                          EditType INT NOT NULL,
                                          UpdateTime TIMESTAMP NOT NULL
);
CREATE INDEX IDX_configchangemicrolog_1 ON tbl_configchangemicrolog(ObjectId, ConfigId, EditType);

-- Control tables
CREATE TABLE tbl_control (
                             Id SERIAL PRIMARY KEY,
                             EquipmentTemplateId INT NOT NULL,
                             ControlId INT NOT NULL,
                             ControlName VARCHAR(128) NOT NULL,
                             ControlCategory INT NOT NULL,
                             CmdToken TEXT NOT NULL,
                             BaseTypeId DECIMAL(12,0) DEFAULT NULL,
                             ControlSeverity INT NOT NULL,
                             SignalId INT DEFAULT NULL,
                             TimeOut DOUBLE PRECISION DEFAULT NULL,  -- 修改为 DOUBLE PRECISION
                             Retry INT DEFAULT NULL,
                             Description VARCHAR(255) DEFAULT NULL,
                             Enable BOOLEAN NOT NULL,
                             Visible BOOLEAN NOT NULL,
                             DisplayIndex INT NOT NULL,
                             CommandType INT NOT NULL,
                             ControlType SMALLINT DEFAULT NULL,
                             DataType SMALLINT DEFAULT NULL,
                             MaxValue DOUBLE PRECISION NOT NULL,  -- 修改为 DOUBLE PRECISION
                             MinValue DOUBLE PRECISION NOT NULL,  -- 修改为 DOUBLE PRECISION
                             DefaultValue DOUBLE PRECISION DEFAULT NULL,  -- 修改为 DOUBLE PRECISION
                             ModuleNo INT NOT NULL DEFAULT 0
);


CREATE INDEX IDX_TBLControl_1 ON tbl_control(EquipmentTemplateId);
CREATE INDEX IDX_TBLControl_2 ON tbl_control(EquipmentTemplateId, ControlId);

CREATE TABLE tbl_controllogaction (
                                      LogActionId INT NOT NULL,
                                      ActionId INT NOT NULL,
                                      ActionName VARCHAR(50) DEFAULT NULL,
                                      EquipmentId INT DEFAULT NULL,
                                      ControlId INT DEFAULT NULL,
                                      ActionValue VARCHAR(255) DEFAULT NULL
);

CREATE TABLE tbl_controlmeanings (
                                     Id SERIAL PRIMARY KEY,
                                     EquipmentTemplateId INT NOT NULL,
                                     ControlId INT NOT NULL,
                                     ParameterValue SMALLINT NOT NULL,
                                     Meanings VARCHAR(255) DEFAULT NULL,
                                     BaseCondId DECIMAL(12,0) DEFAULT NULL
);

CREATE INDEX IDX_TBLControlMeanings_1 ON tbl_controlmeanings(EquipmentTemplateId, ControlId);

-- Data tables
CREATE TABLE tbl_dataentry (
                               EntryId INT PRIMARY KEY,
                               EntryCategory INT DEFAULT NULL,
                               EntryName VARCHAR(128) DEFAULT NULL,
                               EntryTitle VARCHAR(128) DEFAULT NULL,
                               EntryAlias VARCHAR(255) DEFAULT NULL,
                               Enable BOOLEAN NOT NULL DEFAULT TRUE,
                               Description VARCHAR(255) DEFAULT NULL
);

CREATE TABLE tbl_dataitem (
                              EntryItemId INT PRIMARY KEY,
                              ParentEntryId INT NOT NULL DEFAULT 0,
                              ParentItemId INT NOT NULL DEFAULT 0,
                              EntryId INT NOT NULL,
                              ItemId INT NOT NULL,
                              ItemValue VARCHAR(128) NOT NULL,
                              ItemAlias VARCHAR(255) DEFAULT NULL,
                              Enable BOOLEAN NOT NULL DEFAULT TRUE,
                              IsSystem BOOLEAN NOT NULL DEFAULT TRUE,
                              IsDefault BOOLEAN NOT NULL DEFAULT FALSE,
                              Description VARCHAR(255) DEFAULT NULL,
                              ExtendField1 VARCHAR(255) DEFAULT NULL,
                              ExtendField2 VARCHAR(255) DEFAULT NULL,
                              ExtendField3 VARCHAR(255) DEFAULT NULL,
                              ExtendField4 VARCHAR(255) DEFAULT NULL,
                              ExtendField5 VARCHAR(255) DEFAULT NULL,
                              CONSTRAINT tbl_dataitem_Idx1 UNIQUE (EntryId, ItemId)
);

CREATE INDEX IDX_DataItem_EntryId ON tbl_dataitem(EntryId);

-- Employee table
CREATE TABLE tbl_employee (
                              EmployeeId SERIAL PRIMARY KEY,
                              DepartmentId INT DEFAULT NULL,
                              EmployeeName VARCHAR(128) NOT NULL,
                              EmployeeType INT DEFAULT NULL,
                              EmployeeTitle INT DEFAULT NULL,
                              JobNumber VARCHAR(20) NOT NULL,
                              Gender INT DEFAULT NULL,
                              Mobile VARCHAR(50) DEFAULT NULL,
                              Phone VARCHAR(50) DEFAULT NULL,
                              Email VARCHAR(128) DEFAULT NULL,
                              Address VARCHAR(255) DEFAULT NULL,
                              PostAddress VARCHAR(255) DEFAULT NULL,
                              Enable BOOLEAN NOT NULL DEFAULT TRUE,
                              Description VARCHAR(255) DEFAULT NULL,
                              IsAddTempUser BOOLEAN NOT NULL DEFAULT FALSE,
                              UserValidTime INT DEFAULT 172800
);

CREATE INDEX DepartmentId ON tbl_employee(DepartmentId);

-- Equipment tables
CREATE TABLE tbl_equipment (
                               StationId INT NOT NULL,
                               EquipmentId SERIAL PRIMARY KEY,
                               EquipmentName VARCHAR(128) NOT NULL,
                               EquipmentNo VARCHAR(128) NOT NULL,
                               EquipmentModule VARCHAR(128) DEFAULT NULL,
                               EquipmentStyle VARCHAR(128) DEFAULT NULL,
                               AssetState INT DEFAULT NULL,
                               Price DOUBLE PRECISION DEFAULT NULL,
                               UsedLimit DOUBLE PRECISION DEFAULT NULL,
                               UsedDate TIMESTAMP DEFAULT NULL,
                               BuyDate TIMESTAMP DEFAULT NULL,
                               Vendor VARCHAR(255) DEFAULT NULL,
                               Unit VARCHAR(255) DEFAULT NULL,
                               EquipmentCategory INT NOT NULL,
                               EquipmentType INT NOT NULL,
                               EquipmentClass INT DEFAULT NULL,
                               EquipmentState INT NOT NULL,
                               EventExpression VARCHAR(255) DEFAULT NULL,
                               StartDelay DOUBLE PRECISION DEFAULT NULL,
                               EndDelay DOUBLE PRECISION DEFAULT NULL,
                               Property VARCHAR(255) DEFAULT NULL,
                               Description VARCHAR(255) DEFAULT NULL,
                               EquipmentTemplateId INT DEFAULT NULL,
                               HouseId INT DEFAULT NULL,
                               MonitorUnitId INT NOT NULL,
                               WorkStationId INT DEFAULT NULL,
                               SamplerUnitId INT NOT NULL,
                               DisplayIndex INT NOT NULL,
                               ConnectState INT NOT NULL,
                               UpdateTime TIMESTAMP NOT NULL,
                               ParentEquipmentId VARCHAR(255) DEFAULT NULL,
                               RatedCapacity VARCHAR(255) DEFAULT NULL,
                               InstalledModule TEXT NOT NULL,
                               ProjectName VARCHAR(255) DEFAULT NULL,
                               ContractNo VARCHAR(255) DEFAULT NULL,
                               InstallTime TIMESTAMP DEFAULT NULL,
                               EquipmentSN VARCHAR(255) DEFAULT NULL,
                               SO VARCHAR(255) DEFAULT NULL,
                               ResourceStructureId INT DEFAULT 0,
                               ExtValue TEXT DEFAULT NULL,
                               photo VARCHAR(255) DEFAULT NULL
);

CREATE INDEX IDC_EquipmentId_MonitorUnit_ID ON tbl_equipment(MonitorUnitId);
CREATE INDEX IDX_EquipmentId_1 ON tbl_equipment(MonitorUnitId, SamplerUnitId);
CREATE INDEX IDX_Equipment_ResourceStructureId ON tbl_equipment(ResourceStructureId);
CREATE INDEX IDX_Equipment_StationId ON tbl_equipment(StationId, HouseId);
CREATE INDEX IDX_EquipmentTemplateId ON tbl_equipment(EquipmentTemplateId);

CREATE TABLE tbl_equipmentprojectinfo (
                                          StationId INT NOT NULL,
                                          MonitorUnitId INT NOT NULL,
                                          EquipmentId INT NOT NULL,
                                          ProjectName VARCHAR(255) DEFAULT NULL,
                                          ContractNo VARCHAR(255) DEFAULT NULL,
                                          InstallTime TIMESTAMP DEFAULT NULL,
                                          EquipmentSN VARCHAR(255) DEFAULT NULL,
                                          SO VARCHAR(255) DEFAULT NULL,
                                          CONSTRAINT TBL_EquipmentProjectInfo_IDX1 UNIQUE (StationId, EquipmentId)
);

CREATE TABLE tbl_equipmenttemplate (
                                       EquipmentTemplateId SERIAL PRIMARY KEY,
                                       EquipmentTemplateName VARCHAR(128) NOT NULL,
                                       ParentTemplateId INT NOT NULL,
                                       Memo VARCHAR(255) NOT NULL,
                                       ProtocolCode VARCHAR(32) NOT NULL,
                                       EquipmentCategory INT NOT NULL,
                                       EquipmentType INT NOT NULL,
                                       Property VARCHAR(255) DEFAULT NULL,
                                       Description VARCHAR(255) DEFAULT NULL,
                                       EquipmentStyle VARCHAR(128) DEFAULT NULL,
                                       Unit VARCHAR(255) DEFAULT NULL,
                                       Vendor VARCHAR(255) DEFAULT NULL,
                                       Photo VARCHAR(255) DEFAULT NULL,
                                       EquipmentBaseType INT DEFAULT NULL,
                                       StationCategory INT DEFAULT NULL,
                                       ExtendField1 VARCHAR(255) DEFAULT NULL
);

-- Event tables
CREATE TABLE tbl_event (
                           Id SERIAL PRIMARY KEY,
                           EquipmentTemplateId INT NOT NULL,
                           EventId INT NOT NULL,
                           EventName VARCHAR(128) NOT NULL,
                           StartType INT NOT NULL,
                           EndType INT NOT NULL,
                           StartExpression TEXT,
                           SuppressExpression TEXT,
                           EventCategory INT NOT NULL,
                           SignalId INT DEFAULT NULL,
                           Enable BOOLEAN NOT NULL,
                           Visible BOOLEAN NOT NULL,
                           Description VARCHAR(255) DEFAULT NULL,
                           DisplayIndex INT DEFAULT NULL,
                           ModuleNo INT NOT NULL DEFAULT 0
);

CREATE INDEX IDX_TBLEvent_1 ON tbl_event(EquipmentTemplateId);
CREATE INDEX IDX_TBLEvent_2 ON tbl_event(EquipmentTemplateId, EventId);
CREATE INDEX IDX_Event_Signal_1 ON tbl_event(EquipmentTemplateId, SignalId);

CREATE TABLE tbl_eventcondition (
                                    Id SERIAL PRIMARY KEY,
                                    EventConditionId INT NOT NULL,
                                    EquipmentTemplateId INT NOT NULL,
                                    EventId INT NOT NULL,
                                    StartOperation VARCHAR(4) NOT NULL,
                                    StartCompareValue DOUBLE PRECISION NOT NULL,
                                    StartDelay INT NOT NULL,
                                    EndOperation VARCHAR(4) DEFAULT NULL,
                                    EndCompareValue DOUBLE PRECISION DEFAULT NULL,
                                    EndDelay INT DEFAULT NULL,
                                    Frequency INT DEFAULT NULL,
                                    FrequencyThreshold INT DEFAULT NULL,
                                    Meanings VARCHAR(255) DEFAULT NULL,
                                    EquipmentState INT DEFAULT NULL,
                                    BaseTypeId DECIMAL(12,0) DEFAULT NULL,
                                    EventSeverity INT NOT NULL,
                                    StandardName INT DEFAULT NULL
);

CREATE INDEX IDX_TBLEventCondition_1 ON tbl_eventcondition(EquipmentTemplateId, EventId);

CREATE TABLE tbl_eventex (
                             Id SERIAL PRIMARY KEY,
                             EquipmentTemplateId INT NOT NULL,
                             EventId INT NOT NULL,
                             turnover INT DEFAULT NULL,
                             ExtendField1 VARCHAR(20) DEFAULT NULL
);

CREATE INDEX IDX_TBLEventex_1 ON tbl_eventex(EquipmentTemplateId);
CREATE INDEX IDX_TBLEventex_2 ON tbl_eventex(EquipmentTemplateId, EventId);

CREATE TABLE tbl_eventlogaction (
                                    LogActionId INT NOT NULL,
                                    ActionName VARCHAR(255) NOT NULL,
                                    StationId INT NOT NULL,
                                    MonitorUnitId INT NOT NULL,
                                    TriggerType INT NOT NULL,
                                    StartExpression VARCHAR(255) DEFAULT NULL,
                                    SuppressExpression VARCHAR(255) DEFAULT NULL,
                                    InformMsg VARCHAR(255) DEFAULT NULL,
                                    Description VARCHAR(255) DEFAULT NULL
);

-- Station and structure tables
CREATE TABLE tbl_house (
                           HouseId INT PRIMARY KEY,
                           StationId INT NOT NULL,
                           HouseName VARCHAR(128) NOT NULL,
                           Description VARCHAR(255) DEFAULT NULL,
                           LastUpdateDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tbl_logicclassentry (
                                     EntryId INT PRIMARY KEY,
                                     EntryCategory INT DEFAULT NULL,
                                     LogicClassId INT DEFAULT NULL,
                                     LogicClass VARCHAR(128) DEFAULT NULL,
                                     StandardType INT NOT NULL,
                                     Description VARCHAR(255) DEFAULT NULL
);

CREATE TABLE tbl_loginformlist (
                                   LogActionId INT NOT NULL,
                                   InformerId INT NOT NULL,
                                   UserId INT DEFAULT NULL,
                                   InfoType INT DEFAULT NULL,
                                   Description VARCHAR(255) DEFAULT NULL
);

CREATE TABLE tbl_monitorunitprojectinfo (
                                            StationId INT NOT NULL,
                                            MonitorUnitId INT NOT NULL,
                                            ProjectName VARCHAR(255) DEFAULT NULL,
                                            ContractNo VARCHAR(255) DEFAULT NULL,
                                            InstallTime TIMESTAMP DEFAULT NULL,
                                            CONSTRAINT TBL_MUProjectInfo_IDX1 UNIQUE (StationId, MonitorUnitId)
);

-- Operation detail table
CREATE TABLE tbl_operationdetail (
                                     UserId INT NOT NULL,
                                     ObjectId VARCHAR(128) DEFAULT NULL,
                                     ObjectType INT NOT NULL,
                                     PropertyName VARCHAR(128) DEFAULT NULL,
                                     OperationTime TIMESTAMP NOT NULL,
                                     OperationType VARCHAR(64) NOT NULL,
                                     OldValue VARCHAR(4000) DEFAULT NULL,
                                     NewValue VARCHAR(4000) DEFAULT NULL
);

-- Origin business category map table
CREATE TABLE tbl_originbussinesscategorymap (
                                                EquipmentTemplateId INT NOT NULL,
                                                OriginCategory INT NOT NULL,
                                                PRIMARY KEY (EquipmentTemplateId)
);

-- Primary key identity table
CREATE TABLE tbl_primarykeyidentity (
                                        TableId INT NOT NULL,
                                        TableName VARCHAR(30) DEFAULT NULL,
                                        Description VARCHAR(255) DEFAULT NULL,
                                        PRIMARY KEY (TableId)
);

-- Primary key value table
CREATE TABLE tbl_primarykeyvalue (
                                     TableId INT NOT NULL,
                                     PostalCode INT NOT NULL,
                                     MinValue INT DEFAULT NULL,
                                     CurrentValue INT DEFAULT NULL,
                                     PRIMARY KEY (PostalCode, TableId)
);

-- Signal tables
CREATE TABLE tbl_signal (
                            Id SERIAL PRIMARY KEY,
                            EquipmentTemplateId INT NOT NULL,
                            SignalId INT NOT NULL,
                            Enable BOOLEAN NOT NULL,
                            Visible BOOLEAN NOT NULL,
                            Description VARCHAR(255) DEFAULT NULL,
                            SignalName VARCHAR(128) NOT NULL,
                            SignalCategory INT NOT NULL,
                            SignalType INT NOT NULL,
                            ChannelNo INT NOT NULL,
                            ChannelType INT NOT NULL,
                            Expression TEXT,
                            DataType INT DEFAULT NULL,
                            ShowPrecision VARCHAR(20) DEFAULT NULL,
                            Unit VARCHAR(64) DEFAULT NULL,
                            StoreInterval DOUBLE PRECISION DEFAULT NULL,
                            AbsValueThreshold DOUBLE PRECISION DEFAULT NULL,
                            PercentThreshold DOUBLE PRECISION DEFAULT NULL,
                            StaticsPeriod INT DEFAULT NULL,
                            BaseTypeId DECIMAL(12,0) DEFAULT NULL,
                            ChargeStoreInterVal DOUBLE PRECISION DEFAULT NULL,
                            ChargeAbsValue DOUBLE PRECISION DEFAULT NULL,
                            DisplayIndex INT NOT NULL,
                            MDBSignalId INT DEFAULT NULL,
                            ModuleNo INT NOT NULL DEFAULT 0
);

CREATE INDEX IDX_TBLSignal_1 ON tbl_signal(EquipmentTemplateId);
CREATE INDEX IDX_TBLSignal_2 ON tbl_signal(EquipmentTemplateId, SignalId);
CREATE INDEX IDX_TBLSignal_3 ON tbl_signal(EquipmentTemplateId, BaseTypeId);
CREATE INDEX IDX_BaseTypeId ON tbl_signal(BaseTypeId);

CREATE TABLE tbl_signalmeanings (
                                    Id SERIAL PRIMARY KEY,
                                    EquipmentTemplateId INT NOT NULL,
                                    SignalId INT NOT NULL,
                                    StateValue SMALLINT NOT NULL,
                                    Meanings VARCHAR(255) DEFAULT NULL,
                                    BaseCondId DECIMAL(12,0) DEFAULT NULL,
                                    CONSTRAINT CLUSTERED UNIQUE (EquipmentTemplateId, SignalId, StateValue)
);
CREATE INDEX IDX_TBLSignalMeanings_1 ON tbl_signalmeanings(EquipmentTemplateId, SignalId);

CREATE TABLE tbl_signalproperty (
                                    Id SERIAL PRIMARY KEY,
                                    EquipmentTemplateId INT NOT NULL,
                                    SignalId INT NOT NULL,
                                    SignalPropertyId INT NOT NULL,
                                    CONSTRAINT index_27 UNIQUE (EquipmentTemplateId, SignalId, SignalPropertyId)
);
CREATE INDEX IDX_TBLSignalProperty_1 ON tbl_signalproperty(EquipmentTemplateId, SignalId);

-- Station tables
CREATE TABLE tbl_station (
                             StationId INT PRIMARY KEY,
                             StationName VARCHAR(255) NOT NULL,
                             Latitude DECIMAL(22,17) DEFAULT NULL,
                             Longitude DECIMAL(22,17) DEFAULT NULL,
                             SetupTime TIMESTAMP DEFAULT NULL,
                             CompanyId INT DEFAULT NULL,
                             ConnectState INT NOT NULL DEFAULT 2,
                             UpdateTime TIMESTAMP NOT NULL,
                             StationCategory INT NOT NULL,
                             StationGrade INT NOT NULL,
                             StationState INT NOT NULL,
                             ContactId INT DEFAULT NULL,
                             SupportTime INT DEFAULT NULL,
                             OnWayTime DOUBLE PRECISION DEFAULT NULL,
                             SurplusTime DOUBLE PRECISION DEFAULT NULL,
                             FloorNo VARCHAR(50) DEFAULT NULL,
                             PropList VARCHAR(255) DEFAULT NULL,
                             Acreage DOUBLE PRECISION DEFAULT NULL,
                             BuildingType INT DEFAULT NULL,
                             ContainNode BOOLEAN NOT NULL DEFAULT FALSE,
                             Description VARCHAR(255) DEFAULT NULL,
                             BordNumber INT DEFAULT NULL,
                             CenterId INT NOT NULL,
                             Enable BOOLEAN NOT NULL DEFAULT TRUE,
                             StartTime TIMESTAMP DEFAULT NULL,
                             EndTime TIMESTAMP DEFAULT NULL,
                             ProjectName VARCHAR(255) DEFAULT NULL,
                             ContractNo VARCHAR(255) DEFAULT NULL,
                             InstallTime TIMESTAMP DEFAULT NULL
);

CREATE TABLE tbl_stationbasemap (
                                    StationBaseType INT NOT NULL,
                                    StationCategory INT NOT NULL,
                                    StandardType INT NOT NULL,
                                    PRIMARY KEY (StandardType, StationBaseType, StationCategory)
);

CREATE TABLE tbl_stationbasetype (
                                     Id INT NOT NULL,
                                     StandardId INT NOT NULL,
                                     Type VARCHAR(128) DEFAULT NULL,
                                     PRIMARY KEY (Id, StandardId)
);

CREATE TABLE tbl_stationmask (
                                 StationId INT NOT NULL,
                                 TimeGroupId INT DEFAULT NULL,
                                 Reason VARCHAR(255) DEFAULT NULL,
                                 StartTime TIMESTAMP DEFAULT NULL,
                                 EndTime TIMESTAMP DEFAULT NULL,
                                 UserId INT DEFAULT NULL,
                                 PRIMARY KEY (StationId)
);

CREATE TABLE tbl_stationprojectinfo (
                                        StationId INT NOT NULL,
                                        ProjectName VARCHAR(255) DEFAULT NULL,
                                        ContractNo VARCHAR(255) DEFAULT NULL,
                                        InstallTime TIMESTAMP DEFAULT NULL,
                                        CONSTRAINT TBL_StationProjectInfo_IDX1 UNIQUE (StationId)
);

CREATE TABLE tbl_stationstructure (
                                      StructureId INT PRIMARY KEY,
                                      StructureGroupId INT NOT NULL,
                                      ParentStructureId INT NOT NULL,
                                      StructureName VARCHAR(128) NOT NULL,
                                      IsUngroup BOOLEAN NOT NULL,
                                      StructureType INT NOT NULL,
                                      MapZoom DOUBLE PRECISION DEFAULT NULL,
                                      Longitude DECIMAL(22,17) DEFAULT NULL,
                                      Latitude DECIMAL(22,17) DEFAULT NULL,
                                      Description VARCHAR(255) DEFAULT NULL,
                                      LevelPath VARCHAR(200) NOT NULL,
                                      Enable BOOLEAN NOT NULL
);

CREATE TABLE tbl_stationstructuremap (
                                         StructureId INT NOT NULL,
                                         StationId INT NOT NULL,
                                         PRIMARY KEY (StationId, StructureId)
);

CREATE TABLE tbl_stationswatchmap (
                                      SwatchStationId INT NOT NULL,
                                      StationId INT NOT NULL
);

CREATE TABLE tbl_swatchstation (
                                   SwatchStationId SERIAL PRIMARY KEY,
                                   SwatchStationName VARCHAR(128) NOT NULL,
                                   StationId INT NOT NULL,
                                   CreateTime TIMESTAMP NOT NULL,
                                   Description VARCHAR(255) DEFAULT NULL
);
CREATE INDEX SwatchStationId ON tbl_swatchstation(SwatchStationId);

-- System tables
CREATE TABLE tbl_sysconfig (
                               ConfigKey VARCHAR(255) NOT NULL,
                               ConfigValue VARCHAR(1024) DEFAULT NULL
);
CREATE INDEX IDX_SysConfig_1 ON tbl_sysconfig(ConfigKey);

CREATE TABLE tbl_userrole (
                              RoleId INT PRIMARY KEY,
                              RoleName VARCHAR(128) NOT NULL,
                              Description VARCHAR(255) DEFAULT NULL
);

CREATE TABLE tbl_userrolemap (
                                 UserId INT NOT NULL,
                                 RoleId INT NOT NULL,
                                 PRIMARY KEY (RoleId, UserId)
);

CREATE TABLE tbl_workstation (
                                 WorkStationId INT PRIMARY KEY,
                                 WorkStationName VARCHAR(255) NOT NULL,
                                 WorkStationType INT NOT NULL,
                                 IPAddress VARCHAR(64) NOT NULL,
                                 ParentId INT NOT NULL DEFAULT 0,
                                 ConnectState INT NOT NULL,
                                 UpdateTime TIMESTAMP NOT NULL,
                                 IsUsed BOOLEAN NOT NULL DEFAULT TRUE,
                                 CPU DOUBLE PRECISION DEFAULT NULL,
                                 Memory DOUBLE PRECISION DEFAULT NULL,
                                 ThreadCount INT DEFAULT NULL,
                                 DiskFreeSpace DOUBLE PRECISION DEFAULT NULL,
                                 DBFreeSpace DOUBLE PRECISION DEFAULT NULL,
                                 LastCommTime TIMESTAMP DEFAULT NULL
);

CREATE TABLE tbl_writebackentry (
                                    EntryId INT PRIMARY KEY,
                                    EntryCategory INT DEFAULT NULL,
                                    EntryName VARCHAR(128) DEFAULT NULL,
                                    EntryTitle VARCHAR(128) DEFAULT NULL,
                                    EntryAlias VARCHAR(255) DEFAULT NULL,
                                    Enable BOOLEAN NOT NULL DEFAULT TRUE,
                                    Description VARCHAR(255) DEFAULT NULL
);

-- TSL tables
CREATE TABLE tsl_acrossmonitorunitsignal (
                                             StationId INT NOT NULL,
                                             MonitorUnitId INT NOT NULL,
                                             EquipmentId INT NOT NULL,
                                             SignalId INT NOT NULL,
                                             Expression TEXT,
                                             PRIMARY KEY (EquipmentId, SignalId, StationId)
);

CREATE TABLE tsl_channelmap (
                                SamplerUnitId INT NOT NULL,
                                MonitorUnitId INT NOT NULL,
                                OriginalChannelNo INT NOT NULL,
                                StandardChannelNo INT NOT NULL,
                                PRIMARY KEY (SamplerUnitId)
);

CREATE TABLE tsl_monitorunit (
                                 MonitorUnitId INT PRIMARY KEY,
                                 MonitorUnitName VARCHAR(128) NOT NULL,
                                 MonitorUnitCategory INT NOT NULL,
                                 MonitorUnitCode VARCHAR(128) NOT NULL,
                                 WorkStationId INT DEFAULT NULL,
                                 StationId INT DEFAULT NULL,
                                 IpAddress VARCHAR(128) DEFAULT NULL,
                                 RunMode INT DEFAULT NULL,
                                 ConfigFileCode VARCHAR(32) DEFAULT NULL,
                                 ConfigUpdateTime TIMESTAMP DEFAULT NULL,
                                 SampleConfigCode VARCHAR(32) DEFAULT NULL,
                                 SoftwareVersion VARCHAR(64) DEFAULT NULL,
                                 Description VARCHAR(255) DEFAULT NULL,
                                 StartTime TIMESTAMP DEFAULT NULL,
                                 HeartbeatTime TIMESTAMP DEFAULT NULL,
                                 ConnectState INT NOT NULL DEFAULT 2,
                                 UpdateTime TIMESTAMP NOT NULL,
                                 IsSync BOOLEAN NOT NULL DEFAULT TRUE,
                                 SyncTime TIMESTAMP DEFAULT NULL,
                                 IsConfigOK BOOLEAN NOT NULL DEFAULT TRUE,
                                 ConfigFileCode_Old VARCHAR(32) DEFAULT NULL,
                                 SampleConfigCode_Old VARCHAR(32) DEFAULT NULL,
                                 AppCongfigId INT DEFAULT NULL,
                                 CanDistribute BOOLEAN NOT NULL,
                                 Enable BOOLEAN NOT NULL,
                                 ProjectName VARCHAR(255) DEFAULT NULL,
                                 ContractNo VARCHAR(255) DEFAULT NULL,
                                 InstallTime TIMESTAMP DEFAULT NULL,
                                 FSU BOOLEAN DEFAULT FALSE
);
CREATE INDEX IDX_MonitorUnit_WorkStationId ON tsl_monitorunit(WorkStationId);
CREATE INDEX IDX_MonitorUnit_StationId ON tsl_monitorunit(StationId);

CREATE TABLE tsl_monitorunitconfig (
                                       Id SERIAL PRIMARY KEY,
                                       AppConfigId INT NOT NULL,
                                       SiteWebTimeOut INT NOT NULL,
                                       RetryTimes INT NOT NULL,
                                       HeartBeat INT NOT NULL,
                                       EquipmentTimeOut INT NOT NULL,
                                       PortInterruptCount INT NOT NULL,
                                       PortInitializeInternal INT NOT NULL,
                                       MaxPortInitializeTimes INT NOT NULL,
                                       PortQueryTimeOut INT NOT NULL,
                                       DataSaveTimes INT NOT NULL,
                                       HistorySignalSavedTimes INT NOT NULL,
                                       HistoryBatterySavedTimes INT NOT NULL,
                                       HistoryEventSavedTimes INT NOT NULL,
                                       CardRecordSavedCount INT NOT NULL,
                                       ControlLog BOOLEAN NOT NULL,
                                       IpAddressDS VARCHAR(128) DEFAULT NULL
);

CREATE TABLE tsl_monitorunitevent (
                                      StationId INT NOT NULL,
                                      MonitorUnitId INT NOT NULL,
                                      EquipmentId INT NOT NULL,
                                      EventId INT NOT NULL,
                                      StartExpression VARCHAR(255) DEFAULT NULL,
                                      SuppressExpression TEXT,
                                      PRIMARY KEY (EquipmentId, EventId, StationId)
);

CREATE TABLE tsl_monitorunitsignal (
                                       StationId INT NOT NULL,
                                       MonitorUnitId INT NOT NULL,
                                       EquipmentId INT NOT NULL,
                                       SignalId INT NOT NULL,
                                       ReferenceSamplerUnitId INT DEFAULT NULL,
                                       ReferenceChannelNo INT DEFAULT NULL,
                                       Expression TEXT,
                                       InstanceType INT NOT NULL,
                                       PRIMARY KEY (EquipmentId, SignalId, StationId)
);

CREATE TABLE tsl_port (
                          Id SERIAL PRIMARY KEY,
                          PortId INT NOT NULL,
                          MonitorUnitId INT NOT NULL,
                          PortNo INT NOT NULL,
                          PortName VARCHAR(128) NOT NULL,
                          PortType INT NOT NULL,
                          Setting VARCHAR(255) NOT NULL,
                          PhoneNumber VARCHAR(128) DEFAULT NULL,
                          LinkSamplerUnitId INT DEFAULT NULL,
                          Description VARCHAR(255) DEFAULT NULL
);
CREATE INDEX IDX_Port_1 ON tsl_port(MonitorUnitId, PortId);
CREATE INDEX IDX_Port_MonitorUnitId ON tsl_port(MonitorUnitId, PortId);

CREATE TABLE tsl_sampler (
                             SamplerId SERIAL PRIMARY KEY,
                             SamplerName VARCHAR(128) NOT NULL,
                             SamplerType SMALLINT NOT NULL,
                             ProtocolCode VARCHAR(32) NOT NULL,
                             DLLCode VARCHAR(255) NOT NULL,
                             DLLVersion VARCHAR(32) NOT NULL,
                             ProtocolFilePath VARCHAR(255) NOT NULL,
                             DLLFilePath VARCHAR(255) NOT NULL,
                             DllPath VARCHAR(255) NOT NULL,
                             Setting VARCHAR(255) DEFAULT NULL,
                             Description VARCHAR(255) DEFAULT NULL,
                             SoCode VARCHAR(255) NOT NULL,
                             SoPath VARCHAR(255) NOT NULL,
                             UploadProtocolFile BOOLEAN DEFAULT FALSE,
                             CONSTRAINT uniqueProtocolcode UNIQUE (ProtocolCode)
);

CREATE TABLE tsl_samplerunit (
                                 Id SERIAL PRIMARY KEY,
                                 SamplerUnitId INT NOT NULL,
                                 PortId INT NOT NULL,
                                 MonitorUnitId INT NOT NULL,
                                 SamplerId INT NOT NULL,
                                 ParentSamplerUnitId INT NOT NULL,
                                 SamplerType INT NOT NULL,
                                 SamplerUnitName VARCHAR(128) NOT NULL,
                                 Address INT NOT NULL,
                                 SpUnitInterval DOUBLE PRECISION DEFAULT NULL,
                                 DllPath VARCHAR(128) DEFAULT NULL,
                                 ConnectState INT NOT NULL,
                                 UpdateTime TIMESTAMP NOT NULL,
                                 PhoneNumber VARCHAR(128) DEFAULT NULL,
                                 Description VARCHAR(255) DEFAULT NULL
);
CREATE INDEX IDX_SamplerUnit_1 ON tsl_samplerunit(MonitorUnitId, SamplerUnitId);
CREATE INDEX IDX_SamplerUnit_MonitorUnitId ON tsl_samplerunit(MonitorUnitId, PortId, SamplerUnitId);
CREATE INDEX IDX_SamplerUnit_SamplerUnitId ON tsl_samplerunit(SamplerUnitId);
