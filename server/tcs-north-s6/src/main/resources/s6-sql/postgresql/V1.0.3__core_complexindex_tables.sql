-- 复杂索引表
CREATE TABLE complexindex (
                              complexindexid SERIAL PRIMARY KEY,  -- 自动增长的主键
                              complexindexname VARCHAR(128),
                              complexindexdefinitionid INT,
                              objectid INT,
                              calccron VARCHAR(128),
                              calctype INT,
                              aftercalc VARCHAR(128),
                              savecron VARCHAR(128),
                              expression TEXT,
                              unit VARCHAR(128),
                              accuracy VARCHAR(128),
                              objecttypeid INT,
                              remark VARCHAR(128),
                              label VARCHAR(128),
                              businesstypeid INT,
                              checkexpression VARCHAR(1024)
);
