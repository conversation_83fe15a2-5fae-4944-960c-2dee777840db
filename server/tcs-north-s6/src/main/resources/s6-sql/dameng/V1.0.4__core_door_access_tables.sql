CREATE TABLE "tbl_card" (
  "CardId" INT NOT NULL,
  "CardCode" VARCHAR(20) NOT NULL,
  "CardName" VARCHAR(128) DEFAULT NULL,
  "CardCategory" INT DEFAULT NULL,
  "CardGroup" INT DEFAULT NULL,
  "UserId" INT DEFAULT NULL,
  "StationId" INT DEFAULT NULL,
  "CardStatus" INT DEFAULT NULL,
  "StartTime" TIMESTAMP DEFAULT NULL,
  "EndTime" TIMESTAMP DEFAULT NULL,
  "RegisterTime" TIMESTAMP DEFAULT NULL,
  "UnRegisterTime" TIMESTAMP DEFAULT NULL,
  "LostTime" TIMESTAMP DEFAULT NULL,
  "Description" VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY ("CardId")
);

CREATE TABLE "tbl_door" (
  "DoorId" INT NOT NULL,
  "<PERSON>No" INT NOT NULL,
  "DoorName" VARCHAR(128) DEFAULT NULL,
  "StationId" INT NOT NULL,
  "EquipmentId" INT NOT NULL,
  "SamplerUnitId" INT DEFAULT NULL,
  "Category" INT NOT NULL,
  "Address" VARCHAR(255) DEFAULT NULL,
  "WorkMode" INT DEFAULT NULL,
  "Infrared" INT DEFAULT NULL,
  "Password" VARCHAR(10) DEFAULT NULL,
  "DoorControlId" INT DEFAULT NULL,
  "DoorInterval" INT DEFAULT NULL,
  "OpenDelay" INT DEFAULT NULL,
  "Description" VARCHAR(255) DEFAULT NULL,
  "OpenMode" INT DEFAULT NULL,
  PRIMARY KEY ("DoorId")
);
CREATE INDEX "idx_door_equipment" ON "tbl_door" ("EquipmentId","DoorId");

CREATE TABLE "tbl_doorcard" (
  "CardId" INT NOT NULL,
  "TimeGroupId" INT NOT NULL,
  "DoorId" INT NOT NULL,
  "StartTime" TIMESTAMP DEFAULT NULL,
  "EndTime" TIMESTAMP DEFAULT NULL,
  "Password" VARCHAR(30) DEFAULT NULL,
  "timegrouptype" INT NOT NULL,
  PRIMARY KEY ("CardId","DoorId","TimeGroupId","timegrouptype")
);

CREATE TABLE "tbl_doortimegroup" (
  "DoorId" INT NOT NULL,
  "TimeGroupId" INT NOT NULL,
  "TimeGroupType" INT NOT NULL,
  PRIMARY KEY ("DoorId","TimeGroupId","TimeGroupType")
);

CREATE TABLE "tbl_facedata" (
  "FaceId" INT NOT NULL,
  "FaceData" BLOB NOT NULL,
  PRIMARY KEY ("FaceId")
);

CREATE TABLE "tbl_fingerprint" (
  "FingerPrintId" INT NOT NULL,
  "FingerPrintNO" INT NOT NULL,
  "FingerPrintData" BLOB NOT NULL,
  PRIMARY KEY ("FingerPrintId","FingerPrintNO")
);