INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1,1,'局站树区划种类',NULL,'StationGroupType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2,1,'局站等级',NULL,'StationGrade',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3,1,'局站分类',NULL,'StationType',0,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (4,1,'局站结构定义',NULL,'StructureCategory ',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (5,1,'局站状态',NULL,'StationState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (6,1,'地图种类',NULL,'MapCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (7,1,'设备种类',NULL,'EquipmentCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (8,1,'设备分类',NULL,'EquipmentType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (9,1,'设备属性',NULL,'EquipmentProperty',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (10,1,'设备资产状态',NULL,'EquipmentAssetState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (11,1,'电池类型',NULL,'EquipmentCase',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (12,1,'电池工作状态',NULL,'EquipmentState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (13,1,'油机状态',NULL,'GeneratorState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (14,1,'设备厂商',NULL,'Vendor',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (15,1,'代维厂商',NULL,'Agent',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (16,1,'设备单位',NULL,'EquipmentUnit',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (17,1,'信号种类',NULL,'SignalCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (18,1,'信号分类',NULL,'SignalType ',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (19,1,'存盘方式',NULL,'SignalThresholdType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (20,1,'信号单位',NULL,'SignalUnit',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (21,1,'信号属性',NULL,'SignalProperty',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (22,1,'通道类型',NULL,'ChannelType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (23,1,'事件等级',NULL,'EventSeverity',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (24,1,'事件种类',NULL,'EventCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (25,1,'事件开始类型',NULL,'StartType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (26,1,'事件结束类型',NULL,'EndType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (27,1,'事件阶段',NULL,'EventPhase',0,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (28,1,'控制命令权限级',NULL,'CommandSeverity',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (29,1,'控制命令执行阶段',NULL,'CommandPhase',0,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (30,1,'控制命令结果类型',NULL,'CommandResultType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (31,1,'控制命令种类',NULL,'CommandCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (32,1,'控制命令分类',NULL,'CommandType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (33,1,'监控单元运行模式',NULL,'RunMode',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (34,1,'监控单元种类',NULL,'MonitorUnitCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (35,1,'监控单元分类',NULL,'MonitorUnitType',0,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (36,1,'监控单元工作状态',NULL,'MonitorUnitWorkState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (37,1,'采集器种类',NULL,'SamplerType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (38,1,'采集单元种类',NULL,'SamplerUnitType',0,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (39,1,'端口种类',NULL,'PortType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (40,1,'通知方式',NULL,'NotifyCategory',0,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (41,1,'员工分类',NULL,'EmployeeType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (42,1,'员工职称',NULL,'EmployeeTitle',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (43,1,'角色类型',NULL,'RoleType',0,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (44,1,'片区类型',NULL,'AreaType',0,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (45,1,'操作日志类型',NULL,'LogType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (46,1,'门禁卡状态',NULL,'CardStutas',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (47,1,'门禁卡种类',NULL,'CardCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (48,1,'门禁进出密码模式',NULL,'PasswordMode',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (49,1,'门禁刷卡标识说明',NULL,'ValidType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (50,1,'设备预警种类',NULL,'EquipmentPredictionCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (51,1,'后台服务种类',NULL,'DbTaskCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (52,1,'后台服务执行方式 ',NULL,'DbTaskExecMode',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (53,1,'后台服务间隔时间种类',NULL,'DbTaskIntervalType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (54,1,'后台服务处理方式',NULL,'DbTaskProcessType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (55,1,'报表种类',NULL,'ReportCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (56,1,'报表时间种类',NULL,'ReportTimeCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (57,1,'报表图表种类',NULL,'ChartCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (58,1,'工作台种类',NULL,'WorkStationCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (59,1,'工作台状态',NULL,'WorkStationState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (60,1,'工作台协议种类',NULL,'ProtocolCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (61,1,'工作台服务种类',NULL,'ServiceCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (62,1,'监控中心(NO.)',NULL,'CenterCode',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (63,1,'报表输出格式定义',NULL,'ReportOutputFormat',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (64,1,'定时任务执行频率定义',NULL,'TaskExecuteFrequency',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (65,1,'触发器共享模式定义',NULL,'TriggerShareMode',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (66,1,'定时任务执行模式',NULL,'TaskExecuteMode',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (67,1,'事件屏蔽种类',NULL,'TimeGroupCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (68,1,'控件种类',NULL,'ControlType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (69,1,'事件状态',NULL,'EventState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (70,1,'数据类型',NULL,'DataType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (71,1,'局站种类',NULL,'StationCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (72,1,'设备分组类型',NULL,'EquipmentGroupType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (73,1,'门禁种类',NULL,'DoorCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (74,1,'门禁红外设置类型',NULL,'DoorInfrared',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (75,1,'卡分组',NULL,'CardGroup',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (76,1,'查询时段种类',NULL,'SelectTimeCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (77,1,'自诊断设备种类',NULL,'SelfDiagnostEquipmentCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (78,1,'用户设备种类',NULL,'NonMonitorEquipmentCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (79,1,'设备监控状态',NULL,'EquipmentMonitoringState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (80,1,'登录分类',NULL,'LoginType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (81,1,'执行类型',NULL,'ExcuteType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (82,1,'部门级别',NULL,'DepartmentLevel',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (83,1,'派单状态',NULL,'EomsStatus',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (84,1,'人员性别',NULL,'Gender',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (85,1,'自诊断状态',NULL,'DiagnosisState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (86,1,'配置验证规则',NULL,'ExamineRule',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (87,1,'操作选项',NULL,'OperationCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (88,1,'网管告警级别',NULL,'NetManagement EventServerity',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (89,1,'告警类别',NULL,'EventClass',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (90,1,'告警逻辑分类',NULL,'Event Logic Category',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (91,1,'告警逻辑子类',NULL,'Event Logic Sub Category',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (92,1,'VIP级别',NULL,'VIP Level',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (93,1,'VIP类型',NULL,'VIP Category',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (94,1,'告警对象类型',NULL,'EventObjectCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (95,1,'网络类型',NULL,'Net Category',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (96,1,'网元版本',NULL,'Cell Version',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (97,1,'告警标准名',NULL,'Event Standard Name',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (98,1,'设备子类',NULL,'Equipment Sub Category',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (99,1,'专业',NULL,'Professional',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (100,1,'映射方式',NULL,'Map Category',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (101,1,'告警联动触发类型',NULL,'LogAction Trigger Type',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (102,1,'告警联动通知方式',NULL,'LogAction Inform Type',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (103,1,'控制执行类别',NULL,'ControlExcute Type',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (104,1,'网络连接类型',NULL,'Connection Type',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (105,1,'告警来源',NULL,'Alarm By',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (106,1,'统计方式',NULL,'Statistics',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (107,1,'传输协议类型',NULL,'Protocol',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (108,1,'MIB',NULL,'MIB',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (109,1,'客户',NULL,'Customer',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (110,1,'拓扑连接类型',NULL,'Topology Type',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (111,1,'级连方式',NULL,'Link Type',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (112,1,'基类信号',NULL,'Base Signal',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (113,1,'基类事件',NULL,'Base Event',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (114,1,'基类控制',NULL,'Base Control',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (115,1,'操作权限类别',NULL,'Operation Category',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (116,1,'控制类型',NULL,'Control Type',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (117,1,'落后电池发现方式',NULL,'BadBatteryIdentifyType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (118,1,'落后电池处理方式',NULL,'BadBatteryDealType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (119,1,'设备型号',NULL,'Equipment Model',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (120,1,'屏蔽类型',NULL,'TimeGroupType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (121,1,'工作模式',NULL,'Work Model',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (122,1,'事件状态',NULL,'EventState',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (123,1,'放电类型',NULL,'Discharge Category',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (124,1,'合同基站数',NULL,'Contract Station Count',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (150,1,'操作对象类型',NULL,'Operation Object Type',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (151,1,'配置检查问题类型',NULL,'Config Check Error Type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (152,1,'是否标准化',NULL,'It is standardized',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (153,1,'字典项类型',NULL,'Dictionary entry type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (156,1,'告警变化表通知类型',NULL,'AlarmChange entry type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (157,1,'门属性',NULL,'Door Property',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1001,1,'基站建筑类型',NULL,'BuildingType',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1002,1,'电表采集类别',NULL,'ScaleObject',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1003,1,'用电量异常类型',NULL,'PowerConsumptionEvenCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1004,1,'基站载频数',NULL,'Station BordNumber',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1005,1,'基站房屋面积',NULL,'Station Acreage',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1006,1,'基站主设备厂商',NULL,'Station Vendors',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1007,1,'基站空调品牌',NULL,'Station Air Condition Bands',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1008,1,'基站开关电源',NULL,'Station Rectifier Bands',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1009,1,'电表类型',NULL,'AmmeterCategory',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1010,1,'电源模块休眠功能',NULL,'Sleep Function Power Module',1,NULL);
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (1011,1,'报表参数控件类型','','ParameterType',1,'Report Parameter Control Type');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2003,1,'卡片种类',NULL,'Door Card Type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2004,1,'指纹读头多种开门方式',NULL,'FingerReaderMultiOpenDoorType',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2005,1,'指纹读头进出门标志',NULL,'FingerReader Door In Or Out Flag',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2006,1,'指纹采集串口服务器类型',NULL,'Serial Device Type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2007,1,'艾默生ISU串口服务器串口号',NULL,'Serial Device Type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2008,1,'艾默生IDU串口服务器串口号',NULL,'Serial Device Type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2009,1,'艾默生eStone串口服务器串口号',NULL,'Serial Device Type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2010,1,'纽贝尔806D4M3/D2M3串口服务器串口号',NULL,'Serial Device Type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2012,1,'中控门禁人员权限',NULL,'ZK door access role',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2013,1,'海康门禁门开方式',NULL,'Hik door open mode',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2021,1,'门禁用户属性',NULL,'Employee Feature',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2022,1,'资产类型',NULL,'AssetType',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (2023,1,'驱动模板类型',NULL,'Drive template Type',1,'批量工具驱动模板类型');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3001,1,'审计级别',NULL,'Audit level',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3002,1,'类别',NULL,'Type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3003,1,'IT设备模型类型',NULL,'Category',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3004,1,'卡号类型',NULL,'Card number type',1,'');
INSERT INTO tbl_dataentry (EntryId, EntryCategory, EntryName, EntryTitle, EntryAlias, Enable, Description) VALUES (3005,1,'组态模板组件类型',NULL,'Configure the template graphic type',1,'');

INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1,0,0,1,1,'按行政区分组','Group by Regions',1,1,0,'','StationGroupType1.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (3,0,0,1,2,'按局站等级分组','Group by Station Grade',1,0,0,'','StationGroupType2.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (5,0,0,1,3,'典型局站分组','Typical Station',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (7,0,0,2,1,'VIP基站','VIP Station',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (9,0,0,2,2,'普通基站','General Station',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (11,0,0,2,3,'重要基站','Important Station ',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (13,0,0,2,4,'边远基站','Boundary Station',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (15,0,0,3,1,'真实局站','Physical Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (17,0,0,3,2,'虚拟局站','Virtual Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (19,0,0,4,4,'四级中心(SSC)','Super Supervision Center',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (21,0,0,4,3,'三级中心(SC)','Supervision Center',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (23,0,0,4,2,'二级中心(SS)','Supervision Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (25,0,0,4,1,'二级中心区划(SG)','Supervision Group',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (27,0,0,5,1,'联网运行 ','Online',1,1,0,'','Ok.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (29,0,0,5,2,'测试状态 ','Testing',1,1,0,'','Testing.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (31,0,0,5,3,'工程状态 ','Building',1,1,0,'','Building.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (33,0,0,5,4,'屏蔽状态','Masking',1,1,0,'','Masking.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (35,0,0,6,1,'静态地图','Static Map',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (37,0,0,6,2,'矢量地图','Vector Map',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (39,0,0,7,11,'高压配电','High Voltage Distribution',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (41,0,0,7,12,'低压配电','Low Voltage Distribution',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (43,0,0,7,13,'油机发电机组','Diesel Generator',1,1,0,'','Generator.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (45,0,0,7,14,'高压进线柜','High-voltage Line Cabinet',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (47,0,0,7,15,'低压出线开关','Low-voltage circuit switcher',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (49,0,0,7,16,'高压出线柜','High-voltage out Cabinet',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (51,0,0,7,17,'高压操作电源','High-voltage power supply',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (53,0,0,7,18,'变压器','Transformer',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (55,0,0,7,21,'整流室交流配电屏','Rectifier Room AC Distribution Board',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (57,0,0,7,22,'整流器','Rectifier',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (59,0,0,7,23,'直流配电屏','DC Distribution Board',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (61,0,0,7,24,'蓄电池','Battery',1,1,0,'','Battery.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (63,0,0,7,25,'高压母联柜','High-voltage Connect Cabinet',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (65,0,0,7,26,'低压进线柜','Low-voltage Line Cabinet',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (67,0,0,7,27,'低压电容补偿柜','Low Voltage Capacitor Cabinet',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (69,0,0,7,28,'低压谐波补偿柜','Low Harmonic Compensation Cabinet',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (71,0,0,7,29,'低压母联柜','Low-voltage Connect Cabinet',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (73,0,0,7,31,'UPS设备','UPS',1,1,0,'','Ups.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (75,0,0,7,41,'中央空调(风冷)','Central Air-Condition (Wind Cooling)',1,1,0,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (77,0,0,7,42,'中央空调(水冷)','Central Air-Condition (Water Cooling)',1,1,0,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (79,0,0,7,43,'专用空调(风冷)','Special Air-Condition (Wind Cooling)',1,1,0,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (81,0,0,7,44,'专用空调(水冷)','Special Air-Condition (Water Cooling)',1,1,0,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (83,0,0,7,45,'分体空调','Split Air-Condition',1,1,0,'','AirCondition.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (85,0,0,7,51,'动力设备局房','Power Equipment House',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (87,0,0,7,52,'空调设备局房','Air-Condition Equipment House',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (89,0,0,7,53,'通信设备局房','Communication Equipment House',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (91,0,0,7,54,'OMC局房','OMC House',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (93,0,0,7,55,'极早期烟感设备','Early-warning Smoke Equipment',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (95,0,0,7,57,'MDF设备','MDF Equipment',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (97,0,0,7,61,'重要信号设备','Important Signal Equipment',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (99,0,0,7,62,'风能设备','Wind Equipment',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (101,0,0,7,71,'地线设备','Grounding Equipment',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (103,0,0,7,72,'防辟雷设备','Lightning Protection Equipment',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (105,0,0,7,81,'通信设备','Communication Equipment',1,1,0,'','Equipment.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (107,0,0,7,82,'门禁','Door Access Control',1,1,0,'','Door.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (109,0,0,7,83,'温度计','Thermometer',1,1,0,'','thermometer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (111,0,0,7,84,'ATM设备','ATM',1,1,0,'','ATMKey.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (113,0,0,7,85,'智能电表','Ammeter',1,1,0,'','ATMKey.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (115,0,0,7,86,'智能通风系统','Aeration System',1,1,0,'','ATMKey.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (117,0,0,7,87,'1-wire设备','1-wire Equipment',1,1,0,'','ATMKey.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (119,0,0,7,90,'图像设备','Image Equipment',1,1,0,'','webcam.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (121,0,0,7,97,'人脸读头','Face Reader',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (123,0,0,7,98,'指纹录入仪','Finger Reader',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (125,0,0,7,99,'自诊断设备','SelfDiagnostics',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (127,0,0,7,101,'240V组合电源','240V Power Supply',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (129,0,0,8,0,'未知','Unkown',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (131,0,0,8,1,'监控设备','Monitoring Equipment',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (133,0,0,8,2,'自诊断设备','Self-Diagnostic Equipment',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (135,0,0,8,3,'非监控设备','Non-Monitoring Equipment',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (137,0,0,9,1,'智能设备','Intelligent Equipment',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (139,0,0,9,2,'告警可屏蔽','Suppressable Equipment',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (141,0,0,9,3,'可控设备','Controlable Equipment',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (143,0,0,9,5,'重要设备','Important Equipment',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (145,0,0,9,4,'未用','Unused',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (147,0,0,9,6,'24伏蓄电池','24 Volt Battery',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (149,0,0,9,7,'48伏蓄电池','48 Volt Battery',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (151,0,0,9,8,'虚拟设备','Virtual Equipment',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (153,0,0,10,1,'未知','Unknown',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (155,0,0,10,2,'故障','Fault',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (157,0,0,10,3,'闲置','Free',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (159,0,0,10,4,'报废','Abandon',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (161,0,0,10,5,'正常','Normal',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (163,0,0,11,0,'未知','Unkown',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (165,0,0,11,1,'2V','2V',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (167,0,0,11,2,'6V','6V',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (169,0,0,11,3,'12V','12V',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (171,0,0,11,4,'24V','24V',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (173,0,0,11,5,'48V','48V',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (175,0,0,12,1,'浮充','Floating Charge',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (177,0,0,12,2,'放电','Discharge',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (179,0,0,12,3,'均充','Average Charge',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (181,0,0,13,1,'待命','Stand by',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (183,0,0,13,2,'派单','Send Order',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (185,0,0,13,3,'在途','On the way',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (187,0,0,13,4,'发电','Generating',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (189,0,0,14,0,'未知','Unknown',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (191,0,0,14,1,'维谛','Vertiv',1,0,0,'包括维谛、力博特、华为电气、安圣、艾默生、克劳瑞德等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (193,0,0,14,2,'中达','Delta',1,0,0,'包括台达、中达电通等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (195,0,0,14,3,'南都','Nandu',1,0,0,'浙江南都',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (197,0,0,14,4,'双登','ShuangDeng',1,0,0,'江苏双登',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (199,0,0,14,5,'光宇','GuangYu',1,0,0,'哈尔滨光宇',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (201,0,0,14,6,'雅达','Jata',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (203,0,0,14,7,'祥正','XiangZheng',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (205,0,0,14,8,'大金','Daikin',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (207,0,0,14,9,'三菱','Mitsubishi',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (209,0,0,14,10,'三洋','Sanyou',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (211,0,0,14,11,'海尔','Hair',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (213,0,0,14,12,'松下','Panasonic',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (215,0,0,14,13,'海尔三菱','Hair&Mitsubishi',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (217,0,0,14,14,'格力','Gree',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (219,0,0,14,15,'华为','Huawei',1,0,0,'华为技术',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (221,0,0,14,16,'中兴','ZTE',1,0,0,'中兴通讯',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (223,0,0,14,17,'罗兰','Rolan',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (225,0,0,14,18,'西恩迪(C&D)','C&D',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (227,0,0,14,19,'明光','Mingguang',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (229,0,0,14,20,'华达','Huada',1,0,0,'包括深圳华达、艾诺斯华达等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (231,0,0,14,21,'奇恩比(GNB)','GNB',1,0,0,'美国GNB',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (233,0,0,14,22,'胜利','Shengli',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (235,0,0,14,23,'佳力图','Galitu',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (237,0,0,14,24,'春兰','Chunnan',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (239,0,0,14,25,'卡特彼勒','CaterPillar',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (241,0,0,14,26,'德锋','Xianfeng',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (243,0,0,14,27,'中恒','ZhongHeng',1,0,0,'包括施威特克、侨兴、中恒等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (245,0,0,14,28,'海洛斯','Hiross',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (247,0,0,14,29,'立博特','Libert',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (249,0,0,14,30,'东芝','Toshiba',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (251,0,0,14,31,'美的','Midea',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (253,0,0,14,32,'科勒','Kohler',1,0,0,'含常州科勒',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (255,0,0,14,33,'星信','Xingxin',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (257,0,0,14,34,'新宏博','Xinhongbo',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (259,0,0,14,35,'江苏移动','JiangShu mobile',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (261,0,0,14,36,'先控','Sicon',1,0,0,'河北先控',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (263,0,0,14,37,'锐高','Ruigao',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (265,0,0,14,38,'爱立信','Ericsson',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (267,0,0,14,39,'APC','APC',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (269,0,0,14,40,'福发','Fufa',1,0,0,'福州柴油机',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (271,0,0,14,41,'PowerWare','PowerWare',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (273,0,0,14,42,'爱美威','Fanday',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (275,0,0,14,43,'登高','Genesis',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (277,0,0,14,44,'ASCO','ASCO',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (279,0,0,14,45,'普赛','PS',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (281,0,0,14,46,'霍克','Huoke',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (283,0,0,14,47,'珠江','Zhujiang',1,0,0,'珠江电源',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (285,0,0,14,48,'环宇','Huyu',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (287,0,0,14,49,'海信','Hisense',1,0,0,'山东海信',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (289,0,0,14,50,'双宇','Shuangyu',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (291,0,0,14,51,'艾思得','Astrid',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (293,0,0,14,52,'爱克赛','Eksi',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (295,0,0,14,53,'波利','Boli',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (297,0,0,14,54,'梅兰日兰','MerlinGerin',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (299,0,0,14,55,'中创瑞普','Zcpurui',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (301,0,0,14,56,'新西兰','NewZealand',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (303,0,0,14,57,'易达','Eltek',1,0,0,'东莞易达',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (305,0,0,14,58,'康明斯','Cummins',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (307,0,0,14,59,'朗讯','Lucent',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (309,0,0,14,60,'DIRIS','DIRIS',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (311,0,0,14,61,'阿特拉斯','Atlas',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (313,0,0,14,62,'京丰','Jingfeng',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (315,0,0,14,63,'泰兴','Taixing',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (317,0,0,14,64,'汤浅','Yuasa',1,0,0,'包括广东汤浅、日本汤浅等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (319,0,0,14,65,'华菱','Valin',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (321,0,0,14,66,'劲达','Kingtec',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (323,0,0,14,67,'兴安','Xingan',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (325,0,0,14,68,'华宝','Huabao',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (327,0,0,14,69,'银波达','Yinboda',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (329,0,0,14,70,'卧龙','Wolong',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (331,0,0,14,71,'灯塔','Dengta',1,0,0,'浙江卧龙灯塔',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (333,0,0,14,72,'TNB','TNB',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (335,0,0,14,73,'博尔','Boer',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (337,0,0,14,74,'艾维达','Avda',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (339,0,0,14,75,'理士','Leoch',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (341,0,0,14,76,'通力环','Tolihi',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (343,0,0,14,77,'威尔逊','Wilson',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (345,0,0,14,78,'动力源','Dynamic',1,0,0,'北京动力源',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (347,0,0,14,79,'科龙','Kelon',1,0,0,'广东科龙',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (349,0,0,14,80,'银泰','Intesolar',1,0,0,'武汉银泰',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (351,0,0,14,81,'陶都','Taodu',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (353,0,0,14,82,'银河','Yinghe',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (355,0,0,14,83,'阿尔西','Airsys',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (357,0,0,14,84,'伟博','Wabertec',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (359,0,0,14,85,'昊诚','Haocheng',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (361,0,0,14,86,'能威','Newave',1,0,0,'瑞士能威',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (363,0,0,14,87,'华富','Huafu',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (365,0,0,14,88,'JDC','JDC',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (367,0,0,14,89,'雷乐士','Riello',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (369,0,0,14,90,'HDR','HDR',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (371,0,0,14,91,'GE','GE',1,0,0,'美国通用',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (373,0,0,14,92,'AGM','AGM',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (375,0,0,14,93,'安奈特','Allied Telesis',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (377,0,0,14,94,'优力','Unitech',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (379,0,0,14,95,'科创','Kechuang',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (381,0,0,14,96,'创科','TTI',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (383,0,0,14,97,'京丰明光','JFMG',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (385,0,0,14,98,'星恒','Xingheng',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (387,0,0,14,99,'硅能','Ledteen',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (389,0,0,14,100,'阳光','Sungrow',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (391,0,0,14,101,'凯旋','Kashinys',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (393,0,0,14,102,'特灵','Trane',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (395,0,0,14,103,'江苏通信','JSCA',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (397,0,0,14,104,'三菱电机','Mitsubishielectric',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (399,0,0,14,105,'尚灵','Sunlines',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (401,0,0,14,106,'科泰','Cooltech',1,0,0,'上海科泰',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (403,0,0,14,107,'锦天乐','Jintianle',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (405,0,0,14,108,'金威远','Jinweiyuan',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (407,0,0,14,109,'亚奥','Asia Olympic',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (409,0,0,14,110,'永为','Yongwei',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (411,0,0,14,111,'搏力','Boli',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (413,0,0,14,112,'易事特','East',1,0,0,'广东易事特',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (415,0,0,14,113,'依米康','Sunrise',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (417,0,0,14,114,'派诺','Polot',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (419,0,0,14,115,'恒通','Hengtong',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (421,0,0,14,116,'万里','Wanli',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (423,0,0,14,117,'威泰迅','Wontex',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (425,0,0,14,118,'海通','Haitong',1,0,0,'江苏海通',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (427,0,0,14,119,'盾安','Dunan',1,0,0,'浙江盾安',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (429,0,0,14,120,'新海宜','New Seaunion',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (431,0,0,14,121,'普兴','Puxing',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (433,0,0,14,122,'山特','Santak',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (435,0,0,14,123,'辛普森','Xinpusheng',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (437,0,0,14,124,'中高','Zhonggao',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (439,0,0,14,125,'科士达','Kstar',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (441,0,0,14,126,'兆宇','Zhaoyu',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (443,0,0,14,127,'派能','Pylontech',1,0,0,'中兴派能',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (445,0,0,14,128,'天能','Tianneng',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (447,0,0,14,129,'宏通','Hongtong',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (449,0,0,14,130,'科华','Kehua',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (451,0,0,14,131,'圣阳','Sacredsun',1,0,0,'山东圣阳',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (453,0,0,14,132,'力创','Lichuang',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (455,0,0,14,133,'武汉普天','Putian',1,0,0,'包括洲际电源、电池等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (457,0,0,14,134,'金威源','Goldpower',1,0,0,'深圳金威源',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (459,0,0,14,135,'通力盛达','Tonlier',1,0,0,'北京通力盛达',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (461,0,0,14,136,'安耐特','Enatel',1,0,0,'深圳安耐特',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (463,0,0,14,137,'新电元','Shindengenelctric',1,0,0,'上海新电元通信设备有限公司',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (465,0,0,14,138,'长光','Yotc',1,0,0,'武汉长光',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (467,0,0,14,139,'火炬','Torch',1,0,0,'淄博火炬',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (469,0,0,14,140,'海霸','Haiba',1,0,0,'山东海霸',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (471,0,0,14,141,'大力神','Technologies',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (473,0,0,14,142,'华日','Huari',1,0,0,'山东华日',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (475,0,0,14,143,'丰日','Fengri',1,0,0,'湖南丰日',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (477,0,0,14,144,'文隆','Wenlong',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (479,0,0,14,145,'北京汉铭','Aceway',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (481,0,0,14,146,'联动天翼','Linkdata',1,0,0,'北京联动天翼',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (483,0,0,14,147,'比亚迪','BYD',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (485,0,0,14,148,'汇龙','Huilong',1,0,0,'云南汇龙',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (487,0,0,14,149,'施耐德','Schneider',1,0,0,'包括施耐德、梅兰日兰、APC等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (489,0,0,14,150,'索科曼','Socemen',1,0,0,'包括索科曼、溯高美等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (491,0,0,14,151,'史图斯','STULZ',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (493,0,0,14,152,'西门子','Siemens',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (495,0,0,14,153,'爱维达','Evada',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (497,0,0,14,154,'伊顿','Eaton',1,0,0,'包括Powerware、山特、伊顿等',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (499,0,0,14,155,'志成冠军','zhicheng-champion',1,0,0,'广东志成冠军',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (501,0,0,14,156,'斯泰科','CTDG',1,0,0,'北京斯泰科',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (503,0,0,14,157,'志高','Chigo',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (505,0,0,14,158,'吉荣','Jirong',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (507,0,0,14,159,'华凌','Hualing',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (509,0,0,14,160,'约顿','Joton',1,0,0,'上海约顿',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (511,0,0,14,161,'铨高','Renovo',1,0,0,'珠海铨高',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (513,0,0,14,162,'融和','Rohe',1,0,0,'北京融和',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (515,0,0,14,163,'艾苏威尔','Isuwel',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (517,0,0,14,164,'开利','Carrier',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (519,0,0,14,165,'十字军','SZJ',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (521,0,0,14,166,'道依兹','DEUTZ',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (523,0,0,14,167,'劳斯莱斯','Rolls-Royce',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (525,0,0,14,168,'威尔信','Weierxin',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (527,0,0,14,169,'泰豪','Tellhow',1,0,0,'泰豪科技',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (529,0,0,14,170,'威能','Saonon',1,0,0,'番禺威能',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (531,0,0,14,171,'济柴','Jicai',1,0,0,'济南柴油机',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (533,0,0,14,172,'怡昌','Yichang',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (535,0,0,14,173,'开普','Gmeey',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (537,0,0,14,174,'南柴','Nancai',1,0,0,'南昌柴油机',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (539,0,0,14,175,'BEST','BEST',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (541,0,0,14,176,'IMV','IMV',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (543,0,0,14,177,'丹科','Dako',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (545,0,0,14,178,'上柴','Shangcai',1,0,0,'上海柴油机',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (547,0,0,14,179,'正和','Zhenghe',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (549,0,0,14,180,'海康','HIK',1,0,0,'HIK',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (551,0,0,14,181,'纽贝尔','NewaBel',1,0,0,'NewaBel',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (553,0,0,15,0,'未知','Unknown',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (555,0,0,16,0,'未知','Unknown',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (557,0,0,17,1,'模拟信号','Analog Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (559,0,0,17,2,'开关信号','Switch Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (561,0,0,17,3,'图像信号','Image Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (563,0,0,18,1,'采集信号','Sample Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (565,0,0,18,2,'虚拟信号','Virtual Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (567,0,0,18,3,'常量信号','Constant Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (569,0,0,19,0,'统计最大值','Statistic Max',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (571,0,0,19,1,'统计最小值','Statistic Min',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (573,0,0,19,2,'统计平均值','Statistic Average',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (575,0,0,19,3,'由事件产生','Triggered by Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (577,0,0,19,4,'超过阀值产生','Triggered By Threshold',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (579,0,0,19,5,'周期存储','Save Periodically',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (581,0,0,19,6,'抄表信号','Record Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (583,0,0,19,7,'定点采集','Sampled by Timing',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (585,0,0,20,1,'V','V',1,1,0,'电位、电压、电动势',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (587,0,0,20,2,'kV','kV',1,1,0,'高压',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (589,0,0,20,3,'A','A',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (591,0,0,20,4,'Hz','Hz',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (593,0,0,20,5,'PF','PF',1,1,0,'功率因数PF',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (595,0,0,20,6,'W','W',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (597,0,0,20,7,'kW','kW',1,1,0,'有功功率、辐射通量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (599,0,0,20,8,'MW','MW',1,1,0,'光伏',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (601,0,0,20,9,'kWh','kWh',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (603,0,0,20,10,'Var','Var',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (605,0,0,20,11,'kVar','kVar',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (607,0,0,20,12,'Varh','Varh',1,1,0,'无功电量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (609,0,0,20,13,'kVarh','kVarh',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (611,0,0,20,14,'VA','VA',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (613,0,0,20,15,'kVA','kVA',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (615,0,0,20,16,'℃','℃',1,1,0,'如果资料为华氏度，需协议转换为摄氏度',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (617,0,0,20,17,'%RH','%RH',1,1,0,'相对湿度(Relative Humidity)',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (619,0,0,20,18,'r/min','r/min',1,1,0,'风扇转速',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (621,0,0,20,19,'kPa','kPa',1,1,0,'压缩机X压力、油压、压强、应力',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (623,0,0,20,20,'MPa','MPa',1,1,0,'冷凝器进水压力，由协议统一，同类设备、信号使用一种',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (625,0,0,20,21,'N','N',1,1,0,'力、重力',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (627,0,0,20,22,'L','L',1,1,0,'燃油剩余容量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (629,0,0,20,23,'Ah','Ah',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (631,0,0,20,24,'%','%',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (633,0,0,20,25,'%','%',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (635,0,0,20,26,'Y','Y',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (637,0,0,20,27,'M','M',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (639,0,0,20,28,'D','D',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (641,0,0,20,29,'h','h',1,1,0,'累计运行时间',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (643,0,0,20,30,'min','min',1,1,0,'电池后备时间',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (645,0,0,20,31,'sec','sec',1,1,0,'压缩机重开保护时间',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (647,0,0,20,32,'kΩ','kΩ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (649,0,0,20,33,'mΩ','mΩ',1,1,0,'电池内阻',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (651,0,0,20,34,'μF','μF',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (653,0,0,20,35,'H','H',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (655,0,0,20,36,'m','m',1,1,0,'长度、高度',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (657,0,0,20,37,'kg','kg',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (659,0,0,20,38,'lx','lx',1,1,0,'衡量单位面积的光通量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (661,0,0,20,39,'lm','lm',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (663,0,0,20,40,'pcs','pcs',1,1,0,'个',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (665,0,0,20,41,'W/m2','W/m2',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (667,0,0,20,42,'m/s','m/s',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (669,0,0,20,43,'Nm','Nm',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (671,0,0,20,44,'m3','m3',1,1,0,'容积、风量',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (673,0,0,20,45,'S','S',1,1,0,'电池电导',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (675,0,0,20,46,'MWh','MWh',1,1,0,'累计电能',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (677,0,0,21,1,'状态信号','State Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (679,0,0,21,2,'抄表信号','Record Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (681,0,0,21,3,'门禁信号','Door Access Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (683,0,0,21,4,'电池后备时间','Battery Stand by Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (685,0,0,21,5,'电池总电压信号','Battery Total Voltage Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (687,0,0,21,6,'模块输出电压','Module Output Voltage',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (689,0,0,21,7,'模块输出电流','Module Input Current',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (691,0,0,21,8,'电表读表信号','Ammeter Record Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (693,0,0,21,9,'CPU使用率','CPU Usage Rate',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (695,0,0,21,10,'内存使用率','Memory Usage Rage',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (697,0,0,21,11,'通讯状态','Communication State',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (699,0,0,21,12,'主机接入','Host Connected',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (701,0,0,21,13,'门开关状态信号','Door Access Switch State Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (703,0,0,21,14,'市电状态信号','Power State Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (705,0,0,21,15,'图像明亮度信号','Image Brightness Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (707,0,0,21,16,'图像对比度信号','Image Contrast Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (709,0,0,21,17,'图像流信号','Image Stream Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (711,0,0,21,18,'图像帧率信号','Image FPS Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (713,0,0,21,19,'落后电池信号','Bad Cell Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (715,0,0,21,20,'电池电流信号','Battery Current Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (717,0,0,21,22,'电池油机工作状态信号','Battery Diesel Work State Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (719,0,0,21,23,'告警可屏蔽','Suppressable Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (721,0,0,21,24,'告警屏蔽','Event Suppression',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (723,0,0,21,25,'重要信号','Important Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (725,0,0,21,27,'可视信号','Visible Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (727,0,0,21,28,'诊断信号','Diagnostic Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (729,0,0,21,30,'单体电池电压信号','Cell Voltage Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (731,0,0,21,31,'电池组剩余容量','Battery Pack Remaining Capacity',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (733,0,0,21,32,'来电提示','Incoming Call Alert',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (735,0,0,21,33,'数据库连接信号','Database Connection Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (737,0,0,21,34,'用电量','Energy',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (739,0,0,21,35,'放电测试状态','Discharge Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (741,0,0,21,36,'设备安装状态','Equipment Fixed Status',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (743,0,0,21,37,'温度信号','Temperature Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (745,0,0,21,38,'DI_DOOR','DI_DOOR',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (747,0,0,21,39,'DI_SMOKE','DI_SMOKE',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (749,0,0,21,40,'DI_LEAK','DI_LEAK',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (751,0,0,21,41,'DI_MOTION','DI_MOTION',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (753,0,0,21,42,'DI_VIBRA','DI_VIBRA',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (755,0,0,21,43,'DI_OTHER','DI_OTHER',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (757,0,0,21,44,'DO_SOUND','DO_SOUND',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (759,0,0,21,45,'DO_LIGHT','DO_LIGHT',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (761,0,0,21,46,'DO_FAN','DO_FAN',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (763,0,0,21,47,'整流器有电状态','Power State Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (765,0,0,21,48,'湿度','Humidity',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (767,0,0,21,49,'DI_MicroWave','DI_MicroWave',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (769,0,0,21,50,'功耗信号','Power Consumption',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (771,0,0,21,51,'负载电流','LoadCurrent',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (773,0,0,21,52,'三相输入Uab','Three-phase input Uab',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (775,0,0,21,53,'三相输入Ubc','Three-phase inputUbc',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (777,0,0,21,54,'三相输入Uac','Three-phase input Uac',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (779,0,0,21,55,'三相输出Uab','Three-phase output Uab',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (781,0,0,21,56,'三相输出Ubc','Three-phase output Ubc',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (783,0,0,21,57,'三相输出Uac','Three-phase output Uac',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (785,0,0,21,58,'输出线电流Ia','Ouput Line Current Ia',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (787,0,0,21,59,'输出线电流Ib','Ouput Line Current Ib',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (789,0,0,21,60,'输出线电流Ic','Ouput Line Current  Ic',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (791,0,0,21,61,'开关电源系统电压','Rectifier System Voltage',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (793,0,0,21,62,'节能状态','Energy State',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (795,0,0,21,63,'整点抄表信号','Dot Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (797,0,0,21,64,'相电流Ia','Phase Current Ia',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (799,0,0,21,65,'相电流Ib','Phase Current Ib',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (801,0,0,21,66,'相电流Ic','Phase Current Ic',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (803,0,0,21,67,'A相保障负荷电流','A-Phase Protection Of current',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (805,0,0,21,68,'B相保障负荷电流','B-Phase Protection Of current',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (807,0,0,21,69,'C相保障负荷电流','C-Phase Protection Of current',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (809,0,0,21,70,'开关位置','Switch position',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (811,0,0,21,71,'有功功率','Active Power',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (813,0,0,21,72,'电池保障时长','Battery  Protected Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (815,0,0,21,73,'落后电池总节数','Bad Cell Total Count',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (817,0,0,21,74,'剩余容量','Remaining Capacity',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (819,0,0,21,75,'已经放电时间','Discharge Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (821,0,0,21,76,'休眠状态','Dormancy Status',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (823,0,0,21,77,'虚拟电表用电量','Virtual Ammeter  Consumption',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (825,0,0,21,78,'停止放电原因','Stop  Discharge Reason',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (827,0,0,21,79,'上次电池测试容量','Last Battery Test Compacity',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (829,0,0,22,1,'模拟量','Analog',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (831,0,0,22,2,'数字量','State',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (833,0,0,23,0,'四级告警','Level 4',1,1,0,'','EventSeverity0.png','EventSeverity0.mp3','#77BDF8','4',NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (835,0,0,23,1,'三级告警','Level 3',1,1,0,'','EventSeverity1.png','EventSeverity1.mp3','#EDD951','3',NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (837,0,0,23,2,'二级告警','Level 2',1,1,0,'','EventSeverity2.png','EventSeverity2.mp3','#F39924','2',NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (839,0,0,23,3,'一级告警','Level 1',1,1,0,'','EventSeverity3.png','EventSeverity3.mp3','#ff2626','1',NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (841,0,0,24,1,'系统事件','System Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (843,0,0,24,2,'设备事件','Equipment Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (845,0,0,24,3,'状态事件','State Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (847,0,0,24,4,'刷卡事件','Swipe Card Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (849,0,0,24,5,'烟感事件','Fire Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (851,0,0,24,6,'自诊断事件','Self-Diagnostic Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (853,0,0,24,7,'通讯状态事件','Communication State Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (855,0,0,24,8,'主机接入事件','Host Connected Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (857,0,0,24,9,'撤防','Disarming',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (859,0,0,24,10,'停电','Power Cut',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (861,0,0,24,11,'一次下电事件','Main Loop Power Cut',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (863,0,0,24,12,'监控中断','Monitoring Interruption',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (865,0,0,24,13,'门事件','Door Access Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (867,0,0,24,14,'空调温度预警','Air-Condition Temperature Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (869,0,0,24,15,'空调压缩机预警','Air-Condition Compressor Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (871,0,0,24,16,'开关电源模块电流预警','Switch Power Module Current Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (873,0,0,24,17,'开关电源负载电流预警','Switch Power Load Current Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (875,0,0,24,18,'单体电池放电预警','Cell Discharge Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (877,0,0,24,19,'单体电池充电预警','Cell Charge Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (879,0,0,24,20,'电缆负载电流预警','Power Cable Load Current Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (881,0,0,24,21,'UPS单机容量预警','UPS Stand-alone Capacity Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (883,0,0,24,22,'UPS单机电流预警','UPS Stand-alone Current Early-warning',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (885,0,0,24,23,'局站撤防','Station Disarming',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (887,0,0,24,24,'端局断站事件','Station Interruption Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (889,0,0,24,25,'License无效事件','License Invalid Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (891,0,0,24,26,'大规模停电事件','Large Scale Power Cut Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (893,0,0,24,27,'数据库连接中断事件','Database Connection Interruption Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (895,0,0,24,28,'设备有效状态事件 ','Equipment Valid State Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (897,0,0,24,29,'单体落后电池事件','Bad Cell Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (899,0,0,24,30,'采集器通讯状态事件','Sampler Communication State Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (901,0,0,24,31,'直流电源告警','DC Power Event',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (903,0,0,24,32,'防盗告警','Anti-theft Event',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (905,0,0,24,33,'总电压告警','Total voltage Event',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (907,0,0,24,34,'温度告警','Temperature   Event',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (909,0,0,24,35,'门碰告警','Door Access Event',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (911,0,0,24,36,'红外告警','Infrared Event',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (913,0,0,24,37,'水浸告警','Flood Event',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (915,0,0,24,38,'非Vertiv采集器接入事件','Non-Vertiv Sampler Connected Event',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (917,0,0,24,39,'接入监控单元数超过License限制','Access Monitoring Unit Over License Limit',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (919,0,0,24,40,'设备未安装事件','Equipment Non-Fixed Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (921,0,0,24,41,'设备安装事件','Equipment Fixed Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (923,0,0,24,42,'油机供电','Machine Generating',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (925,0,0,24,43,'放电结束事件','DisCharge Finish Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (927,0,0,24,44,'湿度告警','Humidity  Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (929,0,0,24,45,'空调设备事件','AirCondition Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (931,0,0,24,46,'单体电压事件','Cell Voltage Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (933,0,0,24,47,'电池温度事件','Battery Temperature Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (935,0,0,24,48,'电表电压事件','Ammeter Voltage Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (937,0,0,24,49,'市电开关事件','Power Status Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (939,0,0,24,50,'开关电源事件','Rectifier Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (941,0,0,24,51,'非法开门事件','Illegal Door Open Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (943,0,0,24,52,'超长门开事件','Door Opening long Time Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (945,0,0,24,53,'专用空调事件','Special Air-Condition Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (947,0,0,24,54,'自动重合闸设备事件','Autoreclose circuit breaker',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (949,0,0,24,55,'油机事件','Generator Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (951,0,0,24,56,'UPS事件','UPS Event ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (953,0,0,24,57,'直流电源输出电压告警','DC Voltage Output  Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (955,0,0,24,58,'直流电源交流输出事件','AC Voltage Output  Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (957,0,0,24,59,'开关电源休眠','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (959,0,0,24,60,'模块休眠','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (961,0,0,24,61,'电池组单体状态','Battery Cell Voltage Status',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (963,0,0,24,62,'数据库空间不足告警','Database FreeSize not Enough',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (965,0,0,24,63,'设备通讯状态事件','Equipment Communication State Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (967,0,0,25,1,'条件事件','Conditional Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (969,0,0,25,2,'非条件事件','Non-conditional Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (971,0,0,26,1,'脉冲事件','Pulse Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (973,0,0,26,2,'间隔事件','Interval Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (975,0,0,26,3,'持续事件','Continual Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (977,0,0,27,0,'事件开始','Event Start',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (979,0,0,27,1,'事件结束','Event End',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (981,0,0,27,4,'事件升级','Event Upgrade',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (983,0,0,28,1,'一般','General',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (985,0,0,28,2,'重要','Important',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (987,0,0,28,3,'紧急','Critical',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (989,0,0,29,1,'待命','Stand by',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (991,0,0,29,2,'正执行中','Executing',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (993,0,0,29,3,'异常','Exception',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (995,0,0,29,4,'完成','Success',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (997,0,0,30,1,'控制成功','Control Success',1,1,0,'','commandstatus_execute.gif',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (999,0,0,30,2,'控制失败','Control Failure',1,1,0,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1001,0,0,30,3,'处理超时','Process Timeout',1,1,0,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1003,0,0,30,4,'未返回','Return Failure',1,1,0,'','commandstatus_execute.gif',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1005,0,0,30,5,'控制单元地址错误','Control Unit Address Error',1,1,0,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1007,0,0,30,6,'参数错误','Parameter Error',1,1,0,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1009,0,0,30,7,'在控制队列中超时','Timeout in the Control Queue',1,1,0,'','commandstatus_fail.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1011,0,0,30,8,'录像结束','Record Over',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1013,0,0,31,0,'未设定','Unknown',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1015,0,0,31,1,'普通控制','General Control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1017,0,0,31,2,'开关门控制','Switch Door Access',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1019,0,0,31,3,'图像上移','Up',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1021,0,0,31,4,'图像下移','Down',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1023,0,0,31,5,'图像左移','Left',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1025,0,0,31,6,'图像右移','Right',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1027,0,0,31,7,'图像前移','Forward',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1029,0,0,31,8,'图像后移','Backward',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1031,0,0,31,9,'图像亮度控制','Brightness',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1033,0,0,31,10,'图像对比度控制','Contrast',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1035,0,0,31,11,'图像帧率控制','Fps',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1037,0,0,31,12,'增加门禁卡','Add Door Access Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1039,0,0,31,13,'删除门禁卡','Delete Door Access Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1041,0,0,31,14,'修改门禁卡设置','Modify Door Access Card Setting',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1043,0,0,31,15,'设置星期准进时间段 ','Set Week Ingress Time Period',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1045,0,0,31,16,'修改验证控制密码','Modify Control Privilege Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1047,0,0,31,17,'删除所有门禁卡','Delete All Door Access Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1049,0,0,31,18,'布防红外','Deploy Infrared',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1051,0,0,31,19,'撤防红外','Disarm Infrared',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1053,0,0,31,20,'开门超时时间','Door Open Timeout',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1055,0,0,31,21,'刷卡进门密码工作方式','Swipe Card Entry Password Work Mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1057,0,0,31,22,'设置时间','Set Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1059,0,0,31,23,'非法开门告警结束确定命令','Illegal Door Open Event End Confirm Command',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1061,0,0,31,24,'放电测试','Discharge Test ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1063,0,0,31,25,'停止放电测试','Stop  Discharge Test ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1065,0,0,31,26,'告警录像控制命令','Event Record Control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1067,0,0,31,27,'放电终止电压设置','Set Discharge Terminate Voltage',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1069,0,0,31,28,'放电终止时长设置','Set Discharge Terminate Period',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1071,0,0,32,1,'遥调','Remote Regulating',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1073,0,0,32,2,'遥控','Remote Control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1075,0,0,33,1,'联网模式','Network Mode',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1077,0,0,33,2,'单机模式','Stand-alone Mode',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1079,0,0,33,3,'测试模式','Test Mode',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1081,0,0,34,1,'RMU下MU','MU of RMU',1,1,0,'','mu.png','4',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1083,0,0,34,2,'IDU','IDU',1,1,0,'','idu.png','11',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1085,0,0,34,4,'IPLU','IPLU',1,1,0,'','iplu.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1087,0,0,34,5,'IMU','IMU',1,1,0,'','IMU.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1089,0,0,34,6,'eStone','eStone',1,1,0,'','eStone.png','14',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1091,0,0,34,7,'IDU-X','IDU-X',1,1,0,'','IDUX.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1093,0,0,34,8,'ISU','ISU',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1095,0,0,34,9,'FSU','FSU',1,1,0,'',NULL,'13',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1097,0,0,34,10,'BInterface','BInterface',1,1,0,'',NULL,'15',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1099,0,0,34,11,'Catcher','Catcher',1,1,0,'',NULL,'16',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1101,0,0,34,12,'GFSU','GFSU',1,1,0,'',NULL,'1',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1103,0,0,34,14,'ISUV2','ISUV2',1,1,0,'',NULL,'3',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1105,0,0,34,16,'WorkStation','WorkStation',1,1,0,'',NULL,'5',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1107,0,0,34,17,'ECG','ECG',1,1,0,'',NULL,'6',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1109,0,0,35,1,'真实MU','Pysical MU',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1111,0,0,35,2,'虚拟MU','Virtual MU',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1113,0,0,36,1,'自启动','Start Automatically',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1115,0,0,36,2,'停机','Halt',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1117,0,0,36,3,'故障','Fault',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1119,0,0,36,4,'远程控制','Remote Control',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1121,0,0,37,0,'自诊断采集器','Self-Diagnostic Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1123,0,0,37,1,'AMS采集器','AMS Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1125,0,0,37,2,'OCE采集器','OCE Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1127,0,0,37,3,'IDA-AI','IDA-AI',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1129,0,0,37,4,'IDA-DI','IDA-DI',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1131,0,0,37,5,'IDA-DO','IDA-DO',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1133,0,0,37,6,'IDA-BAT','IDA-BAT',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1135,0,0,37,7,'IDA-I0','IDA-I0',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1137,0,0,37,8,'SDA-OCE','SDA-OCE',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1139,0,0,37,9,'SDA-IO','SDA-IO',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1141,0,0,37,10,'IDA-SIO','IDA-SIO',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1143,0,0,37,13,'IDU_IO','IDU_IO',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1145,0,0,37,14,'IDU_BAT','IDU_BAT',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1147,0,0,37,11,'IDU_DOOR(IDU一体化门禁)','IDU_DOOR(IDU Integrative Door Access)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1149,0,0,37,12,'DOOR(普通门禁)','DOOR(General Door Access)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1151,0,0,37,15,'简单逻辑控制采集器','Simple Logic Control Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1153,0,0,37,16,'图像采集器','Image Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1155,0,0,37,17,'IOLanplus','IO-Lan plus',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1157,0,0,37,18,'普通采集器','General Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1159,0,0,37,19,'大规模停电采集器','Large Scale Power Cut Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1161,0,0,37,20,'USB Key丢失采集器','USB Key Unfound Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1163,0,0,37,21,'端局断站生成采集器','Station Interruption Generation Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1165,0,0,37,22,'IDU自诊断采集器','IDU Self-diagnostic Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1167,0,0,37,23,'IPLU自诊断采集器','IPLU Self-diagnostic Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1169,0,0,37,24,'eStone自诊断采集器','eStone Self-diagnostic Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1171,0,0,37,25,'EEM-Sampler ','EEM-Sampler ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1173,0,0,37,26,'EEM-RI-Sampler','EEM-RI-Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1175,0,0,37,27,'EEM-Diagnose-Sampler','EEM-Diagnose-Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1177,0,0,37,28,'IMU采集器','IMU Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1179,0,0,37,29,'微站采集器','Micro Station Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1181,0,0,37,30,'ISU自诊断采集器','ISU Self-diagnostic Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1183,0,0,38,0,'自诊断(RMS)','Self-diagnostic(RMS)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1185,0,0,38,1,'采集单元(AMS)','Sampler Unit(AMS)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1187,0,0,38,2,'采集单元(OCE)','Sampler Unit(OCE)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1189,0,0,38,3,'采集单元(普通)','Sampler Unit(General)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1191,0,0,39,1,'标准串口','Standard Serial Port',1,1,0,'','1',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1193,0,0,39,2,'SNU口','SNU Port',1,1,0,'','52',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1195,0,0,39,3,'SNMP口','SNMP Port',1,1,0,'','3',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1197,0,0,39,4,'PSTN巡检口','PSTN Patrol Port',1,1,0,'','53',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1199,0,0,39,5,'虚拟端口','Virtual Port',1,1,0,'','2',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1201,0,0,39,6,'终端服务器口','Terminal Server Port',1,1,0,'','20',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1203,0,0,39,7,'PSTN告警回叫口','PSTN Event Callback Port',1,1,0,'','54',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1205,0,0,39,8,'PSTN手动维护口','PSTN Maintenance Port',1,1,0,'','55',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1207,0,0,39,9,'OMC口','OMC Port',1,1,0,'','56',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1209,0,0,39,10,'系统接入口','System Access Port',1,1,0,'','57',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1211,0,0,39,11,'ISDN拨号备份口','ISDN Dial-up Standby Port',1,1,0,'','58',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1213,0,0,39,12,'专线备份口','Special Line Standby Port',1,1,0,'','59',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1215,0,0,39,13,'IDU-IP口','IDU-IP port',1,1,0,'','60',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1217,0,0,39,14,'IDU-SMS口','IDU-SMS Port',1,1,0,'','61',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1219,0,0,39,15,'IDU-IP-SMS口','IDU-IP-SMS Port',1,1,0,'','62',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1221,0,0,39,16,'IDU-Serial口','IDU-Serial Port',1,1,0,'','63',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1223,0,0,39,17,'DTU口','DTU Port',1,1,0,'','64',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1225,0,0,39,18,'Host口','Host Port',1,1,0,'','65',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1227,0,0,39,19,'简单逻辑控制口','Simple Logic Control Port',1,1,0,'','5',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1229,0,0,39,20,'IP巡检口     ','IP Patrol Port',1,1,0,'','66',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1231,0,0,39,21,'IP告警回叫口 ','IP Event Callback Port',1,1,0,'','67',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1233,0,0,39,22,'IP手动维护口 ','IP Maintenance Port',1,1,0,'','68',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1235,0,0,39,23,'GPRS巡检口     ','GPRS Patrol Port',1,1,0,'','69',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1237,0,0,39,24,'GPRS告警回叫口','GPRS Event Callback Port',1,1,0,'','70',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1239,0,0,39,25,'GPRS手动维护口 ','GPRS Maintenance Port',1,1,0,'','71',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1241,0,0,39,26,'GSM巡检口     ','GSM Patrol Port',1,1,0,'','72',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1243,0,0,39,27,'GSM告警回叫口 ','GSM Event Callback Port',1,1,0,'','73',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1245,0,0,39,28,'GSM手动维护口','GSM Maintenance Port',1,1,0,'','75',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1247,0,0,39,29,'I2C端口','I2C Port',1,1,0,'','76',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1249,0,0,39,30,'MDU端口','MDU Port',1,1,0,'','7',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1251,0,0,39,31,'移动B接口门禁透传端口','CMCC BInterface Access Control Port',1,1,0,'','6',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1253,0,0,40,1,'邮件（Notes）','Email(Notes)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1255,0,0,40,2,'短信（GSM Modem）','ShortMessage(GSM Modem)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1257,0,0,40,3,'语音','Text to Speech',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1259,0,0,40,4,'打印机','Printer',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1261,0,0,40,5,'日志','Log',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1263,0,0,40,6,'传真机','Fax',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1265,0,0,40,7,'屏幕','Screen',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1267,0,0,40,8,'短信（infoX-MAS-SOAP）','ShortMessage(infoX-MAS-SOAP)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1269,0,0,40,9,'短信（infoX-MAS-DLL）','ShortMessage(infoX-MAS-DLL)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1271,0,0,41,1,'普通员工','General Employee',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1273,0,0,41,2,'系统操作员','System Operator',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1275,0,0,41,3,'系统维护员','System Maintenance Operator',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1277,0,0,41,4,'系统管理员','System Administrator',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1279,0,0,42,1,'工程师','Engineer',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1281,0,0,42,2,'业务员','Business Person',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1283,0,0,42,3,'操作员','Operator',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1285,0,0,43,1,'管理','Management',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1287,0,0,43,2,'操作','Operation',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1289,0,0,43,3,'业务','Business',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1291,0,0,44,1,'VIP片区','VIP Area',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1293,0,0,44,2,'普通片区','General Area',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1295,0,0,44,3,'边远片区','Boundary Area',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1297,0,0,44,4,'SVIP片区','SVIP Area',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1299,0,0,45,1,'配置日志','Configuration Log',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1301,0,0,45,2,'操作日志','Operation Log',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1303,0,0,45,3,'业务日志','Business Log',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1305,0,0,46,1,'使用','Using',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1307,0,0,46,2,'挂失','Lost',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1309,0,0,46,3,'作废','Cancel',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1311,0,0,47,1,'管理卡','Management Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1313,0,0,47,2,'维护卡','Maintenance Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1315,0,0,47,3,'操作卡','Operation Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1317,0,0,47,4,'普通卡','General Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1319,0,0,48,1,'进出不要密码','Enter or Exit without Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1321,0,0,48,2,'进出都要密码','Enter or Exit with Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1323,0,0,48,3,'进要密码','Enter with a Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1325,0,0,48,4,'出要密码','Exit  with a Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1327,0,0,49,0,'按钮出门','Exit by Pressing Button',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1329,0,0,49,1,'合法卡','Valid Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1331,0,0,49,2,'过期卡','Unauthorized',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1333,0,0,49,3,'非法时区卡','Invalid Time Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1335,0,0,49,4,'密码错误卡','Password Wrong Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1337,0,0,49,5,'非法门区卡','Invalid Door Region Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1339,0,0,49,6,'非法卡','Invalid Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1341,0,0,49,11,'用户号加密码开门','By ID and password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1343,0,0,50,1,'空调','Air-Condition',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1345,0,0,50,2,'开关电源','Switch Power',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1347,0,0,50,3,'电池','Battery',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1349,0,0,50,4,'电缆','Cable',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1351,0,0,50,5,'UPS','UPS',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1353,0,0,51,1,'分布式服务','Distributed Service',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1355,0,0,51,2,'数据复制服务','Data Replicate Service',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1357,0,0,51,3,'数据备份服务','Data Backup Service',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1359,0,0,51,4,'数据恢复服务','Data Restore Service',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1361,0,0,51,5,'数据分析服务','Data Analysis Service',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1363,0,0,52,1,'自动执行','Execute Automatically',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1365,0,0,52,2,'手动执行','Execute Manually',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1367,0,0,52,3,'自动手动均可','Execute Automatically or Manually',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1369,0,0,53,1,'按秒','In Seconds',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1371,0,0,53,2,'按分','In Minutes',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1373,0,0,53,3,'按小时','Hourly',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1375,0,0,53,4,'按天','Daily',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1377,0,0,53,5,'按月','Monthly',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1379,0,0,54,1,'整表处理','Whole Table Process',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1381,0,0,54,2,'分时间处理','Period Process',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1383,0,0,55,-12,'定制的蓄电池管理报表','Customize Battery Management',1,1,0,'','noimage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1385,0,0,55,-11,'四川定制报表','SiChuan Customize',1,1,0,'','noimage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1387,0,0,55,-10,'高低压配电管理','High And Low Voltage Distribution',1,1,0,'','PowerDistributeReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1389,0,0,55,-9,'告警管理','Event Management',1,1,0,'','AlarmReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1391,0,0,55,-8,'安全管理','Security Management',1,1,0,'','SecurityReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1393,0,0,55,-7,'考核报表','Assessment Report',1,1,0,'','CheckWorkReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1395,0,0,55,-6,'遥测数据分析','Telemetry Data Analysis',1,1,0,'','RemoteControlReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1397,0,0,55,0,'无分类报表','Ungrouped Report',1,1,0,'','NoTypeReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1399,0,0,55,1,'人员管理','User Management',1,0,0,'','usermanage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1401,0,0,55,2,'设备管理','Equipment Management',1,0,0,'','equipmentmanage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1403,0,0,55,3,'门禁管理','Door Access Management',1,0,0,'','janitormanage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1405,0,0,55,4,'运行维护','Running Maintenance',1,0,0,'','runmaintenance.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1407,0,0,55,6,'配置信息','Configuration Asset',1,0,0,'','configurecapital.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1409,0,0,55,7,'系统报表','System Report',1,0,0,'','SystemReportCategory.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1411,0,0,55,5,'电池管理','Battery Management',1,0,0,'','BatteryManage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1413,0,0,55,8,'能源管理','PowerConsumption ManageMent',1,0,0,'','noimage.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1415,0,0,55,9,'告警标准化报表','Event Standardization',1,1,0,'','StdAlarmReport.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1417,0,0,56,1,'年报表','Yearly Report',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1419,0,0,56,2,'月报表','Monthly Report',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1421,0,0,56,3,'日报表','Daily Report',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1423,0,0,56,4,'时报表','Hourly Report',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1425,0,0,56,5,'分报表','Minutely Report',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1427,0,0,56,6,'秒报表','Secondly Report',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1429,0,0,56,7,'时间段报表','Period Report',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1431,0,0,56,8,'无时间分类报表','Time-independent Report',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1433,0,0,57,1,'数据表','Record Table',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1435,0,0,57,2,'饼状图','Pie Chart',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1437,0,0,57,3,'柱状图','Column Chart',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1439,0,0,57,4,'折线图','Curve Chart',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1441,0,0,57,5,'混合表','Mix Table',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1443,0,0,57,6,'矩阵表','Matrix Table',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1445,0,0,58,1,'应用服务器','Application Server',1,1,0,'','ApplicationServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1447,0,0,58,2,'数据服务器','Data Server',1,1,0,'','DataServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1449,0,0,58,3,'数据服务控制器','Data Server Controller',1,1,0,'','DataServerController.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1451,0,0,58,4,'地图服务器','Map Server',1,1,0,'','MapServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1453,0,0,58,5,'报表服务器','Report Server',1,1,0,'','ReportServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1455,0,0,58,6,'数据库服务器','Database Server',1,1,0,'','DatabaseServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1457,0,0,58,7,'远程应用服务器','Remote Application Server',1,1,0,'','ApplicationServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1459,0,0,58,8,'RMU','Real-time Monitoring Unit',1,1,0,'','FrontDataServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1461,0,0,58,9,'Web服务器','Web Server',1,1,0,'','WebServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1463,0,0,58,11,'组态服务器','Configurable Graphic Server',1,1,0,'','ConfigGraphicServer.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1465,0,0,58,12,'图像服务器','Video Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1467,0,0,58,13,'C接口服务器','C-Interface Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1469,0,0,58,14,'通知主机服务器','Notification Host Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1471,0,0,58,15,'综合资源同步服务器','Resource synchronization Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1473,0,0,58,16,'通知服务器','Notification Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1475,0,0,58,20,'业务服务器','BS Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1477,0,0,58,21,'告警复制服务器','Alarm Copy Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1479,0,0,58,22,'后台服务器','BackGround Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1481,0,0,58,23,'实时数据服务器','RealTime Data Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1483,0,0,58,24,'手机接口服务器','Mobile Interface Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1484,0,0,58,25,'定时任务服务器','Scheduled Task Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1485,0,0,58,31,'应用服务器II','Application ServerII',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1487,0,0,58,32,'业务服务器II','BS ServerII',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1489,0,0,58,101,'B接口服务器','B-Interface Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1491,0,0,59,1,'运行','Running',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1493,0,0,59,2,'停机','Halt',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1495,0,0,59,3,'故障','Fault',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1497,0,0,60,1,'TCP','TCP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1499,0,0,60,2,'UDP','UDP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1501,0,0,60,3,'HTTP','HTTP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1503,0,0,60,4,'SMS','SMS',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1505,0,0,60,5,'MSMQ','MSMQ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1507,0,0,61,1,'心跳','Heartbeat',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1509,0,0,61,2,'事件','Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1511,0,0,61,3,'实时','Real-time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1513,0,0,61,4,'历史','History',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1515,0,0,61,5,'请求','Request',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1517,0,0,61,6,'响应','Reponse',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1519,0,0,63,1,'PDF','PDF',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1521,0,0,63,2,'EXCEL','EXCEL',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1523,0,0,63,3,'HTML','HTML',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1525,0,0,63,4,'CSV','CSV',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1527,0,0,63,5,'RTF','RTF',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1529,0,0,64,1,'从不执行','Execute Never',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1531,0,0,64,2,'仅执行一次','Execute Once',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1533,0,0,64,3,'总是执行','Execute Forever',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1535,0,0,65,1,'共享模式','Concurrent Mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1537,0,0,65,2,'独占模式','Exclusive Mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1539,0,0,66,1,'串行模式','Serial Mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1541,0,0,66,2,'并行模式','Parallel Mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1543,0,0,67,1,'全时段屏蔽','Deactivate Daily ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1545,0,0,67,2,'分时段屏蔽','Deactivate Weekly',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1547,0,0,68,1,'文本框','Textbox',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1549,0,0,68,2,'数字框','Numerical Box',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1551,0,0,68,3,'滚动条','Scroll Bar',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1553,0,0,68,4,'下拉框','ComboBox',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1555,0,0,68,5,'日期','Date',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1557,0,0,69,1,'事件开始','Event Start',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1559,0,0,69,2,'事件结束','Event End',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1561,0,0,69,3,'开始确认','Start Confirm',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1563,0,0,69,4,'结束确认','End Confirm',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1565,0,0,69,5,'事件升级','Event Upgrade',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1567,0,0,70,0,'浮点型(FLOAT)','Float',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1569,0,0,70,1,'字符串(STRING)','String',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1571,0,0,70,2,'密码类型','Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1573,0,0,71,1,'基站','Site',1,1,0,'','BigStation.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1575,0,0,71,2,'机房','Station',1,1,0,'','SmallStation.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1577,0,0,71,3,'虚拟局站','Virtual Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1579,0,0,71,4,'服务厅','Service Hall',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1581,0,0,71,5,'传输汇接层','Transfer Aggregation Layer',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1583,0,0,71,6,'工程站','Engineering Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1585,0,0,71,7,'备用站','Standby Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1587,0,0,71,8,'微蜂窝','Microcell',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1589,0,0,71,9,'村通局站','Village Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1591,71,1,71,10,'VIP基站','VIP Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1593,71,1,71,11,'VVIP','VVIP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1595,71,1,71,12,'一级VIP','一级VIP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1597,71,1,71,13,'二级VIP','二级VIP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1599,71,1,71,14,'三级VIP','三级VIP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1601,0,0,71,15,'其他类型','其他类型',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1603,0,0,71,16,'干结点','干结点',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1605,71,5,71,17,'一类节点','区域性骨干节点',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1607,71,5,71,18,'二类节点','汇聚节点',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1609,71,5,71,19,'三类节点','综合业务接入节点',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1611,71,2,71,20,'一类机房','一类机房',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1613,71,2,71,21,'二类机房','二类机房',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1615,71,2,71,22,'三类机房','三类机房',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1617,0,0,71,23,'微站','Micro Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1619,0,0,71,24,'高铁专网','High Railway',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1621,0,0,71,25,'超级VIP基站','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1623,0,0,71,26,'数据中心','Data Center',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1625,0,0,72,1,'机房','Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1627,0,0,72,2,'设备类','Equipment Category',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1629,0,0,73,1,'DC505门禁','DC505 Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1631,0,0,73,2,'2750门禁','2750 Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1633,0,0,73,3,'ES2000-1门禁','ES2000-1 Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1635,0,0,73,4,'ES2000-2门禁','ES2000-2 Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1637,0,0,73,5,'一体化门禁','Integrative Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1639,0,0,73,6,'EDM30E门禁','EDM30E Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1641,0,0,74,1,'布防','Deploy',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1643,0,0,74,2,'撤防','Disarm',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1645,0,0,75,1,'未分组','UnGrouped',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1647,0,0,76,1,'最近12小时','The most recent 12 hours',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1649,0,0,76,2,'最近24小时','The most recent 24 hours',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1651,0,0,76,3,'最近48小时','The most recent 48 hours',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1653,0,0,79,1,'已监控有效','Monitoring and Valid',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1655,0,0,79,2,'已监控无效','Monitoring and Invalid',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1657,0,0,79,3,'未监控','Not Monitoring',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1659,0,0,80,1,'应用服务器登录','Login from Application Server',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1661,0,0,80,2,'配置工具登录','Login from Configuration Tool',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1663,0,0,81,1,'手动执行','Manual Execution',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1665,0,0,81,2,'定时执行','Periodic Execution',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1667,0,0,81,3,'告警联动','Event InterAction',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1669,0,0,81,4,'群控','Control in batch',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1671,0,0,82,1,'代维商','Maintenance Proxy',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1673,0,0,82,2,'其他','Other',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1675,0,0,83,0,'不通知 ','UnNotified',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1677,0,0,83,1,'等待投递 ','Waiting for Delivery',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1679,0,0,83,2,'正在投递 ','Deliverying',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1681,0,0,83,3,'投递成功  ','Deliveried Succeed',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1683,0,0,83,4,'投递失败 ','Deliveried Failure',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1685,0,0,83,5,'通知成功 ','Notified Succeed',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1687,0,0,83,6,'通知失败 ','Notified Failure',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1689,0,0,83,7,'通知超时 ','Notified Time 0ut',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1691,0,0,83,8,'派单处理中','Notification Dealing',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1693,0,0,83,9,'派单处理完毕','Notification Closed',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1695,0,0,83,10,'延迟时间内结束','Closed During Delay',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1697,0,0,83,11,'过期的告警','Over Due Alarm',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1699,0,0,83,12,'通知主机异常的失效告警','Invalidation Notify',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1701,0,0,84,1,'男','Male',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1703,0,0,84,2,'女','Female',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1705,0,0,85,0,'离线','Offline',1,1,0,'','Offline.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1707,0,0,85,1,'在线','Online',1,1,0,'','Online.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1709,0,0,85,2,'异常','Exception',1,1,0,'','Fault.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1711,0,0,85,3,'断开','Interruption',1,1,0,'','Break.png',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1713,0,0,86,1,'检查设备条件，信号，事件表达式是否存在非法字符','Check for the invalid characters in equipment, signal and event expression.',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1715,0,0,86,2,'检查设备条件，信号，事件表达式长度是否越界','Check that the Expression Length of Equipment, Signal and Event is within the allowed range.',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1717,0,0,86,3,'检查事件条件中的事件等级是否为空','Check that the Event severity condition is null.',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1719,0,0,87,1,'登录','Login',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1721,0,0,87,2,'退出','Logout',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1723,0,0,87,3,'踢出','KickOut',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1725,0,0,87,4,'确认','ConfirmEvent',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1727,0,0,87,5,'强制结束','EndEvent',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1729,0,0,87,6,'录入备注信息','AddEventNote',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1731,0,0,87,7,'EOMS手动派单','AsignEOMS',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1733,0,0,87,8,'动态配置','DynamicConfig',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1735,0,0,87,9,'局站屏蔽设置','MaskStation',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1737,0,0,87,10,'设备屏蔽设置','MaskEquipment',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1739,0,0,87,11,'事件屏蔽设置','MaskEvent',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1741,0,0,87,12,'发送控制命令','SendControlCommand',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1743,0,0,87,13,'取消控制命令','CancelControlCommand',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1745,0,0,87,14,'确认控制命令','ConfirmControlCommand',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1747,0,0,87,15,'开关门命令','OpenOrCloseDoorCommand',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1749,0,0,87,16,'专家建议新增','AddEExperience',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1751,0,0,87,17,'专家建议修改','EditExperience',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1753,0,0,87,18,'专家建议删除','DeleteExperience',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1755,0,0,87,19,'过滤条件新增','AddFilter',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1757,0,0,87,20,'过滤条件删除','DeleteFilter',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1759,0,0,87,21,'过滤条件修改','EditFilter',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1761,0,0,87,22,'界面列调整','AdjustUserInterface',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1763,0,0,87,50,'配置更改','Change Configuration ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1765,0,0,88,0,'四级告警','四级告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1767,0,0,88,1,'三级告警','三级告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1769,0,0,88,2,'二级告警','二级告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1771,0,0,88,3,'一级告警','一级告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1773,0,0,89,1,'设备告警','设备告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1775,0,0,89,2,'环境告警','环境告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1777,0,0,89,3,'电网异常','电网异常',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1779,0,0,91,0,'未定义','未定义',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1781,0,0,92,0,'未定义','未定义',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1783,0,0,93,0,'未定义','未定义',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1785,0,0,94,0,'未定义','未定义',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1787,0,0,95,0,'未定义','未定义',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1789,0,0,96,0,'未定义','未定义',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1791,0,0,99,1,'动环网','动环网',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1793,0,0,99,2,'话务网','话务网',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1795,0,0,100,1,'标准','标准',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1797,0,0,101,1,'表达式触发','Expression  Trigger',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1799,0,0,101,2,'时间触发','Time Trigger',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1801,0,0,102,1,'邮件通知','Mail Inform',1,1,0,'','Email.so',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1803,0,0,102,2,'手机通知','Mobile Inform',1,1,0,'','Mobile.so',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1805,0,0,103,1,'用户手动执行','Manual Excute',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1807,0,0,103,2,'告警联动自动执行','EventAction Auto Excute',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1809,0,0,103,3,'定时触发执行','Timing Trigger Excute',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1811,0,0,104,1,'Telnet(TCP/IP)','Telnet(TCP/IP)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1813,0,0,104,2,'Direct','Direct',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1815,0,0,104,3,'PSTN Modem','PSTN Modem',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1817,0,0,104,4,'GPRS Modem','GPRS Modem',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1819,0,0,104,5,'GSM','GSM  Modem',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1821,0,0,105,1,'M.C','M.C',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1823,0,0,105,2,'C.S.U.','C.S.U.',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1825,0,0,106,1,'M.C','M.C',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1827,0,0,106,2,'Deactivated','Deactivated',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1829,0,0,106,3,'C.S.U.','C.S.U.',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1831,0,0,107,1,'EEM','EEM',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1833,0,0,107,2,'SOC','SOC',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1835,0,0,107,3,'SOC-TPE','SOC-TPE',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1837,0,0,107,4,'SNMP','SNMP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1839,0,0,108,1,'MIB LXP','MIB LXP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1841,0,0,108,2,'MIB LMS','MIB LMS',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1843,0,0,108,3,'MIB VORTEX','MIB VORTEX',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1845,0,0,109,1,'ENP','ENP',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1847,0,0,109,2,'ENP Dubai','ENP Dubai',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1849,0,0,109,3,'JAVI Customer','JAVI Customer',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1851,0,0,109,4,'Other Company','Other Company',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1853,0,0,110,1,'点对点','P2P',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1855,0,0,110,2,'链式','LinkedList',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1857,0,0,110,3,'双向环','Bi-Directional Ring',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1859,0,0,110,4,'抽时隙','Pumping Slot',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1861,0,0,110,5,'用户DCN','DCN',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1863,0,0,110,6,'PSTN','PSTN',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1865,0,0,110,7,'无线猫','Wireless Modem',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1867,0,0,111,1,'RJ23','RJ23',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1869,0,0,111,2,'RJ45','RJ45',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1871,7,51,112,1,'温度','Temperature',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1873,7,51,112,2,'湿度','Humidity',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1875,7,51,112,3,'水浸','Flood ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1877,7,51,112,4,'烟雾','Fog',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1879,7,51,112,5,'红外','Infrared',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1881,7,51,112,6,'综合防盗','Anti-theft',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1883,7,51,112,7,'门磁','Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1885,7,52,112,8,'温度','Temperature',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1887,7,52,112,9,'湿度','Humidity',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1889,7,52,112,10,'水浸','Flood ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1891,7,52,112,11,'烟雾','Fog',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1893,7,52,112,12,'红外','Infrared',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1895,7,52,112,13,'综合防盗','Anti-theft',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1897,7,52,112,14,'门磁','Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1899,7,53,112,15,'温度','Temperature',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1901,7,53,112,16,'湿度','Humidity',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1903,7,53,112,17,'水浸','Flood ',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1905,7,53,112,18,'烟雾','Fog',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1907,7,53,112,19,'红外','Infrared',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1909,7,53,112,20,'综合防盗','Anti-theft',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1911,7,53,112,21,'门磁','Door Access',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1913,7,22,112,22,'交流输入AB线电压/相电压','交流输入AB线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1915,7,22,112,23,'交流屏输入BC线电压','交流屏输入BC线电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1917,7,22,112,24,'交流屏输入CA线电压','交流屏输入CA线电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1919,7,22,112,25,'交流输出A相电流','交流输出A相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1921,7,22,112,26,'交流输出B相电流','交流输出B相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1923,7,22,112,27,'交流输出C相电流','交流输出C相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1925,7,22,112,28,'直流输出电压','直流输出电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1927,7,22,112,29,'负载总电流','负载总电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1929,7,22,112,30,'电池总电压','电池总电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1931,7,22,112,31,'电池温度','电池温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1933,7,22,112,32,'系统均浮充状态','系统均浮充状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1935,7,41,112,33,'回风温度','回风温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1937,7,41,112,34,'回风湿度','回风湿度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1939,7,41,112,35,'空调状态','空调状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1941,7,41,112,36,'开关机状态','开关机状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1943,7,42,112,37,'回风温度','回风温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1945,7,42,112,38,'回风湿度','回风湿度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1947,7,42,112,39,'空调状态','空调状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1949,7,42,112,40,'开关机状态','开关机状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1951,7,43,112,41,'回风温度','回风温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1953,7,43,112,42,'回风湿度','回风湿度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1955,7,43,112,43,'空调状态','空调状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1957,7,43,112,44,'开关机状态','开关机状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1959,7,44,112,45,'回风温度','回风温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1961,7,44,112,46,'回风湿度','回风湿度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1963,7,44,112,47,'空调状态','空调状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1965,7,44,112,48,'开关机状态','开关机状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1967,7,45,112,49,'回风温度','回风温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1969,7,45,112,50,'回风湿度','回风湿度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1971,7,45,112,51,'空调状态','空调状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1973,7,45,112,52,'开关机状态','开关机状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1975,7,31,112,53,'交流输入AB线电压/相电压','交流输入AB线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1977,7,31,112,54,'交流输入BC线电压/相电压','交流输入BC线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1979,7,31,112,55,'交流输入CA线电压/相电压','交流输入CA线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1981,7,31,112,56,'交流输出AB线电压/相电压','交流输出AB线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1983,7,31,112,57,'交流输出BC线电压/相电压','交流输出BC线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1985,7,31,112,58,'交流输出CA线电压/相电压','交流输出CA线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1987,7,31,112,59,'电池组总电压','电池组总电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1989,7,31,112,60,'单体电池温度','单体电池温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1991,7,31,112,61,'供电方式','供电方式',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1993,7,13,112,62,'AB线电压/相电压','AB线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1995,7,13,112,63,'BC线电压/相电压','BC线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1997,7,13,112,64,'CA线电压/相电压','CA线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (1999,7,13,112,65,'A相电流','A相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2001,7,13,112,66,'B相电流','B相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2003,7,13,112,67,'C相电流','C相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2005,7,13,112,68,'输出频率/转速','输出频率/转速',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2007,7,13,112,69,'水温','水温',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2009,7,13,112,70,'油温','油温',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2011,7,13,112,71,'油位','油位',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2013,7,13,112,72,'油压','油压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2015,7,13,112,73,'启动电池电压','启动电池电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2017,7,13,112,74,'自动手动状态','自动手动状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2019,7,13,112,75,'停机/运行状态','停机/运行状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2021,7,82,112,76,'卡号','卡号',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2023,7,82,112,77,'刷卡时间','刷卡时间',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2025,7,82,112,78,'非法开门状态','非法开门状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2027,7,82,112,79,'门开关状态','门开关状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2029,7,85,112,80,'AB线电压/相电压','AB线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2031,7,85,112,81,'BC线电压/相电压','BC线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2033,7,85,112,82,'CA线电压/相电压','CA线电压/相电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2035,7,85,112,83,'A相电流','A相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2037,7,85,112,84,'B相电流','B相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2039,7,85,112,85,'C相电流','C相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2041,7,85,112,86,'交流频率','交流频率',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2043,7,85,112,87,'A相功率因数','A相功率因数',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2045,7,85,112,88,'B相功率因数','B相功率因数',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2047,7,85,112,89,'C相功率因数','C相功率因数',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2049,7,85,112,90,'正向有功电能累加值（总用电量）','正向有功电能累加值（总用电量）',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2051,7,24,112,91,'电池组总电压','电池组总电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2053,7,24,112,92,'电池组充放电电流','电池组充放电电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2055,7,24,112,93,'电池后备时间','电池后备时间',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2057,7,24,112,94,'电池组温度','电池组温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2059,7,24,112,95,'电池状态','电池状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2061,7,24,112,96,'单体电池落后状态','单体电池落后状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2063,7,86,112,97,'室内温度','室内温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2065,7,86,112,98,'室外温度','室外温度',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2067,7,86,112,99,'进风风机状态','进风风机状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2069,7,86,112,100,'出风风机状态','出风风机状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2071,7,86,112,101,'进风风机故障','进风风机故障',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2073,7,86,112,102,'出风风机故障','出风风机故障',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2075,7,11,112,103,'AB线电压','AB线电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2077,7,11,112,104,'BC线电压','BC线电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2079,7,11,112,105,'CA线电压','CA线电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2081,7,11,112,106,'A相电流','A相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2083,7,11,112,107,'B相电流','B相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2085,7,11,112,108,'C相电流','C相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2087,7,11,112,109,'出线线柜状态','出线线柜状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2089,7,11,112,110,'进线柜状态','进线柜状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2091,7,11,112,111,'变压器系统状态','变压器系统状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2093,7,11,112,112,'高压操作电池故障状态','高压操作电池故障状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2095,7,11,112,113,'高压操作电池温度过高告警状态','高压操作电池温度过高告警状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2097,7,12,112,114,'AB线电压','AB线电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2099,7,12,112,115,'BC线电压','BC线电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2101,7,12,112,116,'CA线电压','CA线电压',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2103,7,12,112,117,'A相电流','A相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2105,7,12,112,118,'B相电流','B相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2107,7,12,112,119,'C相电流','C相电流',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2109,7,12,112,120,'进线柜开关状态','进线柜开关状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2111,7,12,112,121,'配电柜开关状态','配电柜开关状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2113,7,12,112,122,'过载跳闸状态','过载跳闸状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2115,7,12,112,123,'失压跳闸状态','失压跳闸状态',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2117,7,51,113,1,'水浸告警','水浸告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2119,7,51,113,2,'烟雾告警','烟雾告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2121,7,51,113,3,'红外告警','红外告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2123,7,51,113,4,'综合防盗','综合防盗',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2125,7,51,113,5,'门磁告警','门磁告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2127,7,52,113,6,'水浸告警','水浸告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2129,7,52,113,7,'烟雾告警','烟雾告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2131,7,52,113,8,'红外告警','红外告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2133,7,52,113,9,'综合防盗','综合防盗',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2135,7,52,113,10,'门磁告警','门磁告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2137,7,53,113,11,'水浸告警','水浸告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2139,7,53,113,12,'烟雾告警','烟雾告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2141,7,53,113,13,'红外告警','红外告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2143,7,53,113,14,'综合防盗','综合防盗',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2145,7,53,113,15,'门磁告警','门磁告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2147,7,22,113,16,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2149,7,41,113,17,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2151,7,42,113,18,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2153,7,43,113,19,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2155,7,44,113,20,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2157,7,45,113,21,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2159,7,31,113,22,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2161,7,13,113,23,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2163,7,82,113,24,'非法开门告警','非法开门告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2165,7,82,113,25,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2167,7,24,113,26,'单体电池落后告警','单体电池落后告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2169,7,86,113,27,'进风风机故障','进风风机故障',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2171,7,86,113,28,'出风风机故障','出风风机故障',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2173,7,86,113,29,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2175,7,11,113,30,'变压器系统告警','变压器系统告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2177,7,11,113,31,'高压操作电池故障告警','高压操作电池故障告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2179,7,11,113,32,'高压操作电池温度过高告警','高压操作电池温度过高告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2181,7,11,113,33,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2183,7,12,113,34,'过载跳闸告警','过载跳闸告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2185,7,12,113,35,'失压跳闸告警','失压跳闸告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2187,7,12,113,36,'其他告警','其他告警',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2189,7,22,114,1,'系统均充命令','系统均充命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2191,7,22,114,2,'系统浮充命令','系统浮充命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2193,7,41,114,3,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2195,7,41,114,4,'开机/关机命令','开机/关机命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2197,7,42,114,5,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2199,7,42,114,6,'开机/关机命令','开机/关机命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2201,7,43,114,7,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2203,7,43,114,8,'开机/关机命令','开机/关机命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2205,7,44,114,9,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2207,7,44,114,10,'开机/关机命令','开机/关机命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2209,7,44,114,11,'温度设定值（含遥调命令）','温度设定值（含遥调命令）',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2211,7,44,114,12,'开机/关机命令','开机/关机命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2213,7,13,114,13,'开机/关机','开机/关机',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2215,7,13,114,14,'紧急停机','紧急停机',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2217,7,82,114,15,'远程开门命令','远程开门命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2219,7,86,114,16,'开/关风机命令','开/关风机命令',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2221,0,0,115,1,'SiteWeb权限','SiteWeb Previlege',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2223,0,0,115,2,'油机调度权限','Generator Previlege',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2225,0,0,116,1,'轮流开机','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2227,0,0,116,2,'同时开机','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2229,0,0,1001,0,'其它','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2231,0,0,1001,1,'平顶砖混','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2233,0,0,1001,2,'尖顶砖混','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2235,0,0,1001,3,'彩钢板','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2237,0,0,1001,4,'一体化机房','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2239,0,0,1001,5,'住宅楼','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2241,0,0,1001,6,'办公楼','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2243,0,0,1002,0,'不设定','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2245,0,0,1002,1,'设备','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2247,0,0,1002,2,'空调','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2249,0,0,1003,1,'电表日用电量异常','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2251,0,0,1003,2,'月用电量异常','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2253,0,0,1003,3,'电费折算月用电量异常','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2255,0,0,1003,4,'智能电表工作状态异常','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2257,0,0,1004,1,'6','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2259,0,0,1004,2,'12','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2261,0,0,1004,3,'18','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2263,0,0,1004,4,'24','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2265,0,0,1004,5,'32','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2267,0,0,1005,1,'小面积','',1,0,0,'',' Acreage > 0 AND Acreage <=15 ','0-15',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2269,0,0,1005,2,'中面积','',1,0,0,'',' Acreage > 15 AND Acreage <=30 ','15-30',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2271,0,0,1005,3,'大面积','',1,0,0,'',' Acreage > 30 AND Acreage <=45 ','30-45',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2273,0,0,1005,4,'超大面积','',1,0,0,'',' Acreage > 45  ','45',NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2275,0,0,1006,0,'未知','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2277,0,0,1006,1,'爱立信','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2279,0,0,1006,2,'阿尔卡特','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2281,0,0,1006,3,'华为','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2283,0,0,1006,4,'诺基亚','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2285,0,0,1006,5,'西门子','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2287,0,0,1006,6,'中兴','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2289,0,0,1007,0,'未知','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2291,0,0,1007,1,'大金','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2293,0,0,1007,2,'三菱','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2295,0,0,1007,3,'三洋','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2297,0,0,1007,4,'海尔','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2299,0,0,1007,5,'松下','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2301,0,0,1007,7,'海尔三菱','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2303,0,0,1007,8,'格力','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2305,0,0,1007,9,'美的','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2307,0,0,1008,0,'未知','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2309,0,0,1008,1,'维谛','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2311,0,0,1008,2,'中达','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2313,0,0,1008,3,'中恒','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2315,0,0,1008,4,'易达','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2317,0,0,1008,5,'珠江','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2319,0,0,1008,6,'罗兰','',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2321,0,0,1009,0,'未知','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2323,0,0,1009,1,'交流电表','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2325,0,0,1009,2,'直流电表','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2327,0,0,1010,0,'无','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2329,0,0,1010,1,'有','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2331,0,0,117,0,'其他','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2333,0,0,117,1,'放电测试','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2335,0,0,117,2,'停电','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2337,0,0,118,0,'不处理','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2339,0,0,118,1,'更换电池','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2341,0,0,120,0,'其它','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2343,0,0,120,1,'屏蔽时间段','Mask Duration',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2345,0,0,120,2,'工程预约屏蔽','Project Precontract',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2347,0,0,120,10,'准进时间组1','Allowed to enter the time group 1',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2349,0,0,120,11,'准进时间组2','Allowed to enter the time group 2',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2351,0,0,120,12,'准进时间组3','Allowed to enter the time group 3',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2353,0,0,120,13,'准进时间组4','Allowed to enter the time group 4',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2355,0,0,120,14,'准进时间组5','Allowed to enter the time group 5',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2357,0,0,121,0,'其它','Other',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2359,0,0,121,1,'主设备电表直采模式','Master Equipment Ammeter Direct Sourcing Model',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2361,0,0,121,2,'主设备计算模式','Master Equipment Computing Model',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2363,0,0,121,3,'电源电表直采模式','Power Supply Ammeter Direct Sourcing Model',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2365,0,0,121,4,'电源计算模式','Power Supply Computing Model',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2367,0,0,122,0,'未知','Unknown',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2369,0,0,122,1,'局站工程预约状态下的事件','Events at station building',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2371,0,0,122,2,'系统强制结束的超长时间事件','The long time events of system terminated',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2373,0,0,123,0,'未知','Unknown',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2375,0,0,123,1,'停电放电','Discharge because of power off',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2377,0,0,123,2,'手动放电','Manual Discharge',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2379,0,0,124,0,'0','Contract Site Count',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2381,0,0,150,1,'监控中心','MonitorCenter',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2383,0,0,150,2,'局站分组','StationStructure',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2385,0,0,150,3,'局房','House',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2387,0,0,150,4,'工作站','WorkStation',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2389,0,0,150,5,'局站','Station',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2391,0,0,150,6,'监控单元','MonitorUnit',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2393,0,0,150,7,'端口','Port',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2395,0,0,150,8,'采集单元','SamplerUnit',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2397,0,0,150,9,'采集器','Sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2399,0,0,150,10,'设备模板','EquipmentTemplate',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2401,0,0,150,11,'设备','Equipment',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2403,0,0,150,12,'信号','Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2405,0,0,150,13,'信号含义','SignalMeanings',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2407,0,0,150,14,'信号属性','SignalProperty',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2409,0,0,150,15,'事件','Event',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2411,0,0,150,16,'事件条件','EventCondition',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2413,0,0,150,17,'控制','Control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2415,0,0,150,18,'控制含义','ControlMeanings',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2417,0,0,150,19,'样板站','SwatchStation',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2419,0,0,150,20,'人员管理','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2421,0,0,150,21,'部门','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2423,0,0,150,22,'人员','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2425,0,0,150,23,'帐号','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2427,0,0,150,24,'片区','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2429,0,0,150,25,'模块','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2431,0,0,150,26,'操作','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2433,0,0,150,27,'专业','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2435,0,0,150,28,'角色','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2437,0,0,150,29,'权限组授权','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2439,0,0,150,30,'角色帐号授权','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2441,0,0,150,31,'角色权限组授权','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2443,0,0,150,32,'门禁卡','',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2445,0,0,151,1,'缺失实例信号','MonitorUnitSignal Error',1,1,0,'缺失实例信号',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2447,0,0,151,2,'信号类型错误','SignalType Error',1,1,0,'信号类型错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2449,0,0,151,3,'告警过滤表达式错误','EventExpression Error',1,1,0,'告警过滤表达式错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2451,0,0,151,4,'事件开始表达式语法错误','EventExpression Error',1,1,0,'事件开始表达式语法错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2453,0,0,151,5,'事件结束表达式语法错误','EventExpression Error',1,1,0,'事件结束表达式语法错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2455,0,0,151,6,'设备告警过滤表达式语法错误','EquipmentExpression Error',1,1,0,'设备告警过滤表达式语法错误',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2457,0,0,152,0,'否','No',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2459,0,0,152,1,'是','Yes',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2461,0,0,153,0,'系统项','System',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2463,0,0,153,1,'新增项','Add',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2465,0,0,153,2,'修改项','Modify',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2467,0,0,156,1,'解除告警屏蔽','Clear Mask',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2469,0,0,156,2,'结束告警','End Alarm',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2471,0,0,156,3,'确认告警','Confirm Alarm',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2473,0,0,156,5,'增加事件注释','Add Note',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2475,0,0,156,6,'保存派单状态','Save Notification Status',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2477,0,0,156,7,'保存派单号','Save Notification ID',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2479,0,0,156,21,'发送工单','Send Work Order',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2481,0,0,2022,1,'电脑','computer',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2483,0,0,2022,2,'采集器','sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2485,0,0,2022,3,'智能设备','sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2487,0,0,2022,4,'非智能设备','sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2489,0,0,2022,5,'网络资产','sampler',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2491,0,0,2003,1,'ID卡','RFID',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2493,0,0,2003,2,'IC卡','RFIC',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2495,0,0,2004,0,'刷卡 或 指纹 ','Key Or Card Or Finger',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2497,0,0,2004,1,'刷卡 + 指纹','Card+Finger',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2499,0,0,2004,2,'ID号 + 指纹','ID+Finger',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2501,0,0,2004,3,'ID号 + 密码','ID+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2503,0,0,2004,4,'刷卡 + 密码','Card+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2505,0,0,2004,5,'指纹 + 密码','Finger+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2507,0,0,2004,7,'刷卡 + 指纹 + 密码','Card+Finger+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2509,0,0,2005,1,'出门','Out',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2511,0,0,2005,0,'进门','In',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2513,0,0,2006,0,'纽贝尔806D4M3/D2M3','806D4M3/D2M3',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2515,0,0,2006,1,'艾默生ISU','ISU',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2517,0,0,2006,2,'艾默生IDU','IDU',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2519,0,0,2006,3,'艾默生eStone','eStone',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2521,0,0,2006,4,'其他','Others',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2523,0,0,2007,8001,'串口1','COM1',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2525,0,0,2007,8002,'串口2','COM2',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2527,0,0,2007,8003,'串口3','COM3',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2529,0,0,2007,8004,'串口4','COM4',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2531,0,0,2007,8005,'串口5','COM5',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2533,0,0,2007,8006,'串口6','COM6',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2535,0,0,2007,8007,'串口7','COM7',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2537,0,0,2007,8008,'串口8','COM8',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2539,0,0,2007,8009,'串口9','COM9',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2541,0,0,2007,8010,'串口10','COM10',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2543,0,0,2007,8011,'串口11','COM11',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2545,0,0,2007,8012,'串口12','COM12',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2547,0,0,2007,8013,'串口13','COM13',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2549,0,0,2007,8014,'串口14','COM14',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2551,0,0,2007,8015,'串口15','COM15',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2553,0,0,2007,8016,'串口16','COM16',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2555,0,0,2008,5001,'串口1','COM1',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2557,0,0,2008,5002,'串口2','COM2',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2559,0,0,2008,5003,'串口3','COM3',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2561,0,0,2008,5004,'串口4','COM4',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2563,0,0,2008,5005,'串口5','COM5',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2565,0,0,2008,5006,'串口6','COM6',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2567,0,0,2008,5007,'串口7','COM7',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2569,0,0,2008,5008,'串口8','COM8',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2571,0,0,2008,5009,'串口9','COM9',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2573,0,0,2008,5010,'串口10','COM10',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2575,0,0,2008,5011,'串口11','COM11',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2577,0,0,2008,5012,'串口12','COM12',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2579,0,0,2008,5013,'串口13','COM13',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2581,0,0,2008,5014,'串口14','COM14',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2583,0,0,2008,5015,'串口15','COM15',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2585,0,0,2008,5016,'串口16','COM16',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2587,0,0,2008,5017,'串口17','COM17',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2589,0,0,2008,5018,'串口18','COM18',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2591,0,0,2008,5019,'串口19','COM19',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2593,0,0,2008,5020,'串口20','COM20',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2595,0,0,2008,5021,'串口21','COM21',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2597,0,0,2008,5022,'串口22','COM22',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2599,0,0,2008,5023,'串口23','COM23',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2601,0,0,2008,5024,'串口24','COM24',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2603,0,0,2009,5001,'串口1','COM1',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2605,0,0,2009,5002,'串口2','COM2',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2607,0,0,2009,5003,'串口3','COM3',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2609,0,0,2009,5004,'串口4','COM4',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2611,0,0,2009,5005,'串口5','COM5',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2613,0,0,2009,5006,'串口6','COM6',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2615,0,0,2009,5007,'串口7','COM7',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2617,0,0,2009,5008,'串口8','COM8',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2619,0,0,2009,5009,'串口9','COM9',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2621,0,0,2009,5010,'串口10','COM10',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2623,0,0,2009,5011,'串口11','COM11',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2625,0,0,2009,5012,'串口12','COM12',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2627,0,0,2009,5013,'串口13','COM13',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2629,0,0,2009,5014,'串口14','COM14',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2631,0,0,2009,5015,'串口15','COM15',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2633,0,0,2009,5016,'串口16','COM16',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2635,0,0,2009,5017,'串口17','COM17',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2637,0,0,2009,5018,'串口18','COM18',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2639,0,0,2009,5019,'串口19','COM19',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2641,0,0,2009,5020,'串口20','COM20',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2643,0,0,2009,5021,'串口21','COM21',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2645,0,0,2009,5022,'串口22','COM22',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2647,0,0,2009,5023,'串口23','COM23',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2649,0,0,2009,5024,'串口24','COM24',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2651,0,0,2009,5025,'串口25','COM25',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2653,0,0,2009,5026,'串口26','COM26',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2655,0,0,2009,5027,'串口27','COM27',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2657,0,0,2009,5028,'串口28','COM28',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2659,0,0,2009,5029,'串口29','COM29',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2661,0,0,2009,5030,'串口30','COM30',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2663,0,0,2009,5031,'串口31','COM31',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2665,0,0,2009,5032,'串口32','COM32',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2667,0,0,2009,5033,'串口33','COM33',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2669,0,0,2009,5034,'串口34','COM34',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2671,0,0,2009,5035,'串口35','COM35',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2673,0,0,2009,5036,'串口36','COM36',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2675,0,0,2009,5037,'串口37','COM37',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2677,0,0,2010,4002,'串口1','COM1',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2679,0,0,2021,1,'门禁卡','Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2681,0,0,2021,2,'指纹','Fingerprint',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2683,0,0,2021,3,'人脸','Face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2685,0,0,31,29,'添加一人多卡','add multiple cards',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2687,0,0,31,30,'删除用户名下一人多卡','delete multiple cards',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2689,0,0,31,31,'添加或修改指纹','add or update finger print',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2691,0,0,31,32,'删除指纹','delete finger print',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2693,0,0,31,33,'添加或修改人脸','add or update face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2695,0,0,31,34,'删除人脸','delete face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2697,0,0,31,35,'添加或修改用户','add or update user',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2699,0,0,31,36,'删除用户','delete user',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2701,0,0,31,37,'采集人脸信息','sample face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2703,0,0,31,38,'采集指纹信息','sample finger print',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2705,0,0,31,39,'设置读卡器默认验证方式（门开方式）','door open mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2707,0,0,31,40,'读头设置指纹','door open mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2709,0,0,31,41,'读头删除指纹','door open mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2711,0,0,2012,0,'普通用户','Ordinary user',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2713,0,0,2012,2,'登记员','Registrar',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2715,0,0,2012,6,'管理员','Administrator',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2717,0,0,2012,10,'用户自定义','User defined',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2719,0,0,2012,14,'超级管理员','Super admin',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2721,0,0,2013,2,'刷卡+密码','Card+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2723,0,0,2013,3,'刷卡','Card',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2725,0,0,2013,4,'刷卡/（工号+密码）','Card/(JobNO+Password)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2727,0,0,2013,5,'指纹','FingerPrint',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2729,0,0,2013,6,'指纹+密码','FingerPrint+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2731,0,0,2013,7,'刷卡/指纹','Card/FingerPrint',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2733,0,0,2013,8,'刷卡+指纹','Card+FingerPrint',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2735,0,0,2013,9,'刷卡+指纹+密码','Card+FingerPrint+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2737,0,0,2013,10,'刷卡/指纹/人脸/(工号+密码)','Card/FingerPrint/Face/(JobNO+Password)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2739,0,0,2013,11,'指纹+人脸','FingerPrint+Face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2741,0,0,2013,13,'刷卡+人脸','Card+Face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2743,0,0,2013,14,'人脸','Face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2745,0,0,2013,15,'工号+密码','JobNO+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2747,0,0,2013,16,'(工号+密码)/指纹','(JobNO+Password)/FingerPrint',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2749,0,0,2013,17,'工号+指纹','JobNO+FingerPrint',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2751,0,0,2013,18,'工号+密码+指纹','JobNO+Password+FingerPrint',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2753,0,0,2013,19,'刷卡+指纹+人脸','Card+FingerPrint+Face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2755,0,0,2013,20,'指纹+人脸+密码','FingerPrint+Face+Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2757,0,0,2013,21,'工号+人脸','JobNO+Face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2759,0,0,2013,23,'指纹/人脸','FingerPrint/Face',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2761,0,0,2013,24,'刷卡/人脸/(工号+密码)','Card/Face/(JobNO+Password)',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2763,0,0,1011,1,'year','datetime control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2765,0,0,1011,2,'month','datetime control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2767,0,0,1011,3,'date','datetime control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2769,0,0,1011,4,'datetime','datetime control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2771,0,0,1011,5,'transfer','combox control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2773,0,0,1011,6,'tree','tree control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2775,0,0,1011,7,'input','textbox control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2777,0,0,1011,8,'check','checkbox control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2779,0,0,1011,9,'radio','radio button control',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2781,0,0,31,42,'设置门常开','Set Door Normally Open',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2783,0,0,31,43,'设置门常闭','Set Door Normally Close',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2785,0,0,31,44,'远程关门','Remote Close',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2787,0,0,31,45,'火警信号有效方式','Effective Way Of Fire Alarm Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2789,0,0,31,46,'卡封锁错误次数','Number Of Card Block Errors',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2791,0,0,31,47,'卡封锁时间','Card Lock Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2793,0,0,31,48,'非法卡刷卡间隔','Illegal Card Swipe Interval',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2795,0,0,31,49,'门开保持时间','Door Open Holding Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2797,0,0,31,50,'门开方式（纽贝尔）','Door Open Mode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2799,0,0,157,1,'门开方式','Door OpenMode',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2801,0,0,157,2,'门禁火警信号有效方式','Door Effective Way Of Fire Alarm Signal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2803,0,0,157,3,'刷卡或开门状态','Card Swiping Or Door Open State',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2805,0,0,157,4,'进出门标志','Entry Or Exit Sign',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2807,0,0,157,5,'门开保持时间','Door Open Holding Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2809,0,0,157,6,'门开超时时间','Door Open Delay Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2811,0,0,157,7,'卡封锁错误次数','Number Of Card Block Errors',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2813,0,0,157,8,'卡封锁时间','Card Lock Time',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2815,0,0,157,9,'非法卡刷卡间隔','Illegal Card Swipe Interval',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2817,0,0,157,10,'门密码','Door Password',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2819,0,0,2023,1,'SNMP','SNMP',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2821,0,0,2023,2,'BACNet','BACNet',1,0,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2823,0,0,39,32,'BACNet端口(Linux RMU)','BACNetPort',1,0,0,'','74',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2825,0,0,39,33,'SNMP端口(Linux RMU)','SNMPPort',1,0,0,'','4',NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2827,0,0,3001,1,'最小级别','min level',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2829,0,0,3001,2,'基本级别','basic level',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2831,0,0,3001,3,'详细级别','detail level',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2833,0,0,3001,4,'未定义级别','undefined level',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2835,0,0,3002,1,'身份用户鉴别','identity authentication',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2837,0,0,3002,2,'攻击检测','attack detection',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2839,0,0,3002,3,'暴力破解','brute force',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2841,0,0,3002,4,'完整性检测','integrity test',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2843,0,0,3004,10,'十进制','decimal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2845,0,0,3004,16,'十六进制','hexadecimal',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2847,0,0,3005,1,'全部','all',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2849,0,0,3005,2,'动环','power & environment supervision',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2851,0,0,3005,3,'通用','general',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2853,0,0,3005,4,'指标类','complex index',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2855,0,0,3005,5,'能耗','energy',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2857,0,0,3005,6,'定制','customize',1,1,0,'',NULL,NULL,NULL,NULL,NULL);
INSERT INTO tbl_dataitem (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2859,0,0,3005,7,'图表','chart',1,1,0,'',NULL,NULL,NULL,NULL,NULL);