# siteweb6北向接口插件国际化配置 - 中文
plugin.name=siteweb6北向接口
plugin.description=用于与第三方系统集成的北向接口插件

# 公共信息
common.success=操作成功
common.error=操作失败
common.save=保存
common.cancel=取消
common.confirm=确认
common.delete=删除
common.edit=编辑
common.view=查看
common.search=搜索
common.reset=重置
common.refresh=刷新

# API相关
api.test.hello=Hello World - siteweb6北向接口
api.test.success=测试连接成功
api.test.failed=测试连接失败
api.log.title=API日志
api.log.list=API调用日志
api.log.detail=日志详情
api.log.request.params=请求参数
api.log.response.data=响应数据
api.log.execution.time=执行时间
api.log.api.path=API路径
api.log.request.method=请求方法
api.log.response.status=响应状态
api.log.create.time=创建时间

# 状态相关
status.running=运行中
status.stopped=已停止
status.success=成功
status.failed=失败

# 统计相关
stats.total.requests=总请求数
stats.success.requests=成功请求
stats.error.requests=失败请求
stats.avg.response.time=平均响应时间

# 错误信息
error.plugin.load.failed=插件加载失败
error.database.connection.failed=数据库连接失败
error.api.call.failed=API调用失败
error.data.not.found=数据未找到
error.parameter.invalid=参数无效 