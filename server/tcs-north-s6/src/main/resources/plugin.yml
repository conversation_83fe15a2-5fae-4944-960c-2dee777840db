plugin:
  id: tcs-tcs-tcs-north-s6
  # 中间件资源配置
  middleware:
    database:
      primary: s6-postgres-config-primary   # 主数据库资源ID
    siteweb-persistent:
      primary: s6-siteweb-persistent-service

  # 插件依赖配置
  dependencies: []
  
  # 插件配置参数
  config:
    # 数据库配置
    database:
      enabled: true
      migration: true
      
    # 接口配置
    api:
      enabled: true
      base-path: /api/north/s6


siteweb:
  real-time-data:
    redis:
      database: 0
      host: *************
      port: 6479
      password: siteweb1!
      maxActive: 200
      maxIdle: 10
      minIdle: 0
      maxWait: -1
      timeout: 1000


  history-data:
    influxdb:
      url: http://*************:8086
      username: admin
      password: adminadmin
      database: siteweb_v2