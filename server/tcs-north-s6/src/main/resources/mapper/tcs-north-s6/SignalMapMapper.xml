<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.SignalMapMapper">

    <resultMap id="BaseResultMap" type="com.siteweb.tcs.north.s6.dal.entity.SignalMap">
        <result column="DeviceId" property="deviceId" />
        <result column="NorthEquipmentId" property="northEquipmentId" />
        <result column="SignalId" property="signalId" />
        <result column="NorthSignalId" property="northSignalId" />
        <result column="Deleted" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        DeviceId, NorthEquipmentId, SignalId, NorthSignalId, Deleted
    </sql>

    <select id="selectByDeviceId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_signal_map
        WHERE DeviceId = #{deviceId} AND Deleted = FALSE
    </select>
    
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO tcs_signal_map (DeviceId, NorthEquipmentId, SignalId, NorthSignalId, Deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.deviceId}, #{item.northEquipmentId}, #{item.signalId}, #{item.northSignalId}, #{item.deleted})
        </foreach>
    </insert>

    <update id="deleteByCompositeKey">
        UPDATE tcs_signal_map
        SET Deleted = TRUE
        WHERE DeviceId = #{deviceId}
        AND SignalId = #{signalId}
        AND NorthSignalId = #{northSignalId}
    </update>

</mapper> 