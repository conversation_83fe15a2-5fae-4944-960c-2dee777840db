-- Creating table tcs_alarm_map
CREATE TABLE tcs_alarm_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INTEGER NOT NULL,
  AlarmId BIGINT NOT NULL,
  NorthEventId INTEGER NOT NULL,
  NorthEventConditionId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (DeviceId, NorthEquipmentId, AlarmId, NorthEventId, NorthEventConditionId)
);

-- Adding comments for tcs_alarm_map
COMMENT ON TABLE tcs_alarm_map IS '告警映射表';
COMMENT ON COLUMN tcs_alarm_map.Deleted IS '逻辑删除标志';

-- Creating table tcs_control_map
CREATE TABLE tcs_control_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INTEGER DEFAULT NULL,
  ControlId BIGINT NOT NULL,
  NorthControlId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (DeviceId, ControlId, NorthControlId)
);

-- Adding comments for tcs_control_map
COMMENT ON TABLE tcs_control_map IS '控制映射表';
COMMENT ON COLUMN tcs_control_map.Deleted IS '逻辑删除标志';

-- Creating table tcs_device_map
CREATE TABLE tcs_device_map (
  GatewayId BIGINT NOT NULL,
  NorthMonitorUnitId INTEGER DEFAULT NULL,
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INTEGER NOT NULL,
  NorthEquipmentTemplateId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (GatewayId, DeviceId, NorthEquipmentId)
);

-- Adding comments for tcs_device_map
COMMENT ON TABLE tcs_device_map IS '设备映射表';
COMMENT ON COLUMN tcs_device_map.Deleted IS '逻辑删除标志';

-- Creating table tcs_gateway_map
CREATE TABLE tcs_gateway_map (
  GatewayId BIGINT NOT NULL,
  NorthMonitorUnitId INTEGER NOT NULL,
  NorthStationId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (GatewayId, NorthMonitorUnitId)
);

-- Adding comments for tcs_gateway_map
COMMENT ON TABLE tcs_gateway_map IS '网关映射表';
COMMENT ON COLUMN tcs_gateway_map.GatewayId IS 'hub全局网关id';
COMMENT ON COLUMN tcs_gateway_map.NorthMonitorUnitId IS 'siteweb采集单元id';
COMMENT ON COLUMN tcs_gateway_map.Deleted IS '逻辑删除标志';

-- Creating table tcs_signal_map
CREATE TABLE tcs_signal_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INTEGER DEFAULT NULL,
  SignalId BIGINT NOT NULL,
  NorthSignalId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (DeviceId, SignalId, NorthSignalId)
);

-- Adding comments for tcs_signal_map
COMMENT ON TABLE tcs_signal_map IS '信号映射表';
COMMENT ON COLUMN tcs_signal_map.Deleted IS '逻辑删除标志';