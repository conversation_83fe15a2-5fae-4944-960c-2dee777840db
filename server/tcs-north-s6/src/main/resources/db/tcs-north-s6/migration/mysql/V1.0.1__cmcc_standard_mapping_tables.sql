-- 移动设备种类表
CREATE TABLE `cmcc_device_type` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `DeviceTypeID` VARCHAR(45) DEFAULT NULL,
  `DeviceTypeName` varchar(128) DEFAULT NULL,
  `DeviceSubTypeID` VARCHAR(45) DEFAULT NULL,
  `DeviceSubTypeName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='移动设备种类表';

-- 移动设备类型映射表
CREATE TABLE `cmcc_device_type_map` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `DeviceTypeID` varchar(45) DEFAULT NULL COMMENT '设备大类id',
  `DeviceSubTypeID` varchar(45) DEFAULT NULL COMMENT '设备子类ID',
  `EquipmentCategoryID` varchar(45) DEFAULT NULL COMMENT 'SiteWeb设备种类id',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 移动标准化局站类型表
CREATE TABLE `cmcc_station_type` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `StationTypeID` int DEFAULT NULL COMMENT '局站类型',
  `StationTypeName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='移动标准化局站类型';

-- 移动标准化局站类型与Siteweb局站类型映射表
CREATE TABLE `cmcc_station_type_map` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `StationTypeID` int DEFAULT NULL,
  `StationCategoryID` int DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='移动标准化局站类型与Siteweb局站类型映射';

-- cmcc设备拓展表
CREATE TABLE `cmcc_device_ext` (
  `DeviceGuid` bigint NOT NULL COMMENT 'hub设备id',
  `DeviceTypeID` VARCHAR(45) DEFAULT NULL COMMENT 'CMCC设备大类id',
  `DeviceSubTypeID` VARCHAR(45) DEFAULT NULL COMMENT 'CMCC设备子类id',
  `EquipmentCategoryID` int DEFAULT NULL COMMENT 'siteweb设备种类id',
  PRIMARY KEY (`DeviceGuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='cmcc设备拓展表';

-- cmcc机房映射表
CREATE TABLE `cmcc_room_map` (
  `CmccSiteID` varchar(128) DEFAULT NULL COMMENT 'CMCC站点id',
  `CmccRoomID` varchar(128) NOT NULL COMMENT 'CMCC机房id',
  `CmccRoomName` varchar(256) DEFAULT NULL COMMENT 'CMCC机房名称',
  `CmccRoomType` int DEFAULT NULL COMMENT 'CMCC机房类型',
  `StationID` int DEFAULT NULL COMMENT 'SiteWeb局站id',
  `HouseID` int DEFAULT NULL COMMENT 'SiteWeb机房id',
  PRIMARY KEY (`CmccRoomID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='cmcc机房映射表';

-- cmcc站点映射表
CREATE TABLE `cmcc_site_map` (
  `CmccSiteID` varchar(128) NOT NULL COMMENT 'cmcc站点id',
  `CmccSiteName` varchar(256) DEFAULT NULL COMMENT 'cmcc站点名称',
  `CmccSiteType` int DEFAULT NULL COMMENT 'cmcc站点类型（对应局站类型）',
  `StationID` int DEFAULT NULL COMMENT 'siteweb局站id',
  `StationCategoryID` int DEFAULT NULL COMMENT 'siteweb局站类型',
  PRIMARY KEY (`CmccSiteID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='cmcc站点映射表';
