CREATE TABLE tcs_alarm_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INT NOT NULL,
  AlarmId BIGINT NOT NULL,
  NorthEventId INT NOT NULL,
  NorthEventConditionId INT NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE COMMENT '逻辑删除标志',
  CONSTRAINT pk_tcs_alarm_map PRIMARY KEY (DeviceId, NorthEquipmentId, AlarmId, NorthEventId, NorthEventConditionId)
) STORAGE(ON "MAIN", CLUSTERBTR) COMMENT '告警映射表';

CREATE TABLE tcs_control_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INT DEFAULT NULL,
  ControlId BIGINT NOT NULL,
  NorthControlId INT NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE COMMENT '逻辑删除标志',
  CONSTRAINT pk_tcs_control_map PRIMARY KEY (DeviceId, ControlId, NorthControlId)
) STORAGE(ON "MAIN", CLUSTERBTR) COMMENT '控制映射表';

CREATE TABLE tcs_device_map (
  GatewayId BIGINT NOT NULL,
  NorthMonitorUnitId INT DEFAULT NULL,
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INT NOT NULL,
  NorthEquipmentTemplateId INT NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE COMMENT '逻辑删除标志',
  CONSTRAINT pk_tcs_device_map PRIMARY KEY (GatewayId, DeviceId, NorthEquipmentId)
) STORAGE(ON "MAIN", CLUSTERBTR) COMMENT '设备映射表';

CREATE TABLE tcs_gateway_map (
  GatewayId BIGINT NOT NULL COMMENT 'hub全局网关id',
  NorthMonitorUnitId INT NOT NULL COMMENT 'siteweb采集单元id',
  NorthStationId INT NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE COMMENT '逻辑删除标志',
  CONSTRAINT pk_tcs_gateway_map PRIMARY KEY (GatewayId, NorthMonitorUnitId)
) STORAGE(ON "MAIN", CLUSTERBTR) COMMENT '网关映射表';

CREATE TABLE tcs_signal_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INT DEFAULT NULL,
  SignalId BIGINT NOT NULL,
  NorthSignalId INT NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE COMMENT '逻辑删除标志',
  CONSTRAINT pk_tcs_signal_map PRIMARY KEY (DeviceId, SignalId, NorthSignalId)
) STORAGE(ON "MAIN", CLUSTERBTR) COMMENT '信号映射表';