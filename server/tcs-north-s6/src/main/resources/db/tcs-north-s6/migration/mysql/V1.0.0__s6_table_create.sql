CREATE TABLE tcs_alarm_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INT NOT NULL,
  AlarmId BIGINT NOT NULL,
  NorthEventId INT NOT NULL,
  NorthEventConditionId INT NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标志',
  PRIMARY KEY (DeviceId, NorthEquipmentId, AlarmId, NorthEventId, NorthEventConditionId)
) COMMENT '告警映射表';

CREATE TABLE tcs_control_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INT DEFAULT NULL,
  ControlId BIGINT NOT NULL,
  NorthControlId INT NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标志',
  PRIMARY KEY (DeviceId, ControlId, NorthControlId)
) COMMENT '控制映射表';

CREATE TABLE tcs_device_map (
  GatewayId BIGINT NOT NULL,
  NorthMonitorUnitId INT DEFAULT NULL,
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INT NOT NULL,
  NorthEquipmentTemplateId INT NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标志',
  PRIMARY KEY (GatewayId, DeviceId, NorthEquipmentId)
) COMMENT '设备映射表';

CREATE TABLE tcs_gateway_map (
  GatewayId BIGINT NOT NULL COMMENT 'hub全局网关id',
  NorthMonitorUnitId INT NOT NULL COMMENT 'siteweb采集单元id',
  NorthStationId INT NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标志',
  PRIMARY KEY (GatewayId, NorthMonitorUnitId)
) COMMENT '网关映射表';

CREATE TABLE tcs_signal_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INT DEFAULT NULL,
  SignalId BIGINT NOT NULL,
  NorthSignalId INT NOT NULL,
  Deleted BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标志',
  PRIMARY KEY (DeviceId, SignalId, NorthSignalId)
) COMMENT '信号映射表';