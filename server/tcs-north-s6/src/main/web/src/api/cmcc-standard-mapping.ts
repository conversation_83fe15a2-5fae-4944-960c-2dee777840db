import { http } from "@/utils/http";

export interface DeviceTypeMapping {
  deviceTypeId: number;
  deviceTypeName: string;
  deviceSubTypeId: number;
  deviceSubTypeName: string;
  sitewebEquipmentCategoryId?: string; // 映射的siteweb设备类型ID（一对一）
  sitewebEquipmentCategoryName?: string; // 映射的siteweb设备类型名称
  isAutoCreated?: boolean; // 是否为自动创建的siteweb类型
  mappingId?: number;
}

export interface StationTypeMapping {
  stationTypeId: number;
  stationTypeName: string;
  stationCategoryId?: number;
  stationCategoryName?: string;
  mappingId?: number;
}

export interface SiteWebEquipmentCategory {
  itemId: string;
  itemValue: string;
  isAutoCreated?: boolean; // 是否为自动创建
  cmccMappingCount?: number; // 关联的CMCC类型数量
}

export interface SiteWebStationCategory {
  itemId: string,
  itemValue: string
}

export interface DeviceStandardData {
  deviceTypeMappings: DeviceTypeMapping[];
  siteWebEquipmentCategories: SiteWebEquipmentCategory[];
}

export interface StationStandardData {
  stationTypeMappings: StationTypeMapping[];
  siteWebStationCategories: SiteWebStationCategory[];
}

export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

/**
 * 获取设备类型标准化数据
 */
export const getDeviceStandardData = () => {
  return http.request<ApiResponse<DeviceTypeMapping[]>>(
    "get",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/device-standard-data"
  );
};

/**
 * 获取局站类型标准化数据
 */
export const getStationStandardData = () => {
  return http.request<ApiResponse<StationTypeMapping[]>>(
    "get",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/station-standard-data"
  );
};

/**
 * 获取移动设备类型
 */
export const fetchCmccDeviceTypes = () => {
  return http.request<ApiResponse<any[]>>(
    "get",
    "/api/thing/south-cmcc-plugin/standard/standard-device-type"
  );
};

/**
 * 获取移动局站类型
 */
export const fetchCmccStationTypes = () => {
  return http.request<ApiResponse<string>>(
    "get",
    "/api/thing/south-cmcc-plugin/standard/standard-station-type"
  );
};

/**
 * 保存设备类型映射（单对一，旧）
 */
export const saveDeviceMapping = (mappings: DeviceTypeMapping[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/cmcc-standard-mapping/save-device-mapping",
    { data: mappings }
  );
};

/**
 * 保存局站类型映射
 */
export const saveStationMapping = (mappings: StationTypeMapping[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/cmcc-standard-mapping/save-station-mapping",
    { data: mappings }
  );
};

/**
 * 获取SiteWeb设备类型
 */
export const getSiteWebEquipmentCategories = () => {
  return http.request<ApiResponse<SiteWebEquipmentCategory[]>>(
    "get",
    "/api/thing/tcs-north-s6/siteweb-standard/equipment-category"
  );
};

/**
 * 获取SiteWeb局站类型
 */
export const getSiteWebStationCategories = () => {
  return http.request<ApiResponse<SiteWebStationCategory[]>>(
    "get",
    "/api/thing/tcs-north-s6/siteweb-standard/station-category"
  );
};

/**
 * 持久化移动设备类型数据
 */
export const insertCmccDeviceTypes = (deviceTypes: any[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/insert-cmcc-device-types",
    { data: deviceTypes }
  );
};

/**
 * 持久化移动局站类型数据
 */
export const insertCmccStationTypes = (stationTypes: any[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/insert-cmcc-station-types",
    { data: stationTypes }
  );
};

/**
 * 保存单个设备类型映射（一对一）
 */
export const saveDeviceMappingOneToOne = (mapping: {
  deviceTypeId: number;
  deviceSubTypeId: number;
  sitewebEquipmentCategoryId: string;
}) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/save-device-mapping-one-to-one",
    { data: mapping }
  );
};

/**
 * 自动创建siteweb设备类型并建立映射
 */
export const autoCreateAndMapDeviceType = (request: {
  deviceTypeId: number;
  deviceSubTypeId: number;
  deviceTypeName: string;
  deviceSubTypeName: string;
}) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/auto-create-and-map",
    { data: request }
  );
};

/**
 * 批量自动创建未映射的设备类型
 */
export const batchAutoCreateUnmappedDevices = () => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/batch-auto-create-unmapped"
  );
};

/**
 * 清理孤立的siteweb设备类型
 */
export const cleanupOrphanedSitewebTypes = (sitewebEquipmentCategoryIds: string[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/cleanup-orphaned-siteweb-types",
    { data: { sitewebEquipmentCategoryIds } }
  );
};

/**
 * 获取未映射的CMCC设备类型
 */
export const getUnmappedCmccDeviceTypes = () => {
  return http.request<ApiResponse<DeviceTypeMapping[]>>(
    "get",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/unmapped-cmcc-device-types"
  );
};

/**
 * 获取孤立的siteweb设备类型（自动创建但无映射的）
 */
export const getOrphanedSitewebTypes = () => {
  return http.request<ApiResponse<SiteWebEquipmentCategory[]>>(
    "get",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/orphaned-siteweb-types"
  );
};

