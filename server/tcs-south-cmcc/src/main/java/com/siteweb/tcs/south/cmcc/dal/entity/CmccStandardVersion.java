package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * CMCC标准版本实体类
 * 对应表：cmcc_standard_version
 */
@Data
@TableName("cmcc_standard_version")
public class CmccStandardVersion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标准版本ID
     */
    @TableId(value = "idcmcc_standard_version", type = IdType.INPUT)
    private Integer idcmccStandardVersion;
} 