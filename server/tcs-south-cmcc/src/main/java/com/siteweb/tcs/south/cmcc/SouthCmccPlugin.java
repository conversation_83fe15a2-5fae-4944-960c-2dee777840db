package com.siteweb.tcs.south.cmcc;


import ch.qos.logback.classic.Level;
import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.common.util.AviatorHelper;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.domain.v2.letter.LifeCycleEvent;
import com.siteweb.tcs.hub.domain.v2.process.lifecycle.GatewayPipelineProxy;
import com.siteweb.tcs.hub.service.ITcsGatewayService;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.plugin.common.SouthPlugin;
import com.siteweb.tcs.south.cmcc.util.scripts.MobilePkNameFunction;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.nio.charset.Charset;

/**
 * 插件主类
 * <p>
 * 中国移动南向接入插件的入口类，负责插件的生命周期管理
 * </p>
 */
@Slf4j
public class SouthCmccPlugin extends SouthPlugin {

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Autowired
    private ServiceRegistry serviceRegistry;

    @Autowired
    private CMCCWebService webService;

    @Autowired
    private FsuLauncher fsuLaunchService;

    @Autowired
    private ITcsGatewayService gatewayService;


    public SouthCmccPlugin(PluginContext context) {
        super(context);
    }

    @Override
    public void onStart() {
        try {
//            try {
//                context.getPluginWrapper().setLogLevel(Level.ERROR);
//                String str = FileUtil.readString("C:\\sql\\tcs2\\server\\tcs-core\\src\\main\\resources\\test.json", Charset.forName("utf-8"));
//                // 使用Jackson ObjectMapper替代Hutool JSONUtil来处理JsonNode类型
//                ObjectMapper objectMapper = new ObjectMapper();
//                GatewayConfigChangeDto configChangeDto = objectMapper.readValue(str, GatewayConfigChangeDto.class);
//                ConfigChangeResult configChangeResult = gatewayService.handleGatewayConfigChange(configChangeDto);
//                GatewayConfigChangeDto gatewayConfigChangeDto = (GatewayConfigChangeDto) configChangeResult.getConfigData();
//                ActorRef pipeline = ClusterContext.getActorSystem().actorOf(Props.create(GatewayPipelineProxy.class, gatewayConfigChangeDto.getId()));
//                LifeCycleEvent lifeCycleEvent = new LifeCycleEvent();
//                lifeCycleEvent.setLifeCycleEventEnum(LifeCycleEventEnum.CREATE);
//                lifeCycleEvent.setThingId(gatewayConfigChangeDto.getId());
//                lifeCycleEvent.setChangeObject(gatewayConfigChangeDto);
//                lifeCycleEvent.setThingType(ThingType.GATEWAY);
//                pipeline.tell(lifeCycleEvent,ActorRef.noSender());
//            } catch (Exception e) {
//                log.error("处理网关配置变更失败", e);
//            }
            log.info("Starting SouthCmccPlugin");
            super.onStart();
            AviatorHelper.addFunction(new MobilePkNameFunction());
            // 启动所有已存在FSU
            fsuLaunchService.launchAll();
            // 启动Http服务器
            webService.start();
            log.info("SouthCmccPlugin started successfully");
        } catch (Exception e) {
            log.error("The SouthCmccPlugin starts abnormally", e);
        }
    }


    @Override
    public void onStop() {
        log.info("Stopping SouthCmccPlugin");
        try {
            webService.stop();
            // 移除资源引用，避免影响其他插件
            AviatorHelper.removeFunction(new MobilePkNameFunction());
            serviceRegistry.cleanupPluginReferences(getPluginId());
        } catch (Exception e) {
            log.warn("Failed to remove resource reference for {}: {}", dbResourceId, e.getMessage());
        } finally {
            super.onStop();
        }
    }


}