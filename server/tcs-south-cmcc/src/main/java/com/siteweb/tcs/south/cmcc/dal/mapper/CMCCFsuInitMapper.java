package com.siteweb.tcs.south.cmcc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsuInit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CMCC FSU初始化配置 Mapper
 * CMCC FSU Init Mapper Interface
 */
@Mapper
@Repository
public interface CMCCFsuInitMapper extends BaseMapper<CMCCFsuInit> {

    /**
     * 根据联合主键查询FSU配置信息
     * @param fsuId FSU唯一标识
     * @param siteWebMuId SiteWeb监控单元ID
     * @return FSU配置信息
     */
    CMCCFsuInit selectByFsuIdAndSiteWebMuId(@Param("fsuId") String fsuId, @Param("siteWebMuId") Integer siteWebMuId);

    /**
     * 根据FSU ID查询FSU配置信息
     * @param fsuId FSU唯一标识
     * @return FSU配置信息
     */
    CMCCFsuInit selectByFsuId(@Param("fsuId") String fsuId);

    /**
     * 根据SiteWeb监控单元ID查询FSU配置列表
     * @param siteWebMuId SiteWeb监控单元ID
     * @return FSU配置列表
     */
    List<CMCCFsuInit> selectBySiteWebMuId(@Param("siteWebMuId") Integer siteWebMuId);

    /**
     * 根据房间ID查询FSU列表
     * @param roomId 房间ID
     * @return FSU列表
     */
    List<CMCCFsuInit> selectByRoomId(@Param("roomId") String roomId);

    /**
     * 根据站点ID查询FSU列表
     * @param siteId 站点ID
     * @return FSU列表
     */
    List<CMCCFsuInit> selectBySiteId(@Param("siteId") String siteId);

    /**
     * 根据FSU类型查询FSU列表
     * @param fsuType FSU类型
     * @return FSU列表
     */
    List<CMCCFsuInit> selectByFsuType(@Param("fsuType") Integer fsuType);

    /**
     * 根据平台编号查询FSU列表
     * @param platFormNo 平台编号
     * @return FSU列表
     */
    List<CMCCFsuInit> selectByPlatFormNo(@Param("platFormNo") Integer platFormNo);

    /**
     * 根据设备名称模糊查询FSU列表
     * @param deviceName 设备名称
     * @return FSU列表
     */
    List<CMCCFsuInit> selectByDeviceNameLike(@Param("deviceName") String deviceName);

    /**
     * 根据Type76设备ID查询FSU配置
     * @param type76DeviceId Type76设备ID
     * @return FSU配置信息
     */
    CMCCFsuInit selectByType76DeviceId(@Param("type76DeviceId") String type76DeviceId);

    /**
     * 批量插入FSU配置信息
     * @param fsuList FSU配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<CMCCFsuInit> fsuList);

    /**
     * 根据条件查询FSU列表
     * @param roomId 房间ID (可选)
     * @param siteId 站点ID (可选)
     * @param fsuType FSU类型 (可选)
     * @return FSU列表
     */
    List<CMCCFsuInit> selectByCondition(@Param("roomId") String roomId, 
                                        @Param("siteId") String siteId, 
                                        @Param("fsuType") Integer fsuType);
}
