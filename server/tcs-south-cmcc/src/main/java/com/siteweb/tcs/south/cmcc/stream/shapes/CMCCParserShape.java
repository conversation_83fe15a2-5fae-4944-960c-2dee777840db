package com.siteweb.tcs.south.cmcc.stream.shapes;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.enums.Inlet;
import com.siteweb.stream.common.enums.Outlet;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBRawMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBRequestMessage;
import com.siteweb.tcs.south.cmcc.stream.options.MobileParserShapeOption;
import com.siteweb.tcs.south.cmcc.util.MobileBMessageFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * 原始报文解析器
 *
 * <AUTHOR> (2025-05-08)
 **/
@Slf4j
@Shape(type = "cmcc-package-parser")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeColor(bkColor = "#c0edc0")
//@ShapeDefaultOptions(SwitchDefaultOption.class)
@ShapeInlet(id = Inlet.Inlet1, type = MobileBRawMessage.class)
@ShapeOutlet(id = Outlet.Outlet1, type = MobileBRequestMessage.class)
public class CMCCParserShape extends AbstractShape {

    @Recoverable
    private MobileParserShapeOption option;


    public CMCCParserShape(ShapeRuntimeContext context) {
        super(context);

    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof MobileParserShapeOption sendDataShapeOption) {
            option = sendDataShapeOption;
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {
        if (in instanceof MobileBRawMessage rawMessage) {
            try {
                var message = MobileBMessageFactory.parseMobileBMessage(rawMessage);
                message.setMsgId(rawMessage.getMsgId());
                message.setRawMessage(rawMessage);
                context.getOutLet(Outlet.Outlet1).broadcast(message, self());
            } catch (Exception ex) {
                log.error("", ex);
            }
        }
    }


    @Override
    protected void onStart() {
        context.subscribeEntryMessage(self());
        super.onStart();
    }

    @Override
    protected void onStop() {
        context.unSubscribeEntryMessage(self());
        super.onStop();
    }
}
