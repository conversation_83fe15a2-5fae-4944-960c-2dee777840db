package com.siteweb.tcs.south.cmcc.dal.provider;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.tcs.common.runtime.PluginI18nMessageSource;
import com.siteweb.tcs.hub.util.CurrentUserUtil;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCOperationLog;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationObject;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationType;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCOperationLogMapper;
import com.siteweb.tcs.south.cmcc.web.dto.OperationLogQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> (2025-08-04)
 **/
@Slf4j
@Service
public class OperationLogProvider {


    @Autowired
    private PluginI18nMessageSource messageSource;


    @Autowired
    private CMCCOperationLogMapper cmccOperationLogMapper;

    /**
     * 记录操作日志
     *
     * @param objectType      操作对象类型
     * @param opType          操作类型
     * @param objectId        FSUID
     * @param i18nDescription I18N说明
     * @param params          i18n format 参数
     */
    public void record(OperationObject objectType, String objectId, OperationType opType, String i18nDescription, Object... params) {
        try {
            CMCCOperationLog log = new CMCCOperationLog();
            log.setId(null);
            log.setTime(LocalDateTime.now());
            log.setObjectId(objectId);
            log.setOperationType(opType);
            log.setObjectType(objectType);
            var userName = CurrentUserUtil.getCurrentUserName();
            if (userName == null || userName.isEmpty()) userName = "System";
            log.setUserId(CurrentUserUtil.getCurrentUserId());
            log.setUserName(userName);
            var description = messageSource.getMessage(i18nDescription, params);
            log.setDescription(description);
            cmccOperationLogMapper.insert(log);
        } catch (Exception ex) {
            log.error(" operation log record failure :", ex);
        }
    }


    public List<CMCCOperationLog> find(OperationLogQueryDTO queryDTO) {
        // 可选：添加查询条件
        QueryWrapper<CMCCOperationLog> queryWrapper = new QueryWrapper<>();
        if (queryDTO.getOperationType() != null) {
            queryWrapper.eq("op_type", queryDTO.getOperationType().name());
        }
        if (queryDTO.getObjectType() != null && queryDTO.getObjectId() != null) {
            queryWrapper.eq("object_type", queryDTO.getObjectType().name());
            queryWrapper.eq("object_id", queryDTO.getObjectId());
        }
        if (queryDTO.getStartTime() != null) {
            queryWrapper.ge("op_time", queryDTO.getStartTime());
        }
        if (queryDTO.getEndTime() != null) {
            queryWrapper.le("op_time", queryDTO.getEndTime());
        }
        return cmccOperationLogMapper.selectList(queryWrapper);
    }


}
