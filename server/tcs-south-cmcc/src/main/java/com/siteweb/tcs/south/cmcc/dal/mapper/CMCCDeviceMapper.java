package com.siteweb.tcs.south.cmcc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> (2025-05-12)
 **/

@Mapper
public interface CMCCDeviceMapper extends BaseMapper<CMCCDevice> {


    int insertBatch(@Param("list") List<CMCCDevice> deviceList);

    int updateBatch(@Param("list") List<CMCCDevice> deviceList);

    List<CMCCDevice> selectDevicesByFsu(String fsuId);


    int deleteBatchByDevices(@Param("list") List<String> deviceIdList);


}
