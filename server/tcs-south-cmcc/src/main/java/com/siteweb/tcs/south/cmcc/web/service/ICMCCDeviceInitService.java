package com.siteweb.tcs.south.cmcc.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDeviceInit;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCDeviceInitDTO;

import java.util.List;

/**
 * CMCC设备初始化服务接口
 * CMCC Device Init Service Interface
 */
public interface ICMCCDeviceInitService extends IService<CMCCDeviceInit> {

    /**
     * 根据设备ID和SiteWeb设备ID查询设备信息
     * @param deviceId CMCC设备ID
     * @param siteWebEquipId SiteWeb设备ID
     * @return 设备信息
     */
    CMCCDeviceInit getByDeviceIdAndSiteWebEquipId(Long deviceId, Integer siteWebEquipId);

    /**
     * 根据机房ID查询设备列表
     * @param roomId 机房ID
     * @return 设备列表
     */
    List<CMCCDeviceInit> listByRoomId(Long roomId);

    /**
     * 根据设备类型查询设备列表
     * @param deviceType 设备类型
     * @return 设备列表
     */
    List<CMCCDeviceInit> listByDeviceType(Integer deviceType);

    /**
     * 根据设备名称模糊查询设备列表
     * @param deviceName 设备名称
     * @return 设备列表
     */
    List<CMCCDeviceInit> listByDeviceNameLike(String deviceName);

    /**
     * 根据SiteWeb设备ID查询设备信息
     * @param siteWebEquipId SiteWeb设备ID
     * @return 设备信息
     */
    CMCCDeviceInit getBySiteWebEquipId(Integer siteWebEquipId);

    /**
     * 根据SiteWeb监控单元ID查询设备列表（返回DTO）
     * @param siteWebMuId SiteWeb监控单元ID
     * @return 设备DTO列表
     */
    List<CMCCDeviceInitDTO> listDTOBySiteWebMuId(Integer siteWebMuId);

    List<CMCCDeviceInit> listBySiteWebMuId(Integer siteWebMuId);

    /**
     * 批量保存设备信息
     * @param deviceList 设备列表
     * @return 保存是否成功
     */
    boolean saveBatch(List<CMCCDeviceInit> deviceList);

    /**
     * 根据设备ID和SiteWeb设备ID删除设备信息
     * @param deviceId CMCC设备ID
     * @param siteWebEquipId SiteWeb设备ID
     * @return 删除是否成功
     */
    boolean removeByDeviceIdAndSiteWebEquipId(Long deviceId, Integer siteWebEquipId);

    /**
     * 根据SiteWeb设备ID删除设备信息
     * @param siteWebEquipId SiteWeb设备ID
     * @return 删除是否成功
     */
    boolean removeBySiteWebEquipId(Integer siteWebEquipId);

    /**
     * 保存或更新设备信息
     * @param deviceDto 设备DTO
     * @return 保存是否成功
     */
    boolean saveOrUpdateDevice(CMCCDeviceInitDTO deviceDto);
}
