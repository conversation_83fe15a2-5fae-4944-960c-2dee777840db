package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 移动B接口主动请求报文
 *
 * <AUTHOR> (2025-05-09)
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "Request")
//@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
public abstract class MobileBRequestMessage extends MobileBMessage {

    public MobileBRequestMessage() {
    }

    public MobileBRequestMessage(PK_TypeName pkType) {
        getPkType().setName(pkType);
    }

    /**
     * 响应Actor
     */
    @JsonIgnore
    private MobileBRawMessage rawMessage;
}
