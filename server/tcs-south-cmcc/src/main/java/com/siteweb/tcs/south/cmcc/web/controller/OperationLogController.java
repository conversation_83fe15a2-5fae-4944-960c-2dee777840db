package com.siteweb.tcs.south.cmcc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.provider.OperationLogProvider;
import com.siteweb.tcs.south.cmcc.web.dto.OperationLogQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (2025-08-04)
 **/
@Slf4j
@RestController
@RequestMapping("api/cmcc/2016/operation-log/")
public class OperationLogController {

    @Autowired
    private OperationLogProvider operationLogProvider;


    @PostMapping(value = "list", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> backupFileList(@RequestBody OperationLogQueryDTO queryDTO) {
        log.info("获取操作日志列表");
        return ResponseHelper.successful(operationLogProvider.find(queryDTO));
    }
}
