package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumResult;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 重启FSU响应报文
 * 
 * 根据中国移动B接口技术规范5.6.14章节实现
 * FSU向SC返回重启操作的执行结果
 * 当Result取值为1时，FailureCause取值为"NULL"
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Response")
public class RestartFsuAckMessage extends MobileBResponseMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public RestartFsuAckMessage() {
        super(PK_TypeName.SET_FSUREBOOT_ACK);
    }

    @Setter
    @Getter
    public static class Info extends StandardResponseInfo {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

        /**
         * 执行结果
         */
        @JsonProperty("Result")
        @JacksonXmlProperty(localName = "Result")
        private EnumResult result;

        /**
         * 失败原因（当Result为FAILURE时）
         * 重启FSU失败的原因（厂家自定义）
         * 当Result取值为1时，FailureCause取值为"NULL"
         */
        @JsonProperty("FailureCause")
        @JacksonXmlProperty(localName = "FailureCause")
        private String failureCause;
    }
}
