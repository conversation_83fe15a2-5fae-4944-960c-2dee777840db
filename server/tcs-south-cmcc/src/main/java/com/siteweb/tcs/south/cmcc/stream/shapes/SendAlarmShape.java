package com.siteweb.tcs.south.cmcc.stream.shapes;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.enums.Inlet;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceAlarmChange;
import com.siteweb.tcs.south.cmcc.connector.letter.SendAlarmMessage;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.stream.options.SendAlarmShapeOption;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.pekko.actor.ActorRef;

import java.util.List;
import java.util.Map;

/**
 * 上报告警信息处理Shape
 * 优化版本：使用AlarmDataConverter实现高效的告警转换
 * <p>
 * 根据中国移动B接口技术规范5.6.2章节实现
 * 负责处理从FSU发送到SC的告警信息，包括：
 * 1. 接收并解析告警数据
 * 2. 验证告警信息格式
 * 3. 转发告警到下游系统
 * 4. 返回确认响应
 *
 * <AUTHOR> from CMCC B Interface Specification 5.6.2
 */
@Shape(type = "cmcc-send-alarm")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-alarm")
@ShapeAuthor("Siteweb")
@ShapeColor(bkColor = "#ffcccc")
@ShapeInlet(id = Inlet.Inlet1, type = SendAlarmMessage.class)
public class SendAlarmShape extends AbstractShape {
    private static final Logger log = LoggerFactory.getLogger(SendAlarmShape.class);
    private final ActorRef pipeline;
    private final String fsuId;
    private SendAlarmShapeOption options;
    private final CMCCFsu gatewayInfo;
    
    // 告警数据转换器
    private final AlarmDataConverter alarmDataConverter;

    public SendAlarmShape(ShapeRuntimeContext context) {
        super(context);
        pipeline = (ActorRef) context.getGraphOptions("pipelineActorRef");
        fsuId = (String) context.getGraphOptions("fsuId");
        this.options = new SendAlarmShapeOption();
        this.gatewayInfo = (CMCCFsu) context.getGraphOptions("gatewayInfo");
        
        // 初始化告警数据转换器
        this.alarmDataConverter = new AlarmDataConverter(fsuId, gatewayInfo);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof SendAlarmShapeOption alarmOptions) {
            this.options = alarmOptions;
            log.info("Updated SendAlarmShape options: FSU={}, LogLevel={}",
                    alarmOptions.getFsuId(),
                    alarmOptions.getLogging().getLogLevel());
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {
        try {
            if (options.getLogging().isEnableDetailedLogging()) {
                log.debug("Processing alarm message: {}", in);
            }
            if (in instanceof SendAlarmMessage alarmMessage) {
                processAlarmMessage(alarmMessage);
            }
        } catch (Exception e) {
            log.error("Error processing alarm message", e);
            // 发送失败响应
        }
    }

    /**
     * 处理标准告警消息
     * 优化版本：使用AlarmDataConverter实现高效的告警到DeviceAlarmChange转换
     */
    private void processAlarmMessage(SendAlarmMessage alarmMessage) {
        String messageFsuId = alarmMessage.getInfo().getFsuId();
        log.info("Processing alarm message from FSU: {}, alarm count: {}", 
                messageFsuId, alarmMessage.getAlarmCount());
        
        try {
            // 使用告警数据转换器批量处理告警数据
            List<DeviceAlarmChange> deviceAlarmChanges = alarmDataConverter.convertAlarms(
                alarmMessage.getInfo().getValues().getAlarmList());
            
            // 发送处理后的数据到pipeline
            for (DeviceAlarmChange deviceAlarmChange : deviceAlarmChanges) {
                if (deviceAlarmChange != null && deviceAlarmChange.getAlarmChangeList() != null 
                    && !deviceAlarmChange.getAlarmChangeList().isEmpty()) {
                    pipeline.tell(deviceAlarmChange, self());
                }
            }
            
            log.info("Successfully processed {} device alarm changes from FSU: {}", 
                deviceAlarmChanges.size(), messageFsuId);
                
        } catch (Exception e) {
            log.error("Error processing alarm message from FSU: {}", messageFsuId, e);
        }
    }
}