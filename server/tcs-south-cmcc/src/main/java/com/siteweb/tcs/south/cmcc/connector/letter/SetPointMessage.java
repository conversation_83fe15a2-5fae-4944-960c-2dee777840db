package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import com.siteweb.tcs.south.cmcc.connector.protocol.TSemaphore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2025-07-28)
 **/

@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class SetPointMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public SetPointMessage() {
        super(PK_TypeName.SET_POINT);
    }


    @Setter
    @Getter
    public static class Info {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;


        /**
         * 请求设备列表
         */
        @JsonProperty("Value")
        @JacksonXmlProperty(localName = "Value")
        private InfoValue value = new InfoValue();

    }

    @Data
    public static class InfoValue {
        /**
         * 请求设备列表
         */
        @JacksonXmlElementWrapper(localName = "DeviceList")
        @JacksonXmlProperty(localName = "Device")
        private List<Device> devices = new ArrayList<>();
    }


    @Setter
    @Getter
    public static class Device {
        @JsonProperty("ID")
        @JacksonXmlProperty(localName = "ID", isAttribute = true)
        private String id;

        @JsonProperty("TSemaphore")
        @JacksonXmlProperty(localName = "TSemaphore")
        @JacksonXmlElementWrapper(useWrapping = false)
        private List<TSemaphore> points = new ArrayList<TSemaphore>();


    }


}
