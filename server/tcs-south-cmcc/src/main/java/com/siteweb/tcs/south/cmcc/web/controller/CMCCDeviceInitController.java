package com.siteweb.tcs.south.cmcc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDeviceInit;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCDeviceInitDTO;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCDeviceInitService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CMCC设备初始化控制器
 * CMCC Device Init Controller
 */
@RestController
@RequestMapping("/device-init")
public class CMCCDeviceInitController {

    private static final Logger log = LoggerFactory.getLogger(CMCCDeviceInitController.class);

    @Autowired
    private ICMCCDeviceInitService cmccDeviceInitService;

    /**
     * 获取所有设备列表
     * @return 设备列表
     */
    @GetMapping(value = "/list")
    public ResponseEntity<ResponseResult> listDevices() {
        try {
            List<CMCCDeviceInit> devices = cmccDeviceInitService.list();
            return ResponseHelper.successful(devices);
        } catch (Exception e) {
            log.error("Error listing devices: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 根据设备ID和SiteWeb设备ID获取设备信息
     * @param deviceId CMCC设备ID
     * @param siteWebEquipId SiteWeb设备ID
     * @return 设备信息
     */
    @GetMapping(value = "/get")
    public ResponseEntity<ResponseResult> getDevice(@RequestParam Long deviceId,
                                                    @RequestParam Integer siteWebEquipId) {
        try {
            CMCCDeviceInit device = cmccDeviceInitService.getByDeviceIdAndSiteWebEquipId(deviceId, siteWebEquipId);
            return ResponseHelper.successful(device);
        } catch (Exception e) {
            log.error("Error getting device: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 根据SiteWeb设备ID获取设备信息
     * @param siteWebEquipId SiteWeb设备ID
     * @return 设备信息
     */
    @GetMapping(value = "/get-by-siteweb/{siteWebEquipId}")
    public ResponseEntity<ResponseResult> getDeviceBySiteWebEquipId(@PathVariable Integer siteWebEquipId) {
        try {
            CMCCDeviceInit device = cmccDeviceInitService.getBySiteWebEquipId(siteWebEquipId);
            return ResponseHelper.successful(device);
        } catch (Exception e) {
            log.error("Error getting device by SiteWeb equipment ID: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }



    /**
     * 根据设备类型获取设备列表
     * @param deviceType 设备类型
     * @return 设备列表
     */
    @GetMapping(value = "/list-by-type/{deviceType}")
    public ResponseEntity<ResponseResult> listDevicesByType(@PathVariable Integer deviceType) {
        try {
            List<CMCCDeviceInit> devices = cmccDeviceInitService.listByDeviceType(deviceType);
            return ResponseHelper.successful(devices);
        } catch (Exception e) {
            log.error("Error listing devices by type: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 根据设备名称模糊查询设备列表
     * @param deviceName 设备名称
     * @return 设备列表
     */
    @GetMapping(value = "/search")
    public ResponseEntity<ResponseResult> searchDevices(@RequestParam String deviceName) {
        try {
            List<CMCCDeviceInit> devices = cmccDeviceInitService.listByDeviceNameLike(deviceName);
            return ResponseHelper.successful(devices);
        } catch (Exception e) {
            log.error("Error searching devices: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 创建设备信息
     * @param device 设备信息
     * @return 创建结果
     */
    @PostMapping(value = "/create")
    public ResponseEntity<ResponseResult> createDevice(@RequestBody CMCCDeviceInit device) {
        try {
            boolean result = cmccDeviceInitService.save(device);
            log.info("Successfully created device: DeviceId={}, SiteWebEquipId={}",
                    device.getDeviceId(), device.getSiteWebEquipId());
            return ResponseHelper.successful(true);
        } catch (Exception e) {
            log.error("Error creating device: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 更新设备信息
     * @param device 设备信息
     * @return 更新结果
     */
    @PutMapping(value = "/update")
    public ResponseEntity<ResponseResult> updateDevice(@RequestBody CMCCDeviceInit device) {
        try {
            boolean result = cmccDeviceInitService.updateById(device);
            log.info("Successfully updated device: DeviceId={}, SiteWebEquipId={}",
                    device.getDeviceId(), device.getSiteWebEquipId());
            return ResponseHelper.successful(true);
        } catch (Exception e) {
            log.error("Error updating device: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 保存或更新设备信息
     * @param deviceDto 设备DTO
     * @return 保存结果
     */
    @PostMapping(value = "/save-or-update")
    public ResponseEntity<ResponseResult> saveOrUpdateDevice(@RequestBody CMCCDeviceInitDTO deviceDto) {
        try {
            boolean result = cmccDeviceInitService.saveOrUpdateDevice(deviceDto);
            return ResponseHelper.successful(result);
        } catch (IllegalArgumentException e) {
            log.warn("设备配置校验失败: {}", e.getMessage());
            return ResponseHelper.failed(e.getMessage());
        } catch (Exception e) {
            log.error("Error saving or updating device: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }


    /**
     * 删除设备信息
     * @param deviceId CMCC设备ID
     * @param siteWebEquipId SiteWeb设备ID
     * @return 删除结果
     */
    @DeleteMapping(value = "/delete")
    public ResponseEntity<ResponseResult> deleteDevice(@RequestParam Long deviceId,
                                               @RequestParam Integer siteWebEquipId) {
        try {
            boolean result = cmccDeviceInitService.removeByDeviceIdAndSiteWebEquipId(deviceId, siteWebEquipId);
            log.info("Successfully deleted device: DeviceId={}, SiteWebEquipId={}", deviceId, siteWebEquipId);
            return ResponseHelper.successful(true);
        } catch (Exception e) {
            log.error("Error deleting device: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 根据SiteWeb设备ID删除设备信息
     * @param siteWebEquipId SiteWeb设备ID
     * @return 删除结果
     */
    @DeleteMapping(value = "/delete-by-siteweb/{siteWebEquipId}")
    public ResponseEntity<ResponseResult> deleteDeviceBySiteWebEquipId(@PathVariable Integer siteWebEquipId) {
        try {
            boolean result = cmccDeviceInitService.removeBySiteWebEquipId(siteWebEquipId);
            log.info("Successfully deleted device by SiteWeb equipment ID: {}", siteWebEquipId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Error deleting device by SiteWeb equipment ID: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }
}
