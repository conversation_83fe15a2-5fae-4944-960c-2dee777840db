package com.siteweb.tcs.south.cmcc.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.file.PathUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.plugin.common.util.FTPHelper;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsuInit;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCFsuInitMapper;
import com.siteweb.tcs.south.cmcc.util.CmbConfigParser;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCDeviceInitDTO;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCFsuInitDTO;
import com.siteweb.tcs.south.cmcc.web.dto.CmccPullInitConfigDTO;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCDeviceInitService;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCFsuInitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.file.Path;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;

/**
 * CMCC FSU初始化配置服务实现类
 * CMCC FSU Init Service Implementation
 */
@Slf4j
@Service
public class CMCCFsuInitServiceImpl extends ServiceImpl<CMCCFsuInitMapper, CMCCFsuInit> implements ICMCCFsuInitService {

    @Resource
    private CMCCFsuInitMapper cmccFsuInitMapper;

    @Resource
    private ICMCCDeviceInitService cmccDeviceInitService;

    @Resource
    private PluginContext pluginContext;

    @Override
    public CMCCFsuInit getByCompositeKey(String fsuId, Integer siteWebMuId) {
        if (fsuId == null || fsuId.trim().isEmpty()) {
            log.warn("FSU ID cannot be null or empty");
            return null;
        }
        if (siteWebMuId == null) {
            log.warn("SiteWeb监控单元ID cannot be null");
            return null;
        }
        return cmccFsuInitMapper.selectByFsuIdAndSiteWebMuId(fsuId.trim(), siteWebMuId);
    }

    @Override
    public CMCCFsuInit getByFsuId(String fsuId) {
        if (fsuId == null || fsuId.trim().isEmpty()) {
            log.warn("FSU ID cannot be null or empty");
            return null;
        }
        return cmccFsuInitMapper.selectByFsuId(fsuId.trim());
    }

    @Override
    public CMCCFsuInit getBySiteWebMuId(Integer siteWebMuId) {
        if (siteWebMuId == null) {
            log.warn("SiteWeb监控单元ID不能为空");
            return null;
        }
        return this.getById(siteWebMuId);
    }

    @Override
    public CMCCFsuInitDTO getFsuWithDevicesBySiteWebMuId(Integer siteWebMuId) {
        if (siteWebMuId == null) {
            log.warn("SiteWeb监控单元ID不能为空");
            return null;
        }
        
        try {
            // 查询FSU配置列表
            List<CMCCFsuInit> fsuInitList = cmccFsuInitMapper.selectBySiteWebMuId(siteWebMuId);
            
            // 查询设备列表
            List<CMCCDeviceInitDTO> deviceList = cmccDeviceInitService.listDTOBySiteWebMuId(siteWebMuId);
            
            CMCCFsuInitDTO result = null;
            
            // 如果找到FSU配置，取第一个作为主FSU
            if (fsuInitList != null && !fsuInitList.isEmpty()) {
                result = convertToDTO(fsuInitList.get(0));
                log.info("成功查询到FSU配置及设备信息: siteWebMuId={}, fsuCount={}, deviceCount={}", 
                        siteWebMuId, fsuInitList.size(), deviceList != null ? deviceList.size() : 0);
            } else {
                log.warn("未找到对应的FSU配置: siteWebMuId={}", siteWebMuId);
                // 即使没有FSU配置，也创建一个空的DTO对象来返回设备信息
                result = new CMCCFsuInitDTO();
                result.setSiteWebMuId(siteWebMuId);
            }
            
            // 设置设备列表到FSU DTO中
            if (result != null) {
                result.setCmccDeviceInitDTOList(deviceList);
                result.setDeviceNum(deviceList != null ? deviceList.size() : 0);
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据SiteWeb监控单元ID查询FSU配置及设备失败: siteWebMuId={}, error={}", siteWebMuId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<CMCCFsuInit> listByRoomId(String roomId) {
        if (roomId == null || roomId.trim().isEmpty()) {
            log.warn("Room ID cannot be null or empty");
            return List.of();
        }
        return cmccFsuInitMapper.selectByRoomId(roomId.trim());
    }

    @Override
    public List<CMCCFsuInit> listBySiteId(String siteId) {
        if (siteId == null || siteId.trim().isEmpty()) {
            log.warn("Site ID cannot be null or empty");
            return List.of();
        }
        return cmccFsuInitMapper.selectBySiteId(siteId.trim());
    }

    @Override
    public List<CMCCFsuInit> listByFsuType(Integer fsuType) {
        if (fsuType == null) {
            log.warn("FSU type cannot be null");
            return List.of();
        }
        return cmccFsuInitMapper.selectByFsuType(fsuType);
    }

    @Override
    public List<CMCCFsuInit> listByPlatFormNo(Integer platFormNo) {
        if (platFormNo == null) {
            log.warn("Platform number cannot be null");
            return List.of();
        }
        return cmccFsuInitMapper.selectByPlatFormNo(platFormNo);
    }

    @Override
    public List<CMCCFsuInit> listByDeviceNameLike(String deviceName) {
        if (deviceName == null || deviceName.trim().isEmpty()) {
            log.warn("Device name cannot be null or empty");
            return List.of();
        }
        return cmccFsuInitMapper.selectByDeviceNameLike(deviceName.trim());
    }

    @Override
    public CMCCFsuInit getByType76DeviceId(String type76DeviceId) {
        if (type76DeviceId == null || type76DeviceId.trim().isEmpty()) {
            log.warn("Type76 Device ID cannot be null or empty");
            return null;
        }
        return cmccFsuInitMapper.selectByType76DeviceId(type76DeviceId.trim());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "DefaultTransactionManager")
    public boolean saveBatch(List<CMCCFsuInit> fsuList) {
        if (fsuList == null || fsuList.isEmpty()) {
            log.warn("FSU list cannot be null or empty");
            return false;
        }
        
        try {
            int result = cmccFsuInitMapper.batchInsert(fsuList);
            if (result > 0) {
                log.info("Successfully batch inserted {} FSU records", result);
                return true;
            } else {
                log.warn("Batch insert failed, no records inserted");
                return false;
            }
        } catch (Exception e) {
            log.error("Error occurred during batch insert: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<CMCCFsuInit> listByCondition(String roomId, String siteId, Integer fsuType) {
        return cmccFsuInitMapper.selectByCondition(roomId, siteId, fsuType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "DefaultTransactionManager")
    public boolean saveOrUpdateFsu(CMCCFsuInitDTO fsuDto) {
        if (fsuDto == null) {
            throw new IllegalArgumentException("FSU配置不能为空");
        }
        
        // 必填字段验证
        if (fsuDto.getFsuId() == null || fsuDto.getFsuId().trim().isEmpty()) {
            throw new IllegalArgumentException("FSU ID不能为空");
        }
        
        if (fsuDto.getFsuPort() == null || fsuDto.getFsuPort() <= 0) {
            throw new IllegalArgumentException("FSU端口不能为空");
        }
        
        if (fsuDto.getLoginPwd() == null || fsuDto.getLoginPwd().trim().isEmpty()) {
            throw new IllegalArgumentException("登录密码不能为空");
        }
        
        if (fsuDto.getLoginUser() == null || fsuDto.getLoginUser().trim().isEmpty()) {
            throw new IllegalArgumentException("登录用户不能为空");
        }
        
        if (fsuDto.getFtpUser() == null || fsuDto.getFtpUser().trim().isEmpty()) {
            throw new IllegalArgumentException("FTP用户不能为空");
        }
        
        if (fsuDto.getFtpPwd() == null || fsuDto.getFtpPwd().trim().isEmpty()) {
            throw new IllegalArgumentException("FTP密码不能为空");
        }
        
        if (fsuDto.getFsuType() == null || fsuDto.getFsuType() <= 0) {
            throw new IllegalArgumentException("机房类型不能为空");
        }

        
        if (fsuDto.getRoomId() == null || fsuDto.getRoomId().trim().isEmpty()) {
            throw new IllegalArgumentException("机房ID不能为空");
        }
        
        if (fsuDto.getRoomName() == null || fsuDto.getRoomName().trim().isEmpty()) {
            throw new IllegalArgumentException("机房名称不能为空");
        }
        
        if (fsuDto.getPlatFormNo() == null || fsuDto.getPlatFormNo() <= 0) {
            throw new IllegalArgumentException("平台数量不能为空");
        }
        
        if (fsuDto.getScSwitchMode() == null || fsuDto.getScSwitchMode() < 0) {
            throw new IllegalArgumentException("SC切换方式不能为空");
        }
        
                try {
            // 以主键 SiteWebMuId 判断是否存在
            CMCCFsuInit existingFsu = null;
            List<CMCCFsuInit> byMuList = cmccFsuInitMapper.selectBySiteWebMuId(fsuDto.getSiteWebMuId());
            if (byMuList != null && !byMuList.isEmpty()) {
                existingFsu = byMuList.get(0);
            }

            // 如果是新增且存在相同 FSUID（全局唯一），则拒绝
            if (existingFsu == null) {
                CMCCFsuInit globalExistingFsu = getByFsuId(fsuDto.getFsuId());
                if (globalExistingFsu != null) {
                    throw new IllegalArgumentException("FSU ID '" + fsuDto.getFsuId() + "' 已存在，无法重复添加");
                }
            }
            
            // 转换DTO为Entity
            CMCCFsuInit fsu = convertToEntity(fsuDto);
            
            boolean result;
            if (existingFsu != null) {
                // 更新现有FSU配置
                result = updateById(fsu);
                if (result) {
                    log.info("Successfully updated FSU: FsuId={}, SiteWebMuId={}", fsu.getFsuId(), fsu.getSiteWebMuId());
                }
            } else {
                // 插入新FSU配置
                result = save(fsu);
                if (result) {
                    log.info("Successfully inserted new FSU: FsuId={}, SiteWebMuId={}", fsu.getFsuId(), fsu.getSiteWebMuId());
                }
            }
            
            return result;
        } catch (IllegalArgumentException e) {
            log.warn("FSU配置校验失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error occurred during save or update FSU: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 将DTO转换为Entity
     */
    private CMCCFsuInit convertToEntity(CMCCFsuInitDTO dto) {
        CMCCFsuInit entity = new CMCCFsuInit();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    @Override
    public CMCCFsuInitDTO pullInitConfig(CmccPullInitConfigDTO pullConfigDTO) throws Exception {
        if (pullConfigDTO == null) {
            throw new IllegalArgumentException("拉取配置参数不能为空");
        }

        if (pullConfigDTO.getFtpHost() == null || pullConfigDTO.getFtpHost().trim().isEmpty()) {
            throw new IllegalArgumentException("FTP主机地址不能为空");
        }

        if (pullConfigDTO.getFtpUsername() == null || pullConfigDTO.getFtpUsername().trim().isEmpty()) {
            throw new IllegalArgumentException("FTP用户名不能为空");
        }

        if (pullConfigDTO.getFtpPassword() == null) {
            throw new IllegalArgumentException("FTP密码不能为空");
        }

        // 构建FTP配置
        FTPHelper.FTPConfig ftpConfig = FTPHelper.FTPConfig.create(
                pullConfigDTO.getFtpHost().trim(),
                pullConfigDTO.getFtpUsername().trim(),
                pullConfigDTO.getFtpPassword()
        );

        if (pullConfigDTO.getFtpPort() != null) {
            ftpConfig.setPort(pullConfigDTO.getFtpPort());
        }
        ftpConfig.setEncoding("gbk");

        FTPClient ftpClient = null;
        try {
            log.info("开始连接FTP服务器: {}:{}", pullConfigDTO.getFtpHost(), pullConfigDTO.getFtpPort());
            
            // 连接FTP服务器
            ftpClient = FTPHelper.connect(ftpConfig);
            
            // 下载配置文件
            String remotePath = "/home/<USER>/cmbcfg/cmb_init_list.ini";
            log.info("开始下载配置文件: {}", remotePath);
            
            String configContent = FTPHelper.downloadFileAsString(ftpClient, remotePath);
            if (configContent == null || configContent.trim().isEmpty()) {
                throw new Exception("配置文件下载失败或文件为空: " + remotePath);
            }
            
            log.info("配置文件下载成功，文件大小: {} 字符", configContent.length());
            
            // 解析配置文件
            CMCCFsuInitDTO fsuInitDTO = CmbConfigParser.parseConfig(configContent);
            
            log.info("配置文件解析成功，FSU ID: {}, 设备数量: {}", 
                    fsuInitDTO.getFsuId(), fsuInitDTO.getDeviceNum());
            
            return fsuInitDTO;
            
        } catch (Exception e) {
            log.error("拉取FSU初始化配置失败: {}", e.getMessage(), e);
            
            // 根据异常类型提供更具体的错误信息
            if (e.getMessage().contains("连接") || e.getMessage().contains("connect")) {
                throw new Exception("FTP连接失败，请检查服务器地址、端口和网络连接: " + e.getMessage());
            } else if (e.getMessage().contains("登录") || e.getMessage().contains("login")) {
                throw new Exception("FTP登录失败，请检查用户名和密码: " + e.getMessage());
            } else if (e.getMessage().contains("下载") || e.getMessage().contains("download")) {
                throw new Exception("配置文件下载失败，请检查文件路径和权限: " + e.getMessage());
            } else if (e.getMessage().contains("解析") || e.getMessage().contains("parse")) {
                throw new Exception("配置文件解析失败，请检查文件格式: " + e.getMessage());
            } else {
                throw new Exception("拉取配置失败: " + e.getMessage());
            }
        } finally {
            // 关闭FTP连接
            if (ftpClient != null) {
                FTPHelper.disconnect(ftpClient);
                log.info("FTP连接已关闭");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "DefaultTransactionManager")
    public boolean overrideConfig(CMCCFsuInitDTO fsuDto) {
        if (fsuDto == null) {
            throw new IllegalArgumentException("FSU配置不能为空");
        }
        
        if (fsuDto.getSiteWebMuId() == null) {
            throw new IllegalArgumentException("SiteWeb监控单元ID不能为空");
        }
        
        try {
            log.info("开始覆盖FSU配置: siteWebMuId={}, fsuId={}", fsuDto.getSiteWebMuId(), fsuDto.getFsuId());
            
            // 1. 删除现有的FSU配置
            List<CMCCFsuInit> existingFsus = cmccFsuInitMapper.selectBySiteWebMuId(fsuDto.getSiteWebMuId());
            if (existingFsus != null && !existingFsus.isEmpty()) {
                for (CMCCFsuInit existingFsu : existingFsus) {
                    removeById(existingFsu.getSiteWebMuId());
                    log.info("删除现有FSU配置: siteWebMuId={}, fsuId={}", existingFsu.getSiteWebMuId(), existingFsu.getFsuId());
                }
            }
            
            // 2. 删除现有的设备配置
            List<CMCCDeviceInitDTO> existingDevices = cmccDeviceInitService.listDTOBySiteWebMuId(fsuDto.getSiteWebMuId());
            if (existingDevices != null && !existingDevices.isEmpty()) {
                for (CMCCDeviceInitDTO existingDevice : existingDevices) {
                    cmccDeviceInitService.removeById(existingDevice.getSiteWebEquipId());
                    log.info("删除现有设备配置: siteWebEquipId={}, deviceId={}", existingDevice.getSiteWebEquipId(), existingDevice.getDeviceId());
                }
            }
            
            // 3. 保存新的FSU配置
            CMCCFsuInit newFsu = convertToEntity(fsuDto);
            boolean fsuSaved = save(newFsu);
            if (!fsuSaved) {
                throw new Exception("保存新FSU配置失败");
            }
            log.info("保存新FSU配置成功: siteWebMuId={}, fsuId={}", newFsu.getSiteWebMuId(), newFsu.getFsuId());
            
            // 4. 保存新的设备配置
            if (fsuDto.getCmccDeviceInitDTOList() != null && !fsuDto.getCmccDeviceInitDTOList().isEmpty()) {
                for (CMCCDeviceInitDTO deviceDto : fsuDto.getCmccDeviceInitDTOList()) {
                    deviceDto.setSiteWebMuId(fsuDto.getSiteWebMuId());
                    boolean deviceSaved = cmccDeviceInitService.saveOrUpdateDevice(deviceDto);
                    if (!deviceSaved) {
                        throw new Exception("保存新设备配置失败: deviceId=" + deviceDto.getDeviceId());
                    }
                    log.info("保存新设备配置成功: siteWebEquipId={}, deviceId={}", deviceDto.getSiteWebEquipId(), deviceDto.getDeviceId());
                }
            }
            
            log.info("FSU配置覆盖完成: siteWebMuId={}, fsuId={}, 设备数量={}", 
                    fsuDto.getSiteWebMuId(), fsuDto.getFsuId(), 
                    fsuDto.getCmccDeviceInitDTOList() != null ? fsuDto.getCmccDeviceInitDTOList().size() : 0);
            
            return true;
            
        } catch (Exception e) {
            log.error("覆盖FSU配置失败: siteWebMuId={}, error={}", fsuDto.getSiteWebMuId(), e.getMessage(), e);
            throw new RuntimeException("覆盖FSU配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将CMCCFsuInit实体转换为CMCCFsuInitDTO
     * @param fsuInit FSU初始化实体
     * @return FSU初始化DTO
     */
    private CMCCFsuInitDTO convertToDTO(CMCCFsuInit fsuInit) {
        if (fsuInit == null) {
            return null;
        }
        
        CMCCFsuInitDTO dto = new CMCCFsuInitDTO();
        BeanUtils.copyProperties(fsuInit, dto);
        return dto;
    }
}
