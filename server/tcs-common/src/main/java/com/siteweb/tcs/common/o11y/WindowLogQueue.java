package com.siteweb.tcs.common.o11y;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.siteweb.tcs.common.ISerializableMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.data.util.Pair;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;



@Data
public class WindowLogQueue implements ISerializableMessage {
    private final int maxSize;
    private final LinkedList<WindowLogItemWrapper> queue;

    public WindowLogQueue() {
        this(100);
    }

    public WindowLogQueue(int maxSize) {
        this.maxSize = maxSize;
        this.queue = new LinkedList<>();
    }

    public Long getSize() {
        synchronized (queue) {
            return (long) queue.size();
        }
    }

    public void clear() {
        synchronized (queue) {
            queue.clear();
        }
    }

    public void enqueue(WindowLogItem windowLogItem) {
        synchronized (queue) {
            if (queue.size() >= maxSize) {
                queue.poll();
            }
            queue.offer(new WindowLogItemWrapper(windowLogItem));
        }
    }

    public String getSnapLog() {
        StringBuilder sb = new StringBuilder();
        synchronized (queue) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (WindowLogItemWrapper wrapper : queue) {
                sb.append(sdf.format(wrapper.getInsertTime())).append("  ")
                        .append(wrapper.getWindowLogItem().getWindowLogString())
                        .append("\n");
            }
        }
        return sb.toString();
    }

    public List<Pair<String, String>> getSnapLogList() {
        List<Pair<String, String>> result = new ArrayList<>();
        synchronized (queue) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (WindowLogItemWrapper wrapper : queue) {
                result.add(Pair.of(sdf.format(wrapper.getInsertTime()), wrapper.getWindowLogItem().getWindowLogString()));
            }
        }
        return result;
    }

    @Data
    @NoArgsConstructor
    public static class WindowLogItemWrapper implements ISerializableMessage{
        @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
        private WindowLogItem windowLogItem;
        private long insertTime;




        public WindowLogItemWrapper(WindowLogItem windowLogItem) {
            this.windowLogItem = windowLogItem;
            this.insertTime = System.currentTimeMillis();
        }

    }
}
