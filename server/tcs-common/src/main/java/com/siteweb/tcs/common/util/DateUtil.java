package com.siteweb.tcs.common.util;

import cn.hutool.core.date.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

public class DateUtil {
    private static final Logger log = LoggerFactory.getLogger(DateUtil.class);

    private DateUtil() {
    }

    public static final String DETAILPATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String PATTERN = "yyyy-MM-dd";
    private static final String HHMMSSPATTERN = " 00:00:00";

    private static final SimpleDateFormat simpleDateFormatDetail = new SimpleDateFormat(DETAILPATTERN);
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN);
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DETAILPATTERN);

    public static int differentSecondsByMillisecond(Date date1, Date date2) {
        return (int) ((date2.getTime() - date1.getTime()) / 1000);
    }

    public static String dateToString(Date time) {
        SimpleDateFormat formatter;
        formatter = new SimpleDateFormat(DETAILPATTERN);
        return formatter.format(time);
    }
    public static String dateToString(LocalDateTime time) {
        return time == null ? "" : dateTimeFormatter.format(time);
    }

    public static String dateToStringAndValidIsNull(Date time) {
        return Objects.isNull(time) ? "" : dateToString(time);
    }

    public static Date dateAddDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    public static Date dateAddMinutes(Date date, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minutes);
        return calendar.getTime();
    }

    public static Date dateAddSeconds(Date date, int seconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, seconds);
        return calendar.getTime();
    }

    public static Date dateAddHours(Date date, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        return calendar.getTime();
    }

    public static Date dateAddMonth(Date date, int months) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, months);
        return calendar.getTime();
    }

    //获取最后一秒
    public static Date getLastSecondsOfToday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        cal.set(Calendar.DATE, cal.get(Calendar.DATE));
        try {
            return simpleDateFormatDetail.parse(simpleDateFormat.format(cal.getTime()) + " 23:59:59");
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 将时间范围内的转换成每天一条String， 格式mm-dd
     */
    public static List<String> getDayDatesDuringTime(Date startTime, Date endTime) {
        List<String> dayDateList = new ArrayList<>();
        String startTimeString = dateToDayString(startTime).replace("-", "");
        String endTimeString = dateToDayString(endTime).replace("-", "");
        while (startTimeString.compareTo(endTimeString) <= 0) {
            dayDateList.add(startTimeString.replace("-", "").substring(4, startTimeString.length()));
            startTime = dateAddDays(startTime, 1);
            startTimeString = dateToDayString(startTime).replace("-", "");
        }
        return dayDateList;
    }

    public static String dateToDayString(Date time) {
        SimpleDateFormat formatter;
        formatter = new SimpleDateFormat(PATTERN);
        return formatter.format(time);
    }

    public static Date stringToSimpleDate(String date) {
        if (date == null || date.isEmpty()) return null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN);
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date stringToDate(String date) {
        if (date == null || date.isEmpty()) return null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DETAILPATTERN);
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }
    public static LocalDateTime stringToDate(String date, DateTimeFormatter formatter) {
        if (date == null || date.isEmpty()) return null;
        try {
            return LocalDateTime.parse(date, formatter);
        } catch (Exception e) {
            return null;
        }
    }

    public static long dateToLong(Date time) {
        return time.getTime();
    }

    /**
     * 获取两个时间段的时间差, 转换为%d小时%d分格式
     */
    public static String getTimeDifference(Date startDate, Date endDate) {
        if (endDate == null || startDate.getTime() >= endDate.getTime()) {
            return "";
        }
        LocaleMessageSourceUtil messageSourceUtil = SpringBeanUtil.getBean(LocaleMessageSourceUtil.class);
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime fromDateTime = LocalDateTime.ofInstant(startDate.toInstant(), zoneId);
        LocalDateTime toDateTime = LocalDateTime.ofInstant(endDate.toInstant(), zoneId);

        LocalDateTime tempDateTime = LocalDateTime.from(fromDateTime);

        long years = tempDateTime.until(toDateTime, ChronoUnit.YEARS);
        tempDateTime = tempDateTime.plusYears(years);

        long months = tempDateTime.until(toDateTime, ChronoUnit.MONTHS);
        tempDateTime = tempDateTime.plusMonths(months);

        long days = tempDateTime.until(toDateTime, ChronoUnit.DAYS);
        tempDateTime = tempDateTime.plusDays(days);

        long hours = tempDateTime.until(toDateTime, ChronoUnit.HOURS);
        tempDateTime = tempDateTime.plusHours(hours);

        long minutes = tempDateTime.until(toDateTime, ChronoUnit.MINUTES);

        String monthMsg = messageSourceUtil.getMessage("common.month");
        String dayMsg = messageSourceUtil.getMessage("common.day");
        String hourMsg = messageSourceUtil.getMessage("common.hour");
        String minuteMsg = messageSourceUtil.getMessage("common.minute");

        if (years != 0) {
            String yearMsg = messageSourceUtil.getMessage("common.year");
            return String.format("%d%s %d%s %d%s %d%s %d%s",
                    years, yearMsg,
                    months, monthMsg,
                    days, dayMsg,
                    hours, hourMsg,
                    minutes, minuteMsg);
        } else if (months != 0) {
            return String.format("%d%s %d%s %d%s %d%s",
                    months, monthMsg,
                    days, dayMsg,
                    hours, hourMsg,
                    minutes, minuteMsg);
        } else if (days != 0) {
            return String.format("%d%s %d%s %d%s",
                    days, dayMsg,
                    hours, hourMsg,
                    minutes, minuteMsg);
        } else {
            return String.format("%d%s %d%s",
                    hours, hourMsg,
                    minutes, minuteMsg);
        }
    }

    /**
     * 获取某月的第一天
     */
    public static Date getFirstDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DATE));
        try {
            return simpleDateFormatDetail.parse(simpleDateFormat.format(cal.getTime()) + HHMMSSPATTERN);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取某月的最后一天
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DATE));
        try {
            return simpleDateFormatDetail.parse(simpleDateFormat.format(cal.getTime()) + " 23:59:59");
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取下一个月的第一天
     */
    public static Date getNextMonthFirstDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        try {
            return simpleDateFormatDetail.parse(simpleDateFormat.format(cal.getTime()) + HHMMSSPATTERN);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 1 第一季度 2 第二季度 3 第三季度 4 第四季度
     *
     * @param date
     * @return
     */
    public static int getSeason(Date date) {
        int season = 0;
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int month = c.get(Calendar.MONTH);
        switch (month) {
            case Calendar.JANUARY, Calendar.FEBRUARY, Calendar.MARCH:
                season = 1;
                break;
            case Calendar.APRIL, Calendar.MAY, Calendar.JUNE:
                season = 2;
                break;
            case Calendar.JULY, Calendar.AUGUST, Calendar.SEPTEMBER:
                season = 3;
                break;
            case Calendar.OCTOBER, Calendar.NOVEMBER, Calendar.DECEMBER:
                season = 4;
                break;
            default:
                break;
        }
        return season;
    }

    /**
     * 获取当前时间所属季度
     *
     * @param startTime
     * @return eg: 2020年第1季度
     */
    public static String getCurrentQuarter(Date startTime) {
        String sDate = new SimpleDateFormat(PATTERN).format(startTime);
        return sDate.substring(0, 4) + "年第" + getSeason(startTime) + "季度";
    }

    /**
     * 获取下个季度第一天
     *
     * @param startTime 时间
     * @return
     */
    public static Date getNextQuarterFirstDay(Date startTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        int year = cal.get(Calendar.YEAR);
        Date nextQuarterFirstDay = null;
        try {
            switch (getSeason(startTime)) {
                case 1:
                    nextQuarterFirstDay = simpleDateFormatDetail.parse(year + "-04-01 00:00:00");
                    break;
                case 2:
                    nextQuarterFirstDay = simpleDateFormatDetail.parse(year + "-07-01 00:00:00");
                    break;
                case 3:
                    nextQuarterFirstDay = simpleDateFormatDetail.parse(year + "-10-01 00:00:00");
                    break;
                case 4:
                    nextQuarterFirstDay = simpleDateFormatDetail.parse(++year + "-01-01 00:00:00");
                    break;
                default:
                    break;
            }
        } catch (ParseException e) {
            log.error("getNextQuarterFirstDay 解析时间异常：", e);
        }
        return nextQuarterFirstDay;
    }

    /**
     * 获取当前所属年
     *
     * @param startTime
     * @return
     */
    public static Integer getCurrentYear(Date startTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        return cal.get(Calendar.YEAR);
    }

    /**
     * 获取下一年第一天
     *
     * @param startTime
     * @return
     */
    public static Date getNextYearFirstDay(Date startTime) {
        Integer currentYear = getCurrentYear(startTime);
        try {
            return simpleDateFormatDetail.parse(++currentYear + "-01-01 00:00:00");
        } catch (ParseException e) {
            log.error("getNextYearFirstDay 解析时间异常：", e);
            return null;
        }
    }

    /**
     * 月开始时间
     *
     * @param date
     * @return
     */
    public static Date getMonthStartTime(Date date) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.set(Calendar.DAY_OF_MONTH, 1);
        ca.set(Calendar.HOUR_OF_DAY, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        ca.set(Calendar.MILLISECOND, 0);

        return ca.getTime();
    }

    /**
     * 取得月最后一秒
     *
     * @param date
     * @return
     */
    public static Date getMonthEndTime(Date date) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.set(Calendar.DAY_OF_MONTH, 1);
        ca.set(Calendar.HOUR_OF_DAY, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        ca.set(Calendar.MILLISECOND, 0);

        ca.add(Calendar.MONTH, 1);
        ca.add(Calendar.SECOND, -1);
        return ca.getTime();
    }

    /**
     * 昨天开始时间.结束时间
     *
     * @param isStart
     * @return
     */
    public static Date getYesterdayStartOrEndTime(boolean isStart) {
        Calendar ca = getTodayStartTime();
        if (isStart) {
            ca.add(Calendar.DATE, -1);
        } else {
            ca.add(Calendar.SECOND, -1);
        }
        return ca.getTime();
    }

    public static Calendar getTodayStartTime() {
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.HOUR_OF_DAY, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        ca.set(Calendar.MILLISECOND, 0);
        return ca;
    }

    /**
     * 上一小时开始时间.结束时间
     *
     * @param isStart
     * @return
     */
    public static Date getLastHourStartOrEndTime(boolean isStart) {
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        ca.set(Calendar.MILLISECOND, 0);
        if (isStart) {
            ca.add(Calendar.HOUR_OF_DAY, -1);
        } else {
            ca.add(Calendar.SECOND, -1);
        }
        return ca.getTime();
    }


    /**
     * local时间转换成UTC时间
     *
     * @param localDate
     * @return
     */
    public static Date localToUTC(Date localDate) {
        long localTimeInMillis = localDate.getTime();
        /** long时间转换成Calendar */
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(localTimeInMillis);
        /** 取得时间偏移量 */
        int zoneOffset = calendar.get(java.util.Calendar.ZONE_OFFSET);
        /** 从本地时间里扣除这些差量，即可以取得UTC时间*/
        calendar.add(java.util.Calendar.MILLISECOND, +(zoneOffset));
        /** 取得的时间就是UTC标准时间 */
        return new Date(calendar.getTimeInMillis());
    }

    /**
     * 获取时间段内的月份
     *
     * @param minDate
     * @param maxDate
     * @return
     */
    public static List<String> getMonthBetween(Date minDate, Date maxDate, String dateFormat) {
        ArrayList<String> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);//格式化为年月
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(minDate);
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        max.setTime(maxDate);
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }

    /**
     * 取得月第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDateOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMinimum(Calendar.DAY_OF_MONTH));
        return c.getTime();
    }

    /**
     * date 转指定格式字符串
     *
     * @param time
     */
    public static String dateToStringByPattern(Date time, String pattern) {
        SimpleDateFormat formatter;
        formatter = new SimpleDateFormat(pattern);
        return formatter.format(time);
    }

    /**
     * 取得月最后一天
     *
     * @param date
     * @return
     */
    public static Date getLastDateOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return c.getTime();
    }

    /**
     * 判断是否是同一年
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameYear(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
    }

    /**
     * 判断是否是同一个月
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameMonth(Date date1, Date date2) {
        if (!isSameYear(date1, date2)) {
            return false;
        }
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
    }

    /**
     * 校验是否是日期格式
     * 假设传入的日期格式是yyyy-MM-dd, 也可以传入yyyy-MM-dd  HH:mm:ss，如2018-1-1或者2018-01-01格式
     *
     * @param strDate
     * @return
     */
    public static boolean isValidDate(String strDate) {
        SimpleDateFormat format = new SimpleDateFormat(PATTERN);
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2018-02-29会被接受，并转换成2018-03-01
            format.setLenient(false);

            //判断传入的yyyy年-MM月-dd日 字符串是否为数字
            String[] sArray = strDate.split("-");
            for (String s : sArray) {
                boolean isNum = s.matches("\\d+");
                // +表示1个或多个（如"3"或"225"），*表示0个或多个（[0-9]*）（如""或"1"或"22"），?表示0个或1个([0-9]?)(如""或"7")
                if (!isNum) {
                    return false;
                }
            }
        } catch (Exception e) {
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            return false;
        }

        return true;
    }

    public static Date dateFirstTimeFormat(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(PATTERN + HHMMSSPATTERN);
        String sDate = formatter.format(date);
        try {
            return formatter.parse(sDate);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 判断该日期是否为今日
     *
     * @param date
     * @return
     */
    public static boolean isToday(Date date) {
        return dateToDayString(new Date()).equals(dateToDayString(date));
    }

    /**
     * 获取之前每个月的第一天
     *
     * @param date     当前日期
     * @param monthNum 之前的几个月
     * @return {@link List}<{@link Date}>
     */
    public static List<Date> getEveryMonthOfFirstDay(Date date, Integer monthNum) {
        List<Date> list = new ArrayList<>(monthNum);
        for (int i = 0; i < monthNum; i++) {
            DateTime dateTime = cn.hutool.core.date.DateUtil.offsetMonth(date, -i);
            Date firstDayOfMonth = getFirstDayOfMonth(dateTime);
            list.add(firstDayOfMonth);
        }
        return list;
    }

    /**
     * 按照指定小时分割时间段
     *
     * @param dateType  类型 M/D/H/N -->每月/每天/每小时/每分钟
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param time      指定小时(如：1、2、3、4)
     * @return
     */
    public static List<String> getDateRange(String dateType, Date startTime, Date endTime, int time) {
        List<String> listDate = new ArrayList<>();
        listDate.add(new SimpleDateFormat(DETAILPATTERN).format(startTime));
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(startTime);
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(endTime);
        while (calEnd.after(calBegin)) {
            if ("H".equals(dateType)) {
                calBegin.add(Calendar.HOUR, time);
            }
            if ("M".equals(dateType)) {
                calBegin.add(Calendar.MONTH, time);
            }
            if ("D".equals(dateType)) {
                calBegin.add(Calendar.DATE, time);
            }
            if ("N".equals(dateType)) {
                calBegin.add(Calendar.MINUTE, time);
            }
            if (calEnd.after(calBegin)) {
                listDate.add(new SimpleDateFormat(DETAILPATTERN).format(calBegin.getTime()));
            } else {
                //
            }

        }
        return listDate;
    }
}
