package com.siteweb.tcs.common.util.scripts;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorLong;
import com.googlecode.aviator.runtime.type.AviatorObject;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR> (2025-02-20)
 **/
public class SizeFunction extends AbstractFunction {
    @Override
    public String getName() {
        return "SIZE$";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        var param = arg1.getValue(env);
        if (param == null) return AviatorLong.valueOf(0);
        if (param instanceof String str) {
            return AviatorLong.valueOf(str.length());
        }
        if (param instanceof Object[] array) {
            return AviatorLong.valueOf(array.length);
        }
        if (param instanceof Collection<?> list) {
            return AviatorLong.valueOf(list.size());
        }
        if (param instanceof Map<?, ?> map) {
            return AviatorLong.valueOf(map.size());
        }
        return AviatorLong.valueOf(0);
    }
}
