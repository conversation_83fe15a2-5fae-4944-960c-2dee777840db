# CTCC 插件开发计划

基于CMCC插件参考开发中国电信动环监控系统B接口CTCC插件的详细计划清单

## 项目概述

- **项目名称**: TCS-SOUTH-CTCC 插件开发
- **基础参考**: TCS-SOUTH-CMCC 插件
- **参考规范**: 中国电信动环监控系统B接口技术规范20230515
- **开发周期**: 预计 6-8 周
- **复用程度**: 约 80% 架构和框架可复用

---

## 任务清单

### 阶段一：规范分析与差异对比 (1周)

#### 1.1 分析CTCC B接口技术规范与CMCC规范的差异
- [ ] 详细对比报文类型差异（CTCC有27种报文类型 vs CMCC的20+种）
- [ ] 分析数据结构定义差异
  - CTCC: SUID vs CMCC: FSUID
  - CTCC: 添加了SUPort、SUVendor、SUModel等字段
  - CTCC: 增加了智能门禁相关报文（可选）
- [ ] 对比业务流程差异
  - CTCC增加了SUREADY验证流程
  - CTCC有标准化配置文件和厂家配置文件区分
  - CTCC增加了监控点配置模板选型功能
- [ ] 分析XML格式和字段命名差异
- [ ] 整理常量定义和枚举值差异
- [ ] 生成差异分析报告

#### 1.2 制定详细技术实施方案
- [ ] 确定架构复用策略
- [ ] 设计组件映射关系
- [ ] 制定代码重构计划
- [ ] 设计测试验证方案

---

### 阶段二：基础架构搭建 (1周)

#### 2.1 搭建CTCC插件基础架构（复制CMCC项目结构）
- [ ] 创建项目目录 `server/tcs-south-ctcc`
- [ ] 复制并调整Maven配置文件 `pom.xml`
- [ ] 修改插件ID为 `south-ctcc-plugin`
- [ ] 更新Spring Boot配置文件
- [ ] 调整包名从 `com.siteweb.tcs.cmcc` 到 `com.siteweb.tcs.ctcc`
- [ ] 复制基础项目文件（.gitignore, README.md等）
- [ ] 验证项目结构可以正常启动

#### 2.2 设置开发环境
- [ ] 配置数据库连接（开发/测试环境）
- [ ] 设置IDE项目配置
- [ ] 配置本地调试环境
- [ ] 准备测试数据

---

### 阶段三：协议定义层重构 (2-3周)

#### 3.1 重新定义CTCC协议枚举类型和常量
- [ ] 创建 `CtccPK_TypeName.java` 枚举类
  ```java
  LOGIN(101), LOGIN_ACK(102),
  SUREADY(103), SUREADY_ACK(104),
  SET_SCIP(105), SET_SCIP_ACK(106),
  // ... 27种报文类型
  ```
- [ ] 重写 `CtccEnumResult.java`、`CtccEnumState.java` 等枚举
- [ ] 定义CTCC常量类 `CtccConstants.java`
  ```java
  SUID_LEN = 20, USER_LENGTH = 20,
  IP_LENGTH = 15, VENDOR_LENGTH = 40,
  MODEL_LENGTH = 40, VER_LEN = 20
  ```

#### 3.2 实现CTCC数据结构定义
- [ ] 重写 `CtccTTime.java` 时间结构
- [ ] 实现 `CtccTAlarm.java` 告警消息结构
- [ ] 创建 `CtccTSemaphore.java` 信号量值结构  
- [ ] 实现 `CtccTThreshold.java` 门限值结构
- [ ] 创建 `CtccTSUStatus.java` SU状态参数结构
- [ ] 实现 `CtccTDevConf.java` 监控对象配置信息
- [ ] 添加智能门禁相关数据结构（可选）

#### 3.3 实现CTCC报文消息类定义
- [ ] **网络连接参数类报文** (6个)
  - `CtccLoginMessage.java` & `CtccLoginAckMessage.java`
  - `CtccSuReadyMessage.java` & `CtccSuReadyAckMessage.java`  
  - `CtccSetScIpMessage.java` & `CtccSetScIpAckMessage.java`
- [ ] **FTP参数配置报文** (4个)
  - `CtccGetSuFtpMessage.java` & `CtccGetSuFtpAckMessage.java`
  - `CtccSetSuFtpMessage.java` & `CtccSetSuFtpAckMessage.java`
- [ ] **配置文件管理报文** (12个)
  - 标准化配置文件: ASK_SCHEMECONFIG, GET_SCHEMECONFIG, SET_SCHEMECONFIG
  - 厂家配置文件: ASK_FACTORYCONFIG, SEND_FACTORYCONFIG, GET_FACTORYCONFIG, SET_FACTORYCONFIG
  - 监控点配置: GET_SPCONFIGOPTION, SET_SPCONFIGOPTION
- [ ] **数据采集报文** (4个)
  - `CtccGetDataMessage.java` & `CtccGetDataAckMessage.java`
  - `CtccAskTodayHisDataMessage.java` & `CtccAskTodayHisDataAckMessage.java`
- [ ] **告警处理报文** (4个)
  - `CtccSendAlarmMessage.java` & `CtccSendAlarmAckMessage.java`
  - `CtccGetActiveAlarmMessage.java` & `CtccGetActiveAlarmAckMessage.java`
- [ ] **控制命令报文** (2个)
  - `CtccSetRmCtrlCmdMessage.java` & `CtccSetRmCtrlCmdAckMessage.java`
- [ ] **系统辅助命令报文** (6个)
  - 时间同步: SET_TIME, SET_TIME_ACK
  - 状态查询: GET_SUINFO, GET_SUINFO_ACK  
  - 重启命令: SU_REBOOT, SU_REBOOT_ACK
- [ ] **智能门禁报文** (8个，可选)
  - GET_SMARTDOOR, SET_SMARTDOOR, SEND_SMARTDOOR, SEND_DOOREVENT等

#### 3.4 配置XML序列化支持
- [ ] 为所有消息类添加Jackson XML注解
- [ ] 实现XML格式验证
- [ ] 处理字符编码问题
- [ ] 测试序列化/反序列化功能

---

### 阶段四：消息处理逻辑重构 (1-2周)

#### 4.1 重写消息处理器（CtccFsuMessageProcessor）
- [ ] 创建 `CtccFsuMessageProcessor.java` 核心处理类
- [ ] 实现ACK响应处理方法
  ```java
  handleLoginAck(), handleSuReadyAck(), 
  handleSetScIpAck(), handleGetDataAck()
  ```
- [ ] 实现用户命令处理方法
  ```java
  processUserCommand(CtccSouthUserCommand command)
  ```
- [ ] 实现HTTP请求处理方法
  ```java
  sendHttpRequest(String xmlContent, String url)
  ```
- [ ] 添加CTCC特有的配置文件处理逻辑

#### 4.2 调整消息处理逻辑
- [ ] 适配CTCC的超时时间和重试机制
- [ ] 修改错误处理机制，适配CTCC错误码
- [ ] 实现CTCC特有的验证流程（SUREADY）
- [ ] 处理配置文件上传下载逻辑

#### 4.3 实现FTP文件处理功能
- [ ] 创建 `CtccFTPHelper.java` 工具类
- [ ] 实现标准化配置文件下载/上传
- [ ] 实现厂家配置文件管理
- [ ] 实现监控图像文件获取
- [ ] 实现历史数据文件处理

---

### 阶段五：数据模型和数据库层 (1-2周)

#### 5.1 设计CTCC数据库表结构和实体类
- [ ] 设计主要实体表
  ```sql
  ctcc_su          -- SU设备表（对应cmcc_fsu）
  ctcc_device      -- 监控设备表  
  ctcc_signal      -- 监控点表
  ctcc_alarm       -- 告警表
  ctcc_event       -- 事件表
  ```
- [ ] 创建实体类
  ```java
  CtccSu.java, CtccDevice.java, 
  CtccSignal.java, CtccAlarm.java
  ```
- [ ] 设计字典表
  ```sql
  ctcc_alarm_dic   -- 告警字典
  ctcc_signal_dic  -- 信号字典  
  ctcc_device_type -- 设备类型字典
  ```

#### 5.2 数据库访问层实现
- [ ] 创建MyBatis Mapper接口
  ```java
  CtccSuMapper, CtccDeviceMapper,
  CtccSignalMapper, CtccAlarmMapper
  ```
- [ ] 编写Mapper XML文件
- [ ] 创建数据库迁移脚本 `V1__Create_ctcc_tables.sql`
- [ ] 准备初始化数据脚本

#### 5.3 数据字典和标准化配置
- [ ] 准备CTCC标准化信号字典数据
- [ ] 创建设备类型和子类型配置
- [ ] 准备告警级别和状态字典
- [ ] 配置业务规则和约束

---

### 阶段六：业务服务层实现 (1周)

#### 6.1 实现CTCC业务服务层
- [ ] 创建 `CtccSuService.java` SU设备管理服务
- [ ] 实现 `CtccDeviceService.java` 设备管理服务
- [ ] 创建 `CtccAlarmService.java` 告警管理服务
- [ ] 实现 `CtccDataService.java` 数据采集服务
- [ ] 创建 `CtccConfigService.java` 配置管理服务

#### 6.2 实现定时任务和后台处理
- [ ] 实现心跳检测机制
- [ ] 创建数据采集定时任务
- [ ] 实现告警处理后台任务
- [ ] 配置文件同步任务

#### 6.3 集成Stream引擎
- [ ] 适配Stream计算引擎
- [ ] 配置数据流处理
- [ ] 实现实时数据推送

---

### 阶段七：前端界面适配 (1周)

#### 7.1 调整前端API接口
- [ ] 复制CMCC前端项目到 `server/tcs-south-ctcc/src/main/web`
- [ ] 修改API接口路径，从 `/cmcc/` 改为 `/ctcc/`
- [ ] 调整数据字段映射
- [ ] 更新TypeScript类型定义

#### 7.2 修改前端业务界面
- [ ] 调整SU设备管理界面（FSUID改为SUID）
- [ ] 修改设备配置界面
- [ ] 调整告警管理界面
- [ ] 修改监控点配置界面
- [ ] 添加CTCC特有功能界面（配置文件管理等）

#### 7.3 更新前端配置
- [ ] 修改菜单配置
- [ ] 调整路由配置
- [ ] 更新国际化配置（如需要）
- [ ] 测试前后端集成

---

### 阶段八：测试和集成 (1-2周)

#### 8.1 单元测试
- [ ] 编写消息序列化/反序列化测试
- [ ] 测试业务逻辑单元
- [ ] 数据库访问层测试
- [ ] 服务层功能测试

#### 8.2 集成测试
- [ ] 协议兼容性测试
- [ ] 端到端业务流程测试
- [ ] 前后端集成测试  
- [ ] 多设备并发测试

#### 8.3 性能和稳定性测试
- [ ] 大量数据处理测试
- [ ] 长时间稳定性测试
- [ ] 内存泄漏检测
- [ ] 数据库性能优化

---

### 阶段九：文档和部署 (1周)

#### 9.1 生成开发文档和部署指南
- [ ] 编写插件架构设计文档
- [ ] 创建API接口文档
- [ ] 编写部署和配置指南
- [ ] 制作用户使用手册

#### 9.2 准备发布版本
- [ ] 构建生产版本
- [ ] 准备安装包
- [ ] 测试部署流程
- [ ] 版本发布准备

---

## 关键风险评估

### 技术风险
- **协议差异风险**: CTCC协议与CMCC存在较大差异，需要大量重写
- **XML格式兼容性**: Jackson XML序列化可能需要特殊配置
- **字符编码问题**: 不同运营商可能使用不同编码格式
- **时间格式差异**: 时间字符串格式可能需要调整

### 业务风险
- **设备类型差异**: CTCC设备分类和编码规则与CMCC不同
- **告警级别差异**: 告警等级定义需要重新适配
- **监控点定义差异**: 信号点ID规则和字典需要更新
- **配置文件格式**: 标准化配置文件格式可能完全不同

### 时间风险
- **规范理解**: 深入理解CTCC规范可能需要更多时间
- **测试验证**: 缺乏CTCC标准设备可能影响测试进度
- **集成调试**: 多个组件集成可能遇到意外问题

---

## 资源配置

### 人员需求
- **后端开发**: 2名（架构设计、协议实现、业务逻辑）
- **前端开发**: 1名（界面适配、API集成）
- **测试工程师**: 1名（测试用例、验证测试）

### 时间安排
- **总工期**: 6-8周
- **关键里程碑**:
  - Week 2: 基础架构搭建完成
  - Week 4: 协议层实现完成
  - Week 6: 业务层和前端完成
  - Week 8: 测试和文档完成

### 技术栈
- **后端**: Spring Boot + Pekko Actor + MyBatis Plus
- **前端**: Vue 3 + TypeScript + Element Plus
- **数据库**: MySQL/PostgreSQL
- **构建工具**: Maven + Vite

---

## 成功指标

### 功能完整性
- [ ] 实现CTCC规范定义的所有27种报文类型
- [ ] 支持完整的SU注册和认证流程
- [ ] 实现数据采集、告警处理、远程控制功能
- [ ] 支持配置文件管理和监控点配置

### 性能指标
- [ ] 支持1000+设备并发接入
- [ ] 告警响应时间<1秒
- [ ] 数据采集周期可配置(1分钟-1小时)
- [ ] 系统稳定运行72小时无故障

### 可维护性
- [ ] 代码复用率达到80%
- [ ] 完整的单元测试覆盖率>70%
- [ ] 完善的文档和部署指南
- [ ] 清晰的架构设计和组件划分

通过这个详细的开发计划，可以系统性地完成从CMCC插件到CTCC插件的开发工作，预计能够节约约80%的开发工作量，确保项目按时高质量完成。
